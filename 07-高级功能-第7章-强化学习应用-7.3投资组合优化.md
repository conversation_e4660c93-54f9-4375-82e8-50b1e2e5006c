# 第7.3节：投资组合优化

## 学习目标

通过本节学习，您将能够：
- 理解强化学习在投资组合优化中的应用
- 掌握动态资产配置的实现方法
- 学会风险预算管理和多目标优化
- 掌握实时决策系统的构建方法

## 7.3.1 动态资产配置

### 强化学习资产配置

```python
class RLPortfolioOptimizer:
    """强化学习投资组合优化器"""
    
    def __init__(self, asset_count, state_dim, action_dim):
        self.asset_count = asset_count
        self.state_dim = state_dim
        self.action_dim = action_dim
        
        # 强化学习模型
        self.agent = PPOAgent(state_dim, action_dim)
        
        # 投资组合状态
        self.portfolio_weights = np.ones(asset_count) / asset_count
        self.portfolio_value = 1.0
    
    def get_portfolio_state(self, market_data, current_time):
        """获取投资组合状态"""
        # 市场状态
        current_prices = market_data.iloc[current_time][['close'] * self.asset_count].values
        price_returns = self.calculate_returns(market_data, current_time)
        
        # 投资组合状态
        portfolio_return = np.sum(self.portfolio_weights * price_returns)
        portfolio_volatility = self.calculate_portfolio_volatility(price_returns)
        
        # 构建状态向量
        state = np.concatenate([
            current_prices,
            price_returns,
            [portfolio_return],
            [portfolio_volatility],
            self.portfolio_weights
        ])
        
        return state
    
    def calculate_returns(self, market_data, current_time):
        """计算收益率"""
        if current_time > 0:
            current_prices = market_data.iloc[current_time][['close'] * self.asset_count].values
            previous_prices = market_data.iloc[current_time-1][['close'] * self.asset_count].values
            
            returns = (current_prices - previous_prices) / previous_prices
        else:
            returns = np.zeros(self.asset_count)
        
        return returns
    
    def calculate_portfolio_volatility(self, returns):
        """计算投资组合波动率"""
        # 简化的波动率计算
        portfolio_return = np.sum(self.portfolio_weights * returns)
        volatility = abs(portfolio_return)  # 简化的波动率
        
        return volatility
    
    def update_portfolio_weights(self, action):
        """更新投资组合权重"""
        # 将动作转换为权重
        # 动作范围：[-1, 1] 表示权重调整
        weight_adjustments = action * 0.1  # 最大调整10%
        
        # 更新权重
        new_weights = self.portfolio_weights + weight_adjustments
        
        # 确保权重和为1且非负
        new_weights = np.clip(new_weights, 0, 1)
        new_weights = new_weights / np.sum(new_weights)
        
        self.portfolio_weights = new_weights
    
    def calculate_reward(self, market_data, current_time):
        """计算奖励"""
        # 计算投资组合收益率
        returns = self.calculate_returns(market_data, current_time)
        portfolio_return = np.sum(self.portfolio_weights * returns)
        
        # 计算基准收益率（等权重）
        benchmark_return = np.mean(returns)
        
        # 奖励 = 超额收益
        reward = portfolio_return - benchmark_return
        
        return reward
    
    def optimize_portfolio(self, market_data, training_episodes=1000):
        """优化投资组合"""
        for episode in range(training_episodes):
            # 重置环境
            self.portfolio_weights = np.ones(self.asset_count) / self.asset_count
            self.portfolio_value = 1.0
            
            for time_step in range(len(market_data) - 1):
                # 获取当前状态
                state = self.get_portfolio_state(market_data, time_step)
                
                # 获取动作
                action, _ = self.agent.get_action(state)
                
                # 更新投资组合权重
                self.update_portfolio_weights(action)
                
                # 计算奖励
                reward = self.calculate_reward(market_data, time_step + 1)
                
                # 获取下一状态
                next_state = self.get_portfolio_state(market_data, time_step + 1)
                
                # 存储经验
                self.agent.store_transition(state, action, reward, next_state, False)
                
                # 更新网络
                if time_step % 64 == 0:
                    self.agent.update()
            
            # 每100个episode打印一次进度
            if episode % 100 == 0:
                print(f"Episode {episode}/{training_episodes}")
```

### 自适应资产配置

```python
class AdaptiveAssetAllocation:
    """自适应资产配置"""
    
    def __init__(self, asset_count, risk_free_rate=0.02, target_volatility=0.15):
        self.asset_count = asset_count
        self.risk_free_rate = risk_free_rate
        self.target_volatility = target_volatility
        self.portfolio_weights = np.ones(asset_count) / asset_count
        self.market_regime = 'normal'  # 'normal', 'volatile', 'trending'
    
    def detect_market_regime(self, market_data, current_time, window=60):
        """检测市场状态"""
        if current_time < window:
            return 'normal'
        
        # 计算历史收益率
        recent_returns = market_data['close'].iloc[current_time-window:current_time].pct_change().dropna()
        
        # 计算波动率
        volatility = recent_returns.std() * np.sqrt(252)
        
        # 计算趋势强度
        trend_strength = abs(recent_returns.mean()) / recent_returns.std() if recent_returns.std() > 0 else 0
        
        # 判断市场状态
        if volatility > 0.25:  # 高波动
            self.market_regime = 'volatile'
        elif trend_strength > 0.1:  # 强趋势
            self.market_regime = 'trending'
        else:
            self.market_regime = 'normal'
        
        return self.market_regime
    
    def adjust_allocation_strategy(self, market_regime):
        """根据市场状态调整配置策略"""
        if market_regime == 'volatile':
            # 高波动时增加防御性资产
            defensive_ratio = 0.6
            aggressive_ratio = 0.4
        elif market_regime == 'trending':
            # 强趋势时增加进攻性资产
            defensive_ratio = 0.3
            aggressive_ratio = 0.7
        else:
            # 正常状态
            defensive_ratio = 0.5
            aggressive_ratio = 0.5
        
        return defensive_ratio, aggressive_ratio
    
    def optimize_allocation(self, expected_returns, covariance_matrix, market_regime):
        """优化资产配置"""
        # 获取配置策略
        defensive_ratio, aggressive_ratio = self.adjust_allocation_strategy(market_regime)
        
        # 分离防御性和进攻性资产
        # 这里简化处理，实际应该根据资产特征分类
        defensive_assets = list(range(self.asset_count // 2))
        aggressive_assets = list(range(self.asset_count // 2, self.asset_count))
        
        # 分别优化
        defensive_weights = self.optimize_defensive_allocation(
            expected_returns[defensive_assets], 
            covariance_matrix[np.ix_(defensive_assets, defensive_assets)]
        )
        
        aggressive_weights = self.optimize_aggressive_allocation(
            expected_returns[aggressive_assets], 
            covariance_matrix[np.ix_(aggressive_assets, aggressive_assets)]
        )
        
        # 组合权重
        final_weights = np.zeros(self.asset_count)
        final_weights[defensive_assets] = defensive_weights * defensive_ratio
        final_weights[aggressive_assets] = aggressive_weights * aggressive_ratio
        
        return final_weights
    
    def optimize_defensive_allocation(self, expected_returns, covariance_matrix):
        """优化防御性资产配置"""
        from scipy.optimize import minimize
        
        def objective(weights):
            portfolio_return = np.sum(weights * expected_returns)
            portfolio_risk = np.sqrt(weights.T @ covariance_matrix @ weights)
            
            # 防御性配置：最小化风险
            return portfolio_risk
        
        # 约束条件
        constraints = [
            {'type': 'eq', 'fun': lambda x: np.sum(x) - 1},  # 权重和为1
        ]
        
        bounds = [(0, 1) for _ in range(len(expected_returns))]
        
        # 初始权重
        initial_weights = np.ones(len(expected_returns)) / len(expected_returns)
        
        # 优化
        result = minimize(objective, initial_weights, method='SLSQP', 
                        bounds=bounds, constraints=constraints)
        
        return result.x
    
    def optimize_aggressive_allocation(self, expected_returns, covariance_matrix):
        """优化进攻性资产配置"""
        from scipy.optimize import minimize
        
        def objective(weights):
            portfolio_return = np.sum(weights * expected_returns)
            portfolio_risk = np.sqrt(weights.T @ covariance_matrix @ weights)
            
            # 进攻性配置：最大化夏普比率
            sharpe_ratio = (portfolio_return - self.risk_free_rate) / portfolio_risk
            return -sharpe_ratio  # 最小化负夏普比率
        
        # 约束条件
        constraints = [
            {'type': 'eq', 'fun': lambda x: np.sum(x) - 1},  # 权重和为1
        ]
        
        bounds = [(0, 1) for _ in range(len(expected_returns))]
        
        # 初始权重
        initial_weights = np.ones(len(expected_returns)) / len(expected_returns)
        
        # 优化
        result = minimize(objective, initial_weights, method='SLSQP', 
                        bounds=bounds, constraints=constraints)
        
        return result.x
```

## 7.3.2 风险预算管理

### 风险预算策略

```python
class RiskBudgetOptimizer:
    """风险预算优化器"""
    
    def __init__(self, asset_count, target_volatility=0.15):
        self.asset_count = asset_count
        self.target_volatility = target_volatility
        self.risk_budgets = np.ones(asset_count) / asset_count
    
    def calculate_risk_contributions(self, weights, covariance_matrix):
        """计算风险贡献"""
        portfolio_risk = np.sqrt(weights.T @ covariance_matrix @ weights)
        
        # 边际风险贡献
        marginal_risk = covariance_matrix @ weights / portfolio_risk
        
        # 风险贡献
        risk_contributions = weights * marginal_risk
        
        return risk_contributions
    
    def optimize_risk_parity(self, covariance_matrix):
        """优化风险平价"""
        from scipy.optimize import minimize
        
        def objective(weights):
            risk_contributions = self.calculate_risk_contributions(weights, covariance_matrix)
            target_contribution = self.target_volatility / self.asset_count
            
            # 风险平价误差
            risk_parity_error = np.sum((risk_contributions - target_contribution) ** 2)
            
            return risk_parity_error
        
        # 约束条件
        constraints = [
            {'type': 'eq', 'fun': lambda x: np.sum(x) - 1},  # 权重和为1
        ]
        
        bounds = [(0, 1) for _ in range(self.asset_count)]
        
        # 初始权重
        initial_weights = np.ones(self.asset_count) / self.asset_count
        
        # 优化
        result = minimize(objective, initial_weights, method='SLSQP', 
                        bounds=bounds, constraints=constraints)
        
        return result.x
    
    def optimize_risk_budget(self, covariance_matrix, risk_budgets):
        """优化风险预算"""
        from scipy.optimize import minimize
        
        def objective(weights):
            risk_contributions = self.calculate_risk_contributions(weights, covariance_matrix)
            
            # 风险预算误差
            budget_error = np.sum((risk_contributions - risk_budgets * self.target_volatility) ** 2)
            
            return budget_error
        
        # 约束条件
        constraints = [
            {'type': 'eq', 'fun': lambda x: np.sum(x) - 1},  # 权重和为1
        ]
        
        bounds = [(0, 1) for _ in range(self.asset_count)]
        
        # 初始权重
        initial_weights = np.ones(self.asset_count) / self.asset_count
        
        # 优化
        result = minimize(objective, initial_weights, method='SLSQP', 
                        bounds=bounds, constraints=constraints)
        
        return result.x

class DynamicRiskBudget:
    """动态风险预算"""
    
    def __init__(self, asset_count, base_risk_budgets):
        self.asset_count = asset_count
        self.base_risk_budgets = base_risk_budgets
        self.current_risk_budgets = base_risk_budgets.copy()
        self.performance_history = []
    
    def update_risk_budgets(self, asset_performance):
        """根据资产表现更新风险预算"""
        # 计算相对表现
        avg_performance = np.mean(asset_performance)
        relative_performance = asset_performance / avg_performance
        
        # 调整风险预算
        # 表现好的资产增加风险预算，表现差的减少
        adjustment_factor = 0.1  # 调整幅度
        performance_adjustment = (relative_performance - 1) * adjustment_factor
        
        # 更新风险预算
        new_risk_budgets = self.current_risk_budgets * (1 + performance_adjustment)
        
        # 确保风险预算非负且和为1
        new_risk_budgets = np.clip(new_risk_budgets, 0.01, 0.5)
        new_risk_budgets = new_risk_budgets / np.sum(new_risk_budgets)
        
        self.current_risk_budgets = new_risk_budgets
        
        return new_risk_budgets
    
    def calculate_asset_performance(self, returns, window=252):
        """计算资产表现"""
        if len(returns) < window:
            window = len(returns)
        
        recent_returns = returns.iloc[-window:]
        
        # 计算夏普比率
        mean_return = recent_returns.mean() * 252
        std_return = recent_returns.std() * np.sqrt(252)
        
        if std_return > 0:
            sharpe_ratio = mean_return / std_return
        else:
            sharpe_ratio = 0
        
        return sharpe_ratio
    
    def optimize_with_dynamic_budget(self, covariance_matrix, asset_performance):
        """使用动态风险预算优化"""
        # 更新风险预算
        updated_risk_budgets = self.update_risk_budgets(asset_performance)
        
        # 使用更新后的风险预算进行优化
        optimizer = RiskBudgetOptimizer(self.asset_count)
        optimal_weights = optimizer.optimize_risk_budget(covariance_matrix, updated_risk_budgets)
        
        return optimal_weights, updated_risk_budgets
```

### 风险平价策略

```python
class RiskParityStrategy:
    """风险平价策略"""
    
    def __init__(self, target_volatility=0.10, rebalance_frequency=30):
        self.target_volatility = target_volatility
        self.rebalance_frequency = rebalance_frequency
        self.last_rebalance = None
    
    def calculate_risk_contributions(self, returns_data):
        """计算风险贡献"""
        # 计算协方差矩阵
        cov_matrix = returns_data.cov()
        
        # 计算各资产的风险贡献
        n_assets = len(returns_data.columns)
        risk_contributions = {}
        
        for i, asset in enumerate(returns_data.columns):
            # 计算边际风险贡献
            marginal_risk = cov_matrix.iloc[i, :].values
            
            # 风险贡献 = 权重 * 边际风险贡献
            risk_contributions[asset] = marginal_risk
        
        return risk_contributions
    
    def optimize_risk_parity(self, returns_data):
        """优化风险平价权重"""
        from scipy.optimize import minimize
        
        def objective(weights):
            # 计算组合风险
            cov_matrix = returns_data.cov()
            portfolio_risk = np.sqrt(np.dot(weights.T, np.dot(cov_matrix, weights)))
            
            # 计算各资产的风险贡献
            risk_contributions = np.dot(cov_matrix, weights) * weights / portfolio_risk
            
            # 风险平价目标：所有资产的风险贡献相等
            target_contribution = portfolio_risk / len(weights)
            risk_parity_error = np.sum((risk_contributions - target_contribution) ** 2)
            
            return risk_parity_error
        
        # 约束条件
        constraints = [
            {'type': 'eq', 'fun': lambda x: np.sum(x) - 1},  # 权重和为1
        ]
        
        bounds = [(0, 1) for _ in range(len(returns_data.columns))]
        
        # 初始权重
        initial_weights = np.array([1/len(returns_data.columns)] * len(returns_data.columns))
        
        # 优化
        result = minimize(objective, initial_weights, method='SLSQP', 
                        bounds=bounds, constraints=constraints)
        
        return dict(zip(returns_data.columns, result.x))
    
    def generate_trade_decision(self, returns_data):
        """生成交易决策"""
        # 检查是否需要再平衡
        if self.should_rebalance():
            # 计算风险平价权重
            weights = self.optimize_risk_parity(returns_data)
            self.last_rebalance = pd.Timestamp.now()
            return weights
        else:
            # 保持当前权重
            return self.current_weights
    
    def should_rebalance(self):
        """检查是否需要再平衡"""
        if self.last_rebalance is None:
            return True
        
        days_since_rebalance = (pd.Timestamp.now() - self.last_rebalance).days
        return days_since_rebalance >= self.rebalance_frequency
```

## 7.3.3 多目标优化

### 多目标投资组合优化

```python
class MultiObjectiveOptimizer:
    """多目标优化器"""
    
    def __init__(self, asset_count):
        self.asset_count = asset_count
    
    def optimize_multi_objective(self, expected_returns, covariance_matrix, 
                               return_weight=0.5, risk_weight=0.3, diversification_weight=0.2):
        """多目标优化"""
        from scipy.optimize import minimize
        
        def objective(weights):
            # 收益率目标
            portfolio_return = np.sum(weights * expected_returns)
            return_objective = -portfolio_return  # 最大化收益率
            
            # 风险目标
            portfolio_risk = np.sqrt(weights.T @ covariance_matrix @ weights)
            risk_objective = portfolio_risk  # 最小化风险
            
            # 分散化目标
            diversification = -np.sum(weights ** 2)  # 最大化分散化
            
            # 加权目标
            total_objective = (return_weight * return_objective + 
                             risk_weight * risk_objective + 
                             diversification_weight * diversification)
            
            return total_objective
        
        # 约束条件
        constraints = [
            {'type': 'eq', 'fun': lambda x: np.sum(x) - 1},  # 权重和为1
        ]
        
        bounds = [(0, 1) for _ in range(self.asset_count)]
        
        # 初始权重
        initial_weights = np.ones(self.asset_count) / self.asset_count
        
        # 优化
        result = minimize(objective, initial_weights, method='SLSQP', 
                        bounds=bounds, constraints=constraints)
        
        return result.x
    
    def pareto_optimization(self, expected_returns, covariance_matrix, num_solutions=10):
        """帕累托优化"""
        from pymoo.core.problem import Problem
        from pymoo.algorithms.moo.nsga2 import NSGA2
        from pymoo.factory import get_sampling, get_crossover, get_mutation
        from pymoo.optimize import minimize
        
        class PortfolioProblem(Problem):
            def __init__(self, expected_returns, covariance_matrix):
                super().__init__(n_var=len(expected_returns), n_obj=3, n_constr=1)
                self.expected_returns = expected_returns
                self.covariance_matrix = covariance_matrix
            
            def _evaluate(self, X, out, *args, **kwargs):
                f1 = []  # 收益率目标
                f2 = []  # 风险目标
                f3 = []  # 分散化目标
                g1 = []  # 约束条件
                
                for weights in X:
                    # 收益率
                    portfolio_return = np.sum(weights * self.expected_returns)
                    f1.append(-portfolio_return)
                    
                    # 风险
                    portfolio_risk = np.sqrt(weights.T @ self.covariance_matrix @ weights)
                    f2.append(portfolio_risk)
                    
                    # 分散化
                    diversification = -np.sum(weights ** 2)
                    f3.append(diversification)
                    
                    # 约束：权重和为1
                    g1.append(np.sum(weights) - 1)
                
                out["F"] = np.column_stack([f1, f2, f3])
                out["G"] = np.column_stack([g1])
        
        # 创建问题
        problem = PortfolioProblem(expected_returns, covariance_matrix)
        
        # 算法设置
        algorithm = NSGA2(
            pop_size=100,
            sampling=get_sampling("real_random"),
            crossover=get_crossover("real_sbx", prob=0.9, eta=15),
            mutation=get_mutation("real_pm", eta=20),
            eliminate_duplicates=True
        )
        
        # 优化
        res = minimize(problem, algorithm, ('n_gen', 100), verbose=True)
        
        return res.X, res.F
```

### 目标风险策略

```python
class TargetRiskStrategy:
    """目标风险策略"""
    
    def __init__(self, benchmark_returns, risk_free_rate=0.02):
        self.benchmark_returns = benchmark_returns
        self.risk_free_rate = risk_free_rate
    
    def calculate_optimal_weights(self, returns_data):
        """计算最优权重"""
        # 计算预期收益率和协方差矩阵
        expected_returns = returns_data.mean()
        cov_matrix = returns_data.cov()
        
        # 使用最大夏普比率优化
        from scipy.optimize import minimize
        
        def objective(weights):
            portfolio_return = np.sum(weights * expected_returns)
            portfolio_vol = np.sqrt(np.dot(weights.T, np.dot(cov_matrix, weights)))
            sharpe_ratio = (portfolio_return - self.risk_free_rate) / portfolio_vol
            
            # 目标：最大化夏普比率，同时控制风险
            risk_penalty = max(0, portfolio_vol - self.target_volatility) ** 2
            
            return -(sharpe_ratio - risk_penalty)
        
        # 约束条件
        constraints = [
            {'type': 'eq', 'fun': lambda x: np.sum(x) - 1},  # 权重和为1
        ]
        
        bounds = [(0, 1) for _ in range(len(expected_returns))]
        
        # 初始权重
        initial_weights = np.array([1/len(expected_returns)] * len(expected_returns))
        
        # 优化
        result = minimize(objective, initial_weights, method='SLSQP', 
                        bounds=bounds, constraints=constraints)
        
        return dict(zip(returns_data.columns, result.x))
    
    def generate_trade_decision(self, returns_data):
        """生成交易决策"""
        # 计算最优权重
        weights = self.calculate_optimal_weights(returns_data)
        
        # 应用风险度调整
        adjusted_weights = {stock: weight * self.risk_degree 
                          for stock, weight in weights.items()}
        
        return adjusted_weights
```

## 7.3.4 实时决策系统

### 实时决策框架

```python
class RealTimeDecisionSystem:
    """实时决策系统"""
    
    def __init__(self, market_data_handler, portfolio_optimizer, risk_manager):
        self.market_data_handler = market_data_handler
        self.portfolio_optimizer = portfolio_optimizer
        self.risk_manager = risk_manager
        self.current_portfolio = None
        self.decision_history = []
    
    def update_market_data(self, new_data):
        """更新市场数据"""
        self.market_data_handler.update(new_data)
    
    def get_current_state(self):
        """获取当前状态"""
        market_data = self.market_data_handler.get_latest_data()
        portfolio_state = self.current_portfolio.get_state() if self.current_portfolio else None
        
        return {
            'market_data': market_data,
            'portfolio_state': portfolio_state,
            'timestamp': pd.Timestamp.now()
        }
    
    def make_decision(self):
        """做出决策"""
        # 获取当前状态
        current_state = self.get_current_state()
        
        # 风险检查
        risk_check = self.risk_manager.check_risk(current_state)
        
        if not risk_check['passed']:
            # 风险过高，执行风险控制
            return self.execute_risk_control(risk_check['actions'])
        
        # 投资组合优化
        optimal_weights = self.portfolio_optimizer.optimize(current_state)
        
        # 生成交易决策
        trading_decision = self.generate_trading_decision(optimal_weights)
        
        # 记录决策
        decision_record = {
            'timestamp': current_state['timestamp'],
            'optimal_weights': optimal_weights,
            'trading_decision': trading_decision,
            'risk_check': risk_check
        }
        self.decision_history.append(decision_record)
        
        return trading_decision
    
    def generate_trading_decision(self, optimal_weights):
        """生成交易决策"""
        if self.current_portfolio is None:
            # 初始投资组合
            return {
                'type': 'INITIAL',
                'weights': optimal_weights,
                'orders': self.generate_orders(optimal_weights)
            }
        
        # 计算权重调整
        current_weights = self.current_portfolio.get_weights()
        weight_changes = optimal_weights - current_weights
        
        # 生成交易订单
        orders = self.generate_orders_from_changes(weight_changes)
        
        return {
            'type': 'REBALANCE',
            'weights': optimal_weights,
            'orders': orders
        }
    
    def generate_orders(self, target_weights):
        """生成订单"""
        orders = []
        
        for asset, weight in enumerate(target_weights):
            if weight > 0:
                order = {
                    'asset': asset,
                    'action': 'BUY',
                    'quantity': weight,
                    'type': 'MARKET'
                }
                orders.append(order)
        
        return orders
    
    def generate_orders_from_changes(self, weight_changes):
        """根据权重变化生成订单"""
        orders = []
        
        for asset, change in enumerate(weight_changes):
            if abs(change) > 0.01:  # 最小调整阈值
                if change > 0:
                    order = {
                        'asset': asset,
                        'action': 'BUY',
                        'quantity': change,
                        'type': 'MARKET'
                    }
                else:
                    order = {
                        'asset': asset,
                        'action': 'SELL',
                        'quantity': abs(change),
                        'type': 'MARKET'
                    }
                orders.append(order)
        
        return orders
    
    def execute_risk_control(self, risk_actions):
        """执行风险控制"""
        orders = []
        
        for action in risk_actions:
            if action['type'] == 'REDUCE_POSITION':
                order = {
                    'asset': action['asset'],
                    'action': 'SELL',
                    'quantity': action['quantity'],
                    'type': 'MARKET',
                    'reason': 'RISK_CONTROL'
                }
                orders.append(order)
        
        return {
            'type': 'RISK_CONTROL',
            'orders': orders
        }
    
    def update_portfolio(self, execution_results):
        """更新投资组合"""
        if self.current_portfolio is None:
            self.current_portfolio = Portfolio()
        
        # 更新持仓
        for execution in execution_results:
            asset = execution['asset']
            quantity = execution['quantity']
            price = execution['price']
            
            if execution['action'] == 'BUY':
                self.current_portfolio.add_position(asset, quantity, price)
            else:
                self.current_portfolio.reduce_position(asset, quantity, price)
```

### 决策系统优化

```python
class DecisionSystemOptimizer:
    """决策系统优化器"""
    
    def __init__(self, decision_system):
        self.decision_system = decision_system
        self.performance_metrics = []
    
    def evaluate_decision_performance(self, decision, market_data):
        """评估决策性能"""
        # 计算决策后的投资组合表现
        portfolio_return = self.calculate_portfolio_return(decision, market_data)
        benchmark_return = self.calculate_benchmark_return(market_data)
        
        # 计算超额收益
        excess_return = portfolio_return - benchmark_return
        
        # 计算风险调整收益
        risk_adjusted_return = self.calculate_risk_adjusted_return(decision, market_data)
        
        return {
            'excess_return': excess_return,
            'risk_adjusted_return': risk_adjusted_return,
            'decision_quality': self.assess_decision_quality(decision)
        }
    
    def optimize_decision_parameters(self, historical_decisions, performance_data):
        """优化决策参数"""
        # 分析历史决策的性能
        decision_performance = {}
        
        for decision, performance in zip(historical_decisions, performance_data):
            decision_type = decision['type']
            if decision_type not in decision_performance:
                decision_performance[decision_type] = []
            decision_performance[decision_type].append(performance)
        
        # 计算各类型决策的平均性能
        avg_performance = {}
        for decision_type, performances in decision_performance.items():
            avg_performance[decision_type] = {
                'avg_excess_return': np.mean([p['excess_return'] for p in performances]),
                'avg_risk_adjusted_return': np.mean([p['risk_adjusted_return'] for p in performances]),
                'success_rate': np.mean([p['excess_return'] > 0 for p in performances])
            }
        
        # 根据性能调整决策参数
        self.adjust_decision_parameters(avg_performance)
        
        return avg_performance
    
    def adjust_decision_parameters(self, performance_analysis):
        """根据性能分析调整决策参数"""
        # 根据决策类型调整参数
        for decision_type, performance in performance_analysis.items():
            if performance['success_rate'] < 0.5:
                # 成功率低，增加保守性
                self.increase_conservatism(decision_type)
            elif performance['avg_excess_return'] > 0.01:
                # 表现好，增加积极性
                self.increase_aggressiveness(decision_type)
    
    def increase_conservatism(self, decision_type):
        """增加保守性"""
        if decision_type == 'REBALANCE':
            # 增加再平衡阈值
            self.decision_system.rebalance_threshold *= 1.2
        elif decision_type == 'RISK_CONTROL':
            # 降低风险阈值
            self.decision_system.risk_threshold *= 0.8
    
    def increase_aggressiveness(self, decision_type):
        """增加积极性"""
        if decision_type == 'REBALANCE':
            # 降低再平衡阈值
            self.decision_system.rebalance_threshold *= 0.9
        elif decision_type == 'RISK_CONTROL':
            # 提高风险阈值
            self.decision_system.risk_threshold *= 1.1
```

## 本节小结

本节详细介绍了投资组合优化的各种方法，包括：

1. **动态资产配置**：强化学习资产配置和自适应资产配置
2. **风险预算管理**：风险预算策略和风险平价策略
3. **多目标优化**：多目标投资组合优化和帕累托优化
4. **实时决策系统**：实时决策框架和决策系统优化

## 课后练习

### 练习1：动态资产配置
1. 实现强化学习资产配置
2. 添加自适应机制
3. 测试不同市场条件下的表现

### 练习2：风险预算管理
1. 实现风险平价策略
2. 设计动态风险预算
3. 评估风险控制效果

### 练习3：多目标优化
1. 实现多目标投资组合优化
2. 使用帕累托优化
3. 分析优化结果

## 扩展阅读

1. **投资组合理论**
   - 《现代投资组合理论》
   - 《风险管理与投资组合》

2. **强化学习应用**
   - 《强化学习在金融中的应用》
   - 《深度强化学习》 