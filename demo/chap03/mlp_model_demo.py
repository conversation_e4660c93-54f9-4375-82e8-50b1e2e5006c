#!/usr/bin/env python3
"""
MLP神经网络模型演示
基于第3章监督学习模型内容
"""

import qlib
import torch
import torch.nn as nn
import numpy as np
from qlib.contrib.data.handler import Alpha158
from qlib.contrib.model.pytorch_nn import DNNModelPytorch
from qlib.data.dataset import DatasetH

if __name__ == '__main__':
    # 初始化Qlib
    print("初始化Qlib...")
    qlib.init(mount_path="~/.qlib/qlib_data/cn_data", region="cn")
    
    print("准备数据...")
    
    # 准备数据处理器
    handler = Alpha158(
        instruments='csi300',
        start_time='2020-01-01',
        end_time='2020-12-31',
        freq='day'
    )
    
    # 创建数据集
    dataset = DatasetH(
        handler=handler,
        segments={
            'train': ('2020-01-01', '2020-06-30'),
            'valid': ('2020-07-01', '2020-09-30'),
            'test': ('2020-10-01', '2020-12-31')
        }
    )
    
    print("创建MLP模型...")
    
    # 获取特征数量
    train_data = dataset.prepare('train')
    feature_dim = train_data['feature'].shape[1]
    print(f"特征维度: {feature_dim}")
    
    # 定义MLP网络结构
    class MLPModel(nn.Module):
        def __init__(self, input_size, hidden_size=128, num_layers=3, dropout=0.2):
            super(MLPModel, self).__init__()
            
            layers = []
            layers.append(nn.Linear(input_size, hidden_size))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout))
            
            for _ in range(num_layers - 2):
                layers.append(nn.Linear(hidden_size, hidden_size))
                layers.append(nn.ReLU())
                layers.append(nn.Dropout(dropout))
            
            layers.append(nn.Linear(hidden_size, 1))
            
            self.network = nn.Sequential(*layers)
        
        def forward(self, x):
            return self.network(x)
    
    # 创建MLP模型
    mlp_net = MLPModel(
        input_size=feature_dim,
        hidden_size=64,  # 减小隐藏层大小以适应数据量
        num_layers=3,
        dropout=0.3
    )
    
    # 包装为Qlib模型
    model = DNNModelPytorch(
        model=mlp_net,
        optimizer='adam',
        loss='mse',
        lr=0.001,
        max_epochs=50,  # 减少训练轮数用于演示
        batch_size=256,
        early_stop=10,
        GPU=0 if torch.cuda.is_available() else None
    )
    
    print("训练模型...")
    model.fit(dataset)
    
    print("模型预测...")
    predictions = model.predict(dataset, segment='test')
    
    print("预测结果样例：")
    print(predictions.head(10))
    
    print("模型评估...")
    # 获取测试数据的真实标签
    test_data = dataset.prepare('test')
    y_true = test_data['label']['LABEL0']
    
    # 计算MSE
    mse = np.mean((predictions.values.flatten() - y_true.values.flatten()) ** 2)
    print(f"测试集MSE: {mse:.6f}")
    
    # 计算相关系数
    correlation = np.corrcoef(predictions.values.flatten(), y_true.values.flatten())[0, 1]
    print(f"预测相关系数: {correlation:.6f}")
    
    print("MLP模型演示完成！")