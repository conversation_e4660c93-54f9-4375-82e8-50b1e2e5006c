#!/usr/bin/env python3
"""
Transformer模型示例
基于第3章深度学习模型内容
"""

import qlib
import torch
import torch.nn as nn
import math
import numpy as np
from qlib.contrib.data.handler import Alpha158
from qlib.contrib.model.pytorch_nn import DNNModelPytorch
from qlib.data.dataset import DatasetH

# 初始化Qlib
print("初始化Qlib...")
qlib.init(mount_path="~/.qlib/qlib_data/cn_data", region="cn")

class PositionalEncoding(nn.Module):
    def __init__(self, d_model, max_len=5000):
        super(PositionalEncoding, self).__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        return x + self.pe[:x.size(0), :]

class TransformerModel(nn.Module):
    def __init__(self, input_size, d_model=128, nhead=8, num_layers=3, dropout=0.1):
        super(TransformerModel, self).__init__()
        
        self.d_model = d_model
        self.input_projection = nn.Linear(input_size, d_model)
        self.pos_encoder = PositionalEncoding(d_model)
        
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            batch_first=False  # Transformer expects (seq_len, batch_size, d_model)
        )
        
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers)
        self.fc = nn.Linear(d_model, 1)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        # 确保输入是3维的 (batch_size, seq_len, input_size)
        if len(x.shape) == 2:
            x = x.unsqueeze(1)  # 添加时间维度
        
        # 投影到模型维度
        x = self.input_projection(x)  # (batch_size, seq_len, d_model)
        x = x * math.sqrt(self.d_model)
        
        # 转换为 Transformer 期望的格式
        x = x.transpose(0, 1)  # (seq_len, batch_size, d_model)
        x = self.pos_encoder(x)
        
        # Transformer编码
        x = self.transformer_encoder(x)
        x = x.transpose(0, 1)  # (batch_size, seq_len, d_model)
        
        # 取最后一个时间步的输出
        last_output = x[:, -1, :]
        
        # Dropout和全连接层
        output = self.dropout(last_output)
        output = self.fc(output)
        return output

def main():
    print("准备数据...")
    
    # 准备数据处理器
    handler = Alpha158(
        instruments='csi300',
        start_time='2020-01-01',
        end_time='2020-12-31',
        freq='day'
    )
    
    # 创建数据集
    dataset = DatasetH(
        handler=handler,
        segments={
            'train': ('2020-01-01', '2020-06-30'),
            'valid': ('2020-07-01', '2020-09-30'),
            'test': ('2020-10-01', '2020-12-31')
        }
    )
    
    print("创建Transformer模型...")
    
    # 获取特征数量
    train_data = dataset.prepare('train')
    feature_dim = train_data['feature'].shape[1]
    
    # 创建Transformer模型
    model = TransformerModel(
        input_size=feature_dim,
        d_model=64,  # 减小模型尺寸避免过拟合
        nhead=8,
        num_layers=2,
        dropout=0.3
    )
    
    # 包装为Qlib模型
    qlib_model = DNNModelPytorch(
        model=model,
        optimizer='adam',
        loss='mse',
        lr=0.0001,  # 使用较小的学习率
        max_epochs=30,
        batch_size=128,
        early_stop=10,
        GPU=0 if torch.cuda.is_available() else None
    )
    
    print("训练模型...")
    qlib_model.fit(dataset)
    
    print("模型预测...")
    predictions = qlib_model.predict(dataset, segment='test')
    
    print("预测结果样例：")
    print(predictions.head(10))
    
    print("模型评估...")
    # 获取测试数据的真实标签
    test_data = dataset.prepare('test')
    y_true = test_data['label']['LABEL0']
    
    # 计算MSE
    mse = np.mean((predictions.values.flatten() - y_true.values.flatten()) ** 2)
    print(f"测试集MSE: {mse:.6f}")
    
    # 计算相关系数
    correlation = np.corrcoef(predictions.values.flatten(), y_true.values.flatten())[0, 1]
    print(f"预测相关系数: {correlation:.6f}")

if __name__ == "__main__":
    main()