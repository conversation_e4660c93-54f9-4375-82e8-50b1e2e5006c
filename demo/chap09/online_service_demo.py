#!/usr/bin/env python3
"""
在线服务架构演示
基于第9章在线服务与部署内容
"""

import qlib
import numpy as np
import pandas as pd
from qlib.contrib.model.gbdt import LGBModel
from qlib.contrib.data.handler import Alpha158
import pickle
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 初始化Qlib
print("初始化Qlib...")
qlib.init(mount_path="~/.qlib/qlib_data/cn_data", region="cn")

class ModelService:
    """模型服务类"""
    
    def __init__(self, model_path=None):
        self.model = None
        self.model_metadata = {}
        self.prediction_cache = {}
        self.service_stats = {
            'requests': 0,
            'cache_hits': 0,
            'errors': 0,
            'start_time': datetime.now()
        }
        
        if model_path:
            self.load_model(model_path)
    
    def load_model(self, model_path):
        """加载模型"""
        try:
            with open(model_path, 'rb') as f:
                model_data = pickle.load(f)
            
            if isinstance(model_data, dict):
                self.model = model_data['model']
                self.model_metadata = model_data.get('metadata', {})
            else:
                self.model = model_data
            
            print(f"模型加载成功: {model_path}")
        except Exception as e:
            print(f"模型加载失败: {e}")
            self.service_stats['errors'] += 1
    
    def predict(self, features, use_cache=True):
        """预测服务"""
        self.service_stats['requests'] += 1
        
        try:
            # 生成缓存键
            cache_key = hash(str(features.values.tobytes())) if hasattr(features, 'values') else hash(str(features))
            
            # 检查缓存
            if use_cache and cache_key in self.prediction_cache:
                self.service_stats['cache_hits'] += 1
                return self.prediction_cache[cache_key]
            
            # 模型预测
            if self.model is None:
                raise ValueError("模型未加载")
            
            prediction = self.model.predict(features)
            
            # 缓存结果
            if use_cache:
                self.prediction_cache[cache_key] = prediction
            
            return prediction
            
        except Exception as e:
            self.service_stats['errors'] += 1
            print(f"预测错误: {e}")
            return None
    
    def get_service_stats(self):
        """获取服务统计信息"""
        runtime = (datetime.now() - self.service_stats['start_time']).total_seconds()
        
        stats = self.service_stats.copy()
        stats['runtime_seconds'] = runtime
        stats['requests_per_second'] = stats['requests'] / runtime if runtime > 0 else 0
        stats['cache_hit_rate'] = stats['cache_hits'] / stats['requests'] if stats['requests'] > 0 else 0
        stats['error_rate'] = stats['errors'] / stats['requests'] if stats['requests'] > 0 else 0
        
        return stats
    
    def health_check(self):
        """健康检查"""
        try:
            if self.model is None:
                return {'status': 'unhealthy', 'reason': 'model_not_loaded'}
            
            # 简单的预测测试
            test_features = np.random.random((1, 10))
            _ = self.model.predict(test_features)
            
            return {'status': 'healthy', 'timestamp': datetime.now().isoformat()}
        
        except Exception as e:
            return {'status': 'unhealthy', 'reason': str(e)}

class DataService:
    """数据服务类"""
    
    def __init__(self):
        self.data_cache = {}
        self.cache_ttl = 300  # 5分钟缓存
        self.handler = None
    
    def initialize_handler(self):
        """初始化数据处理器"""
        try:
            self.handler = Alpha158(
                instruments='csi300',
                start_time='2020-01-01',
                end_time='2020-12-31',
                freq='day'
            )
            print("数据处理器初始化成功")
        except Exception as e:
            print(f"数据处理器初始化失败: {e}")
    
    def get_latest_features(self, date=None, use_cache=True):
        """获取最新特征数据"""
        if self.handler is None:
            self.initialize_handler()
        
        cache_key = f"features_{date}"
        current_time = datetime.now().timestamp()
        
        # 检查缓存
        if use_cache and cache_key in self.data_cache:
            cached_data, cache_time = self.data_cache[cache_key]
            if current_time - cache_time < self.cache_ttl:
                return cached_data
        
        try:
            # 获取数据（这里简化处理）
            data = self.handler.fetch(
                segments={'latest': ('2020-12-01', '2020-12-31')}
            )
            
            features = data['latest']['feature']
            
            # 缓存数据
            if use_cache:
                self.data_cache[cache_key] = (features, current_time)
            
            return features
            
        except Exception as e:
            print(f"获取特征数据失败: {e}")
            return None

class PredictionAPI:
    """预测API类"""
    
    def __init__(self):
        self.model_service = ModelService()
        self.data_service = DataService()
        self.request_log = []
    
    def initialize_services(self, model_path):
        """初始化服务"""
        self.model_service.load_model(model_path)
        self.data_service.initialize_handler()
    
    def predict_endpoint(self, request_data):
        """预测端点"""
        request_id = len(self.request_log)
        start_time = datetime.now()
        
        try:
            # 解析请求
            if 'features' in request_data:
                features = pd.DataFrame(request_data['features'])
            else:
                # 获取最新特征
                features = self.data_service.get_latest_features()
                if features is None:
                    raise ValueError("无法获取特征数据")
                
                # 取最新一行
                features = features.tail(1)
            
            # 执行预测
            prediction = self.model_service.predict(features)
            
            # 构造响应
            response = {
                'request_id': request_id,
                'prediction': prediction.tolist() if hasattr(prediction, 'tolist') else prediction,
                'timestamp': datetime.now().isoformat(),
                'status': 'success'
            }
            
        except Exception as e:
            response = {
                'request_id': request_id,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'status': 'error'
            }
        
        # 记录请求
        processing_time = (datetime.now() - start_time).total_seconds()
        log_entry = {
            'request_id': request_id,
            'timestamp': start_time.isoformat(),
            'processing_time': processing_time,
            'status': response['status']
        }
        self.request_log.append(log_entry)
        
        return response
    
    def batch_predict_endpoint(self, batch_request):
        """批量预测端点"""
        results = []
        
        for i, request_data in enumerate(batch_request):
            result = self.predict_endpoint(request_data)
            result['batch_index'] = i
            results.append(result)
        
        return {
            'batch_size': len(batch_request),
            'results': results,
            'timestamp': datetime.now().isoformat()
        }
    
    def get_api_stats(self):
        """获取API统计信息"""
        if not self.request_log:
            return {'message': 'No requests processed yet'}
        
        total_requests = len(self.request_log)
        successful_requests = sum(1 for log in self.request_log if log['status'] == 'success')
        avg_processing_time = np.mean([log['processing_time'] for log in self.request_log])
        
        return {
            'total_requests': total_requests,
            'successful_requests': successful_requests,
            'success_rate': successful_requests / total_requests,
            'average_processing_time': avg_processing_time,
            'model_service_stats': self.model_service.get_service_stats()
        }

def create_sample_model():
    """创建示例模型用于演示"""
    print("创建示例模型...")
    
    # 准备数据
    handler = Alpha158(
        instruments='csi300',
        start_time='2020-01-01',
        end_time='2020-06-30',
        freq='day'
    )
    
    from qlib.data.dataset import DatasetH
    dataset = DatasetH(
        handler=handler,
        segments={'train': ('2020-01-01', '2020-06-30')}
    )
    
    # 训练模型
    model = LGBModel(
        loss='mse',
        n_estimators=30,
        max_depth=6,
        learning_rate=0.1,
        verbose=-1
    )
    
    model.fit(dataset)
    
    # 保存模型
    model_data = {
        'model': model,
        'metadata': {
            'created_at': datetime.now().isoformat(),
            'model_type': 'LGBModel',
            'features': list(data['train']['feature'].columns),
            'training_samples': len(data['train']['feature'])
        }
    }
    
    model_path = 'demo_model.pkl'
    with open(model_path, 'wb') as f:
        pickle.dump(model_data, f)
    
    print(f"模型已保存到: {model_path}")
    return model_path

def main():
    print("=== 在线服务架构演示 ===")
    
    # 创建示例模型
    model_path = create_sample_model()
    
    print("\n=== 模型服务演示 ===")
    
    # 创建模型服务
    model_service = ModelService(model_path)
    
    # 健康检查
    health = model_service.health_check()
    print(f"健康检查结果: {health}")
    
    # 模拟预测请求
    print("\n模拟预测请求...")
    for i in range(5):
        # 生成随机特征
        features = pd.DataFrame(np.random.random((1, 158)))
        prediction = model_service.predict(features)
        print(f"请求 {i+1}: 预测结果长度 = {len(prediction) if prediction is not None else 0}")
    
    # 获取服务统计
    stats = model_service.get_service_stats()
    print(f"\n模型服务统计: {stats}")
    
    print("\n=== 数据服务演示 ===")
    
    # 创建数据服务
    data_service = DataService()
    
    # 获取特征数据
    features = data_service.get_latest_features()
    if features is not None:
        print(f"获取特征数据成功，形状: {features.shape}")
    else:
        print("获取特征数据失败")
    
    print("\n=== 预测API演示 ===")
    
    # 创建预测API
    api = PredictionAPI()
    api.initialize_services(model_path)
    
    # 单个预测请求
    request_data = {}  # 空请求，将获取最新数据
    response = api.predict_endpoint(request_data)
    print(f"单个预测响应: {response}")
    
    # 批量预测请求
    batch_request = [
        {'features': np.random.random((1, 158)).tolist()},
        {'features': np.random.random((1, 158)).tolist()},
        {}  # 空请求
    ]
    
    batch_response = api.batch_predict_endpoint(batch_request)
    print(f"批量预测响应数量: {len(batch_response['results'])}")
    
    # API统计
    api_stats = api.get_api_stats()
    print(f"API统计: {api_stats}")
    
    print("\n=== 监控和日志演示 ===")
    
    class ServiceMonitor:
        """服务监控类"""
        
        def __init__(self):
            self.alerts = []
            self.metrics = {}
        
        def check_service_health(self, api):
            """检查服务健康状态"""
            health = api.model_service.health_check()
            stats = api.get_api_stats()
            
            # 检查错误率
            if stats.get('success_rate', 1) < 0.9:
                self.alerts.append({
                    'type': 'high_error_rate',
                    'message': f"错误率过高: {1 - stats['success_rate']:.2%}",
                    'timestamp': datetime.now().isoformat()
                })
            
            # 检查响应时间
            if stats.get('average_processing_time', 0) > 1.0:
                self.alerts.append({
                    'type': 'slow_response',
                    'message': f"响应时间过慢: {stats['average_processing_time']:.2f}s",
                    'timestamp': datetime.now().isoformat()
                })
            
            self.metrics.update({
                'health_status': health['status'],
                'success_rate': stats.get('success_rate', 0),
                'avg_response_time': stats.get('average_processing_time', 0),
                'total_requests': stats.get('total_requests', 0)
            })
        
        def get_monitoring_report(self):
            """获取监控报告"""
            return {
                'metrics': self.metrics,
                'alerts': self.alerts,
                'report_time': datetime.now().isoformat()
            }
    
    # 服务监控
    monitor = ServiceMonitor()
    monitor.check_service_health(api)
    
    monitoring_report = monitor.get_monitoring_report()
    print(f"监控报告: {json.dumps(monitoring_report, indent=2)}")
    
    print("\n=== 负载均衡演示 ===")
    
    class LoadBalancer:
        """负载均衡器"""
        
        def __init__(self):
            self.services = []
            self.current_index = 0
        
        def add_service(self, service):
            """添加服务实例"""
            self.services.append(service)
        
        def get_next_service(self):
            """轮询获取下一个服务"""
            if not self.services:
                return None
            
            service = self.services[self.current_index]
            self.current_index = (self.current_index + 1) % len(self.services)
            return service
        
        def route_request(self, request_data):
            """路由请求"""
            service = self.get_next_service()
            if service:
                return service.predict_endpoint(request_data)
            else:
                return {'error': 'No available services', 'status': 'error'}
    
    # 创建负载均衡器
    load_balancer = LoadBalancer()
    
    # 添加多个API实例
    for i in range(3):
        api_instance = PredictionAPI()
        api_instance.initialize_services(model_path)
        load_balancer.add_service(api_instance)
    
    # 测试负载均衡
    print("测试负载均衡...")
    for i in range(5):
        response = load_balancer.route_request({})
        print(f"请求 {i+1} 路由结果: {response.get('status', 'unknown')}")
    
    print("\n在线服务架构演示完成！")

if __name__ == "__main__":
    main()