#!/usr/bin/env python3
"""
最佳实践与案例分析演示
基于第11章最佳实践与案例分析内容
"""

import qlib
import numpy as np
import pandas as pd
from qlib.contrib.model.gbdt import LGBModel
from qlib.contrib.data.handler import Alpha158
from datetime import datetime, timedelta
import json
import warnings
warnings.filterwarnings('ignore')

# 初始化Qlib
print("初始化Qlib...")
qlib.init(mount_path="~/.qlib/qlib_data/cn_data", region="cn")

class QuantitativeBestPractices:
    """量化投资最佳实践指南"""
    
    def __init__(self):
        self.practices = {
            'data_quality': {
                '数据清洗': '处理异常值、缺失值和重复数据',
                '数据验证': '建立数据一致性检查机制',
                '数据版本控制': '跟踪数据变更和版本历史',
                '数据源多样化': '使用多个数据提供商降低单点风险'
            },
            'feature_engineering': {
                '特征稳定性': '选择在不同市场环境下稳定的特征',
                '特征相关性': '避免高度相关的冗余特征',
                '特征解释性': '确保特征具有经济学意义',
                '特征时效性': '考虑特征的时间衰减效应'
            },
            'model_development': {
                '模型复杂度控制': '平衡模型复杂度和泛化能力',
                '交叉验证': '使用时间序列交叉验证',
                '模型集成': '结合多个模型降低风险',
                '正则化': '使用正则化防止过拟合'
            },
            'risk_management': {
                '风险预算': '合理分配各类风险敞口',
                '止损机制': '设置明确的止损条件',
                '压力测试': '在极端市场条件下测试策略',
                '动态调整': '根据市场变化调整风险参数'
            },
            'performance_evaluation': {
                'IC分析': '全面分析信息系数的稳定性',
                '归因分析': '分解收益来源和风险因子',
                '基准比较': '与合适的基准进行比较',
                '时间段分析': '分析不同时间段的表现差异'
            }
        }
    
    def get_checklist(self, category=None):
        """获取最佳实践检查清单"""
        if category:
            return self.practices.get(category, {})
        else:
            return self.practices
    
    def evaluate_practice_score(self, implementation):
        """评估最佳实践实施情况"""
        total_practices = sum(len(practices) for practices in self.practices.values())
        implemented_count = sum(implementation.values())
        
        return {
            'score': implemented_count / total_practices,
            'implemented': implemented_count,
            'total': total_practices,
            'grade': self._get_grade(implemented_count / total_practices)
        }
    
    def _get_grade(self, score):
        """根据分数获取等级"""
        if score >= 0.9:
            return 'A'
        elif score >= 0.8:
            return 'B'
        elif score >= 0.7:
            return 'C'
        elif score >= 0.6:
            return 'D'
        else:
            return 'F'

class CommonMistakes:
    """常见错误案例"""
    
    def __init__(self):
        self.mistakes = {
            'data_snooping': {
                'description': '数据窥探偏差 - 过度优化历史数据',
                'example': '反复调整参数直到历史回测表现最佳',
                'solution': '使用严格的样本外验证和walk-forward分析'
            },
            'survivorship_bias': {
                'description': '生存偏差 - 只考虑存续的股票',
                'example': '回测时排除已退市的股票',
                'solution': '包含所有历史存在的股票，考虑退市风险'
            },
            'lookahead_bias': {
                'description': '前瞻偏差 - 使用未来信息',
                'example': '使用当日收盘价作为交易价格',
                'solution': '严格按照信息可得时间进行建模'
            },
            'overfitting': {
                'description': '过拟合 - 模型过度拟合训练数据',
                'example': '使用过多参数和复杂模型',
                'solution': '使用正则化、交叉验证和模型简化'
            },
            'transaction_costs': {
                'description': '忽略交易成本 - 理想化的交易假设',
                'example': '假设可以无成本地频繁交易',
                'solution': '现实地建模交易成本和市场冲击'
            }
        }
    
    def get_mistake_analysis(self, mistake_type=None):
        """获取错误分析"""
        if mistake_type:
            return self.mistakes.get(mistake_type, {})
        else:
            return self.mistakes
    
    def detect_potential_issues(self, model_results):
        """检测潜在问题"""
        issues = []
        
        # 检查过拟合征象
        train_score = model_results.get('train_ic', 0)
        test_score = model_results.get('test_ic', 0)
        
        if train_score - test_score > 0.05:
            issues.append({
                'type': 'overfitting',
                'severity': 'high',
                'description': f'训练集IC({train_score:.4f})显著高于测试集IC({test_score:.4f})'
            })
        
        # 检查IC不稳定
        ic_std = model_results.get('ic_std', 0)
        ic_mean = model_results.get('ic_mean', 0)
        
        if ic_std > abs(ic_mean) * 2:
            issues.append({
                'type': 'instability',
                'severity': 'medium',
                'description': f'IC波动过大(标准差{ic_std:.4f} > 2倍均值{ic_mean:.4f})'
            })
        
        return issues

class CaseStudyAnalyzer:
    """案例研究分析器"""
    
    def __init__(self):
        self.case_studies = {
            'momentum_strategy': {
                'name': '动量策略案例',
                'description': '基于价格动量的投资策略',
                'key_factors': ['价格趋势', '成交量确认', '风险控制'],
                'performance_period': '2019-2021',
                'lessons': [
                    '动量策略在趋势市场表现良好',
                    '需要及时止损避免反转风险',
                    '结合基本面可提高策略稳定性'
                ]
            },
            'mean_reversion': {
                'name': '均值回归策略案例',
                'description': '基于价格均值回归的投资策略',
                'key_factors': ['价格偏离度', '基本面支撑', '市场情绪'],
                'performance_period': '2018-2020',
                'lessons': [
                    '均值回归在震荡市场效果更好',
                    '需要判断是暂时偏离还是基本面变化',
                    '持仓时间需要足够长以等待回归'
                ]
            },
            'factor_model': {
                'name': '多因子模型案例',
                'description': '基于多个风险因子的股票选择模型',
                'key_factors': ['价值因子', '成长因子', '质量因子'],
                'performance_period': '2017-2022',
                'lessons': [
                    '因子有效性会随时间变化',
                    '需要定期更新因子权重',
                    '行业中性化可以降低系统性风险'
                ]
            }
        }
    
    def analyze_case(self, case_name):
        """分析具体案例"""
        return self.case_studies.get(case_name, {})
    
    def extract_lessons(self):
        """提取所有案例的经验教训"""
        all_lessons = []
        for case in self.case_studies.values():
            all_lessons.extend(case.get('lessons', []))
        
        return all_lessons

def demonstrate_data_quality_best_practices():
    """演示数据质量最佳实践"""
    
    print("=== 数据质量最佳实践演示 ===")
    
    class DataQualityChecker:
        """数据质量检查器"""
        
        def __init__(self, data):
            self.data = data
            self.quality_report = {}
        
        def check_missing_values(self):
            """检查缺失值"""
            missing_ratio = self.data.isnull().sum() / len(self.data)
            
            self.quality_report['missing_values'] = {
                'columns_with_missing': missing_ratio[missing_ratio > 0].to_dict(),
                'max_missing_ratio': missing_ratio.max(),
                'columns_high_missing': missing_ratio[missing_ratio > 0.1].index.tolist()
            }
        
        def check_outliers(self, method='iqr', threshold=3):
            """检查异常值"""
            numeric_columns = self.data.select_dtypes(include=[np.number]).columns
            outlier_counts = {}
            
            for col in numeric_columns:
                if method == 'iqr':
                    Q1 = self.data[col].quantile(0.25)
                    Q3 = self.data[col].quantile(0.75)
                    IQR = Q3 - Q1
                    outliers = ((self.data[col] < (Q1 - 1.5 * IQR)) | 
                               (self.data[col] > (Q3 + 1.5 * IQR))).sum()
                elif method == 'zscore':
                    z_scores = np.abs((self.data[col] - self.data[col].mean()) / self.data[col].std())
                    outliers = (z_scores > threshold).sum()
                
                outlier_counts[col] = outliers
            
            self.quality_report['outliers'] = outlier_counts
        
        def check_data_consistency(self):
            """检查数据一致性"""
            consistency_issues = []
            
            # 检查日期一致性
            date_columns = self.data.select_dtypes(include=['datetime64']).columns
            for col in date_columns:
                if self.data[col].isnull().any():
                    consistency_issues.append(f"{col} 包含空值日期")
            
            # 检查数值范围
            numeric_columns = self.data.select_dtypes(include=[np.number]).columns
            for col in numeric_columns:
                if (self.data[col] < 0).any() and col.startswith('volume'):
                    consistency_issues.append(f"{col} 包含负值")
            
            self.quality_report['consistency_issues'] = consistency_issues
        
        def generate_report(self):
            """生成质量报告"""
            self.check_missing_values()
            self.check_outliers()
            self.check_data_consistency()
            
            return self.quality_report
    
    # 创建示例数据进行质量检查
    sample_data = pd.DataFrame({
        'price': np.random.normal(100, 10, 1000),
        'volume': np.random.exponential(1000, 1000),
        'feature1': np.random.normal(0, 1, 1000),
        'feature2': np.random.normal(0, 1, 1000)
    })
    
    # 人为添加一些质量问题
    sample_data.loc[50:60, 'price'] = np.nan  # 缺失值
    sample_data.loc[100, 'price'] = 1000  # 异常值
    sample_data.loc[200, 'volume'] = -100  # 负成交量
    
    # 进行质量检查
    quality_checker = DataQualityChecker(sample_data)
    quality_report = quality_checker.generate_report()
    
    print("数据质量检查报告:")
    print(f"缺失值最高比例: {quality_report['missing_values']['max_missing_ratio']:.4f}")
    print(f"高缺失值列数: {len(quality_report['missing_values']['columns_high_missing'])}")
    print(f"异常值数量: {sum(quality_report['outliers'].values())}")
    print(f"一致性问题: {len(quality_report['consistency_issues'])}")

def demonstrate_model_validation_best_practices():
    """演示模型验证最佳实践"""
    
    print("\n=== 模型验证最佳实践演示 ===")
    
    class ModelValidator:
        """模型验证器"""
        
        def __init__(self):
            self.validation_results = {}
        
        def time_series_validation(self, model, data, n_splits=5):
            """时间序列交叉验证"""
            from sklearn.model_selection import TimeSeriesSplit
            
            tscv = TimeSeriesSplit(n_splits=n_splits)
            scores = []
            
            X = data['feature']
            y = data['label']
            
            for train_idx, val_idx in tscv.split(X):
                X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
                y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
                
                # 训练模型
                fold_model = LGBModel(
                    loss='mse',
                    n_estimators=30,
                    max_depth=6,
                    learning_rate=0.1,
                    verbose=-1
                )
                
                fold_model.fit(X_train, y_train)
                pred = fold_model.predict(X_val)
                
                # 计算IC
                ic = self._calculate_ic(pred, y_val.values)
                scores.append(ic)
            
            self.validation_results['time_series_cv'] = {
                'scores': scores,
                'mean_score': np.mean(scores),
                'std_score': np.std(scores)
            }
            
            return scores
        
        def walk_forward_validation(self, model, data, train_window=252, step_size=63):
            """滚动窗口验证"""
            scores = []
            
            X = data['feature']
            y = data['label']
            
            for i in range(train_window, len(X), step_size):
                if i + step_size > len(X):
                    break
                
                # 训练数据
                X_train = X.iloc[i-train_window:i]
                y_train = y.iloc[i-train_window:i]
                
                # 测试数据
                X_test = X.iloc[i:i+step_size]
                y_test = y.iloc[i:i+step_size]
                
                # 训练模型
                fold_model = LGBModel(
                    loss='mse',
                    n_estimators=30,
                    max_depth=6,
                    learning_rate=0.1,
                    verbose=-1
                )
                
                fold_model.fit(X_train, y_train)
                pred = fold_model.predict(X_test)
                
                # 计算IC
                ic = self._calculate_ic(pred, y_test.values)
                scores.append(ic)
            
            self.validation_results['walk_forward'] = {
                'scores': scores,
                'mean_score': np.mean(scores),
                'std_score': np.std(scores)
            }
            
            return scores
        
        def _calculate_ic(self, predictions, returns):
            """计算IC值"""
            valid_mask = ~(np.isnan(predictions) | np.isnan(returns))
            if valid_mask.sum() <= 1:
                return 0
            
            pred_clean = predictions[valid_mask]
            ret_clean = returns[valid_mask]
            
            correlation = np.corrcoef(pred_clean, ret_clean)[0, 1]
            return correlation if not np.isnan(correlation) else 0
    
    # 准备数据进行验证
    handler = Alpha158(
        instruments='csi300',
        start_time='2020-01-01',
        end_time='2020-08-31',
        fit_start_time='2020-01-01',
        fit_end_time='2020-08-31',
        freq='day'
    )
    
    from qlib.data.dataset import DatasetH
    dataset = DatasetH(
        handler=handler,
        segments={'full': ('2020-01-01', '2020-08-31')}
    )
    
    full_data = dataset.prepare('full')
    validation_data = {
        'feature': full_data.drop('LABEL0', axis=1).head(500),  # 使用前500行进行演示
        'label': full_data['LABEL0'].head(500)
    }
    
    # 进行模型验证
    validator = ModelValidator()
    
    # 时间序列交叉验证
    ts_scores = validator.time_series_validation(None, validation_data, n_splits=3)
    
    # 滚动窗口验证
    wf_scores = validator.walk_forward_validation(None, validation_data, train_window=100, step_size=50)
    
    print("模型验证结果:")
    print(f"时间序列CV - 平均IC: {np.mean(ts_scores):.4f}, 标准差: {np.std(ts_scores):.4f}")
    print(f"滚动窗口验证 - 平均IC: {np.mean(wf_scores):.4f}, 标准差: {np.std(wf_scores):.4f}")

def main():
    """主函数"""
    
    print("=== 量化投资最佳实践与案例分析 ===")
    
    # 创建最佳实践指南
    best_practices = QuantitativeBestPractices()
    
    print("最佳实践检查清单:")
    for category, practices in best_practices.get_checklist().items():
        print(f"\n{category.upper()}:")
        for practice, description in practices.items():
            print(f"  □ {practice}: {description}")
    
    # 模拟实施情况评估
    implementation_status = {
        '数据清洗': True,
        '数据验证': True,
        '数据版本控制': False,
        '数据源多样化': True,
        '特征稳定性': True,
        '特征相关性': True,
        '特征解释性': False,
        '特征时效性': True,
        '模型复杂度控制': True,
        '交叉验证': True,
        '模型集成': False,
        '正则化': True,
        '风险预算': False,
        '止损机制': True,
        '压力测试': False,
        '动态调整': True,
        'IC分析': True,
        '归因分析': False,
        '基准比较': True,
        '时间段分析': True
    }
    
    evaluation = best_practices.evaluate_practice_score(implementation_status)
    print(f"\n最佳实践实施评估:")
    print(f"总分: {evaluation['score']:.2%}")
    print(f"等级: {evaluation['grade']}")
    print(f"已实施: {evaluation['implemented']}/{evaluation['total']}")
    
    # 常见错误分析
    print("\n=== 常见错误分析 ===")
    
    mistakes_analyzer = CommonMistakes()
    
    for mistake_type, details in mistakes_analyzer.get_mistake_analysis().items():
        print(f"\n{mistake_type.upper()}:")
        print(f"  描述: {details['description']}")
        print(f"  示例: {details['example']}")
        print(f"  解决方案: {details['solution']}")
    
    # 潜在问题检测演示
    print("\n=== 潜在问题检测 ===")
    
    model_results = {
        'train_ic': 0.08,
        'test_ic': 0.03,
        'ic_mean': 0.02,
        'ic_std': 0.06
    }
    
    issues = mistakes_analyzer.detect_potential_issues(model_results)
    
    if issues:
        print("检测到以下问题:")
        for issue in issues:
            print(f"  - {issue['type'].upper()} ({issue['severity']}): {issue['description']}")
    else:
        print("未检测到明显问题")
    
    # 案例研究分析
    print("\n=== 案例研究分析 ===")
    
    case_analyzer = CaseStudyAnalyzer()
    
    for case_name, case_info in case_analyzer.case_studies.items():
        print(f"\n{case_info['name']}:")
        print(f"  描述: {case_info['description']}")
        print(f"  关键因子: {', '.join(case_info['key_factors'])}")
        print(f"  业绩期间: {case_info['performance_period']}")
        print("  经验教训:")
        for lesson in case_info['lessons']:
            print(f"    - {lesson}")
    
    # 数据质量最佳实践演示
    demonstrate_data_quality_best_practices()
    
    # 模型验证最佳实践演示
    demonstrate_model_validation_best_practices()
    
    print("\n=== 行业发展趋势 ===")
    
    trends = [
        "机器学习技术在量化投资中的深入应用",
        "替代数据（卫星数据、社交媒体等）的兴起",
        "高频交易和算法交易的发展",
        "ESG投资理念的融入",
        "监管科技（RegTech）的应用",
        "云计算和分布式计算的普及",
        "人工智能在风险管理中的应用"
    ]
    
    print("当前量化投资行业发展趋势:")
    for i, trend in enumerate(trends, 1):
        print(f"  {i}. {trend}")
    
    print("\n=== 总结建议 ===")
    
    recommendations = [
        "建立完整的数据治理体系",
        "实施严格的模型验证流程",
        "注重风险管理和风控体系建设",
        "保持对新技术和新方法的学习",
        "建立标准化的开发和部署流程",
        "重视团队建设和人才培养",
        "关注监管变化和合规要求"
    ]
    
    print("量化投资成功的关键建议:")
    for i, rec in enumerate(recommendations, 1):
        print(f"  {i}. {rec}")
    
    print("\n最佳实践与案例分析演示完成！")

if __name__ == "__main__":
    main()