# Qlib Advanced Demo Scripts (Chapters 5-11)

This directory contains comprehensive demo scripts based on chapters 5-11 of the Qlib documentation. Each chapter has been enhanced with complete, runnable examples that demonstrate key concepts and best practices.

## Directory Structure

```
demo/
├── chap05/                              # Chapter 5: Investment Strategy Design
│   ├── strategy_framework_demo.py       # Enhanced strategy framework demo
├── chap06/                              # Chapter 6: Backtesting System  
│   ├── backtest_system_demo.py          # Original backtest demo
│   └── advanced_backtest_demo.py        # NEW: Comprehensive backtest system
├── chap07/                              # Chapter 7: Reinforcement Learning
│   ├── reinforcement_learning_demo.py   # Original RL demo
│   └── reinforcement_learning_advanced_demo.py  # NEW: Advanced RL implementation
├── chap09/                              # Chapter 9: Online Services
│   ├── online_service_demo.py           # Original online service demo  
│   └── online_service_advanced_demo.py  # NEW: Production-ready online system
├── chap10/                              # Chapter 10: Complete Project
│   └── complete_project_demo.py         # Enhanced complete project workflow
└── chap11/                              # Chapter 11: Best Practices
    ├── best_practices_demo.py           # Original best practices demo
    └── best_practices_advanced_demo.py  # NEW: Comprehensive best practices
```

## Chapter 5: Investment Strategy Design

**File**: `chap05/strategy_framework_demo.py`

### Enhanced Features:
- **StrategyParams**: Configurable strategy parameters using dataclass
- **MultiFactorStrategy**: Complete multi-factor model implementation
- **MomentumStrategy**: Advanced momentum strategy with multiple types
- **MeanReversionStrategy**: Statistical mean reversion implementation
- **StrategyEnsemble**: Strategy combination framework
- **StrategyFactory**: Factory pattern for strategy creation

### Key Components:
```python
# Strategy Configuration
params = StrategyParams(
    risk_degree=0.95,
    topk=50,
    max_weight=0.05
)

# Multi-Factor Strategy
strategy = MultiFactorStrategy(
    factors=['momentum', 'value', 'quality', 'size'],
    topk=40
)

# Strategy Ensemble
ensemble = StrategyEnsemble([strategy1, strategy2], weights=[0.6, 0.4])
```

## Chapter 6: Backtesting System

**File**: `chap06/advanced_backtest_demo.py`

### New Features:
- **EventDrivenBacktest**: Complete event-driven backtesting system
- **RiskController**: Comprehensive risk management
- **ReturnAnalyzer**: Advanced performance analysis
- **BacktestReportGenerator**: Automated report generation

### Key Components:
```python
# Event-Driven Backtesting
backtest = EventDrivenBacktest(initial_capital=100000)
backtest.add_event(MarketDataEvent(...))
backtest.process_events()

# Risk Control
risk_controller = RiskController(
    max_position_size=0.1,
    max_drawdown=0.2
)
```

## Chapter 7: Reinforcement Learning

**File**: `chap07/reinforcement_learning_advanced_demo.py`

### New Features:
- **TradingEnvironment**: Abstract trading environment base class
- **PortfolioOptimizationEnv**: RL environment for portfolio optimization  
- **OrderExecutionEnv**: RL environment for order execution
- **DQNAgent**: Deep Q-Network implementation
- **ActorCriticAgent**: Actor-Critic algorithm implementation

### Key Components:
```python
# Portfolio Optimization Environment
env = PortfolioOptimizationEnv(data, initial_capital=100000)

# DQN Agent
agent = DQNAgent(state_size=158, action_size=10)
agent.train(env)

# Actor-Critic Agent  
ac_agent = ActorCriticAgent(state_size=8, action_size=5)
```

## Chapter 9: Online Services

**File**: `chap09/online_service_advanced_demo.py`

### New Features:
- **RealTimeDataService**: Production-ready data streaming
- **ModelServiceAPI**: RESTful model serving API
- **EventHandler**: Event-driven processing system
- **RealTimeTradingSystem**: Complete real-time trading system
- **HighAvailabilitySystem**: High availability and fault tolerance

### Key Components:
```python
# Real-time Data Service
data_service = RealTimeDataService(['wss://data-feed.com'])
data_service.subscribe(callback_function)

# Model API Service
model_api = ModelServiceAPI()
model_api.setup_routes()

# Real-time Trading System
trading_system = RealTimeTradingSystem()
trading_system.start()
```

## Chapter 10: Complete Project Implementation

**File**: `chap10/complete_project_demo.py` (Enhanced)

### Enhanced Features:
- **ProjectRequirementAnalyzer**: Comprehensive project planning
- **DataPreparation**: Advanced data preparation pipeline
- **ModelSelection**: Multi-model comparison framework
- **StrategyDevelopment**: Systematic strategy development
- **BacktestSystem**: Integrated backtesting system

### Key Components:
```python
# Project Requirements Analysis
analyzer = ProjectRequirementAnalyzer()
plan = analyzer.generate_development_plan()

# Model Selection Pipeline
model_selector = ModelSelection(dataset)
model_selector.register_model('LightGBM', LGBModel, **params)
best_model = model_selector.select_best_model()
```

## Chapter 11: Best Practices

**File**: `chap11/best_practices_advanced_demo.py`

### New Features:
- **Error Handling System**: Comprehensive exception management
- **Logging Framework**: Professional logging and monitoring
- **Alpha158MultiFactorModel**: Production-ready multi-factor model
- **LSTMPredictor**: Deep learning model with best practices
- **ComprehensiveDemo**: Integrated demonstration system

### Key Components:
```python
# Error Handling
@error_handler(DataError)
def process_data(data):
    if data.empty:
        raise DataError("Data is empty")

# Logging
@log_performance
def train_model(self):
    logger.info("Starting model training")

# LSTM Model
lstm_model = LSTMPredictor(input_size=158, hidden_size=64)
training_system = LSTMTrainingSystem(lstm_model)
```

## Installation and Setup

### Prerequisites

1. **Qlib Installation**: Make sure Qlib is properly installed and initialized
```bash
pip install pyqlib
```

2. **Data Setup**: Download and prepare Qlib data
```bash
python scripts/get_data.py qlib_data --target_dir ~/.qlib/qlib_data/cn_data --region cn
```

3. **Compile Extensions**: If running from source, compile the C++ extensions
```bash
make prerequisite
```

### Additional Dependencies

Some demos require additional packages:

```bash
# For reinforcement learning (Chapter 7)
pip install torch torchvision

# For online services (Chapter 9)  
pip install aiohttp websockets redis flask

# For advanced features
pip install scikit-learn scipy matplotlib seaborn
```

## Running the Demos

### Basic Execution
```bash
# Run Chapter 5 strategy demo
python demo/chap05/strategy_framework_demo.py

# Run Chapter 6 advanced backtest
python demo/chap06/advanced_backtest_demo.py

# Run Chapter 11 best practices
python demo/chap11/best_practices_advanced_demo.py
```

### Interactive Jupyter Notebooks
For interactive exploration, you can convert the scripts to Jupyter notebooks:
```bash
# Convert Python script to notebook
jupyter nbconvert --to notebook demo/chap05/strategy_framework_demo.py
```

## Demo Features

### Common Features Across All Demos:
1. **Error Handling**: Robust exception management
2. **Logging**: Comprehensive logging and monitoring
3. **Configuration**: Flexible parameter configuration
4. **Documentation**: Detailed docstrings and comments
5. **Best Practices**: Following Python and Qlib conventions

### Advanced Features:
1. **Modular Design**: Clean separation of concerns
2. **Design Patterns**: Factory, Strategy, Observer patterns
3. **Performance Monitoring**: Execution time tracking
4. **Memory Management**: Efficient data handling
5. **Extensibility**: Easy to extend and customize

## Troubleshooting

### Common Issues:

1. **Import Errors**: Make sure Qlib is properly installed and compiled
2. **Data Issues**: Ensure Qlib data is downloaded and accessible
3. **Memory Issues**: Use smaller datasets for testing
4. **Performance Issues**: Enable multiprocessing where available

### Debug Mode:
Enable debug logging for troubleshooting:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Contributing

When adding new demos or enhancing existing ones:

1. Follow the established code structure
2. Include comprehensive error handling
3. Add detailed logging and documentation
4. Test with different data configurations
5. Follow PEP 8 style guidelines

## References

- [Qlib Documentation](https://qlib.readthedocs.io/)
- [Qlib GitHub Repository](https://github.com/microsoft/qlib)
- [Quantitative Finance Best Practices](https://www.quantstart.com/)

## License

These demo scripts follow the same license as the main Qlib project.