# 多用户量化投资平台 - 项目启动指南

## 1. 环境准备

### 1.1 系统要求
- **操作系统**: macOS 10.15+ / Ubuntu 18.04+ / Windows 10+
- **Python版本**: 3.8 - 3.11
- **内存**: 最低8GB，推荐16GB+
- **存储**: 最低50GB可用空间
- **网络**: 稳定的互联网连接

### 1.2 必需软件
- **Python**: 3.8+
- **Node.js**: 16+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Git**: 2.30+

### 1.3 推荐开发工具
- **IDE**: PyCharm / VS Code
- **数据库工具**: DBeaver / pgAdmin
- **API测试**: Postman / Insomnia
- **版本控制**: Git

## 2. 项目结构

```
qlib-platform/
├── backend/                 # 后端服务
│   ├── app/
│   │   ├── api/            # API路由
│   │   ├── core/           # 核心配置
│   │   ├── models/         # 数据模型
│   │   ├── services/       # 业务逻辑
│   │   ├── utils/          # 工具函数
│   │   └── main.py         # 应用入口
│   ├── requirements.txt     # Python依赖
│   ├── Dockerfile          # Docker镜像
│   └── alembic/            # 数据库迁移
├── frontend/               # 前端应用
│   ├── src/
│   │   ├── components/     # Vue组件
│   │   ├── views/          # 页面视图
│   │   ├── router/         # 路由配置
│   │   ├── store/          # 状态管理
│   │   └── utils/          # 工具函数
│   ├── package.json        # Node.js依赖
│   └── Dockerfile          # Docker镜像
├── qlib/                   # Qlib引擎集成
│   ├── services/           # Qlib服务封装
│   ├── models/             # 模型定义
│   └── utils/              # 工具函数
├── docker-compose.yml      # 容器编排
├── .env.example           # 环境变量示例
├── README.md              # 项目说明
└── docs/                  # 项目文档
```

## 3. 快速启动

### 3.1 克隆项目
```bash
git clone https://github.com/your-org/qlib-platform.git
cd qlib-platform
```

### 3.2 环境变量配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

**主要环境变量**:
```bash
# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/qlib_platform
REDIS_URL=redis://localhost:6379

# JWT配置
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Qlib配置
QLIB_DATA_PATH=/app/data/qlib_data

# 文件存储
UPLOAD_DIR=/app/uploads

# 任务队列
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
```

### 3.3 使用Docker启动
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f api
```

### 3.4 本地开发启动

#### 3.4.1 后端服务
```bash
# 进入后端目录
cd backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # macOS/Linux
# venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 初始化数据库
alembic upgrade head

# 启动开发服务器
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 3.4.2 前端服务
```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

#### 3.4.3 启动Redis和PostgreSQL
```bash
# 使用Docker启动数据库服务
docker-compose up -d db redis

# 或者本地安装
# PostgreSQL: https://www.postgresql.org/download/
# Redis: https://redis.io/download
```

## 4. 开发流程

### 4.1 代码规范

#### 4.1.1 Python代码规范
- **代码风格**: 遵循PEP 8
- **类型注解**: 使用typing模块
- **文档字符串**: 使用Google风格
- **测试覆盖**: 最低80%

```python
# 示例代码
from typing import List, Optional
from pydantic import BaseModel

class UserCreate(BaseModel):
    """用户创建模型"""
    username: str
    email: str
    password: str

def create_user(user_data: UserCreate) -> Optional[User]:
    """
    创建新用户
    
    Args:
        user_data: 用户数据
        
    Returns:
        创建的用户对象，失败返回None
    """
    try:
        # 业务逻辑
        pass
    except Exception as e:
        logger.error(f"创建用户失败: {e}")
        return None
```

#### 4.1.2 JavaScript/Vue代码规范
- **代码风格**: 遵循ESLint配置
- **组件命名**: PascalCase
- **文件命名**: kebab-case
- **TypeScript**: 推荐使用

```typescript
// 示例组件
<template>
  <div class="user-profile">
    <h1>{{ user.name }}</h1>
    <p>{{ user.email }}</p>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'

interface User {
  id: number
  name: string
  email: string
}

export default defineComponent({
  name: 'UserProfile',
  setup() {
    const user = ref<User>({
      id: 1,
      name: '测试用户',
      email: '<EMAIL>'
    })

    return {
      user
    }
  }
})
</script>
```

### 4.2 Git工作流

#### 4.2.1 分支策略
```
main                    # 主分支，生产环境
├── develop            # 开发分支
├── feature/xxx        # 功能分支
├── bugfix/xxx         # 修复分支
└── hotfix/xxx         # 紧急修复分支
```

#### 4.2.2 提交规范
```
feat: 添加新功能
fix: 修复bug
docs: 更新文档
style: 代码格式调整
refactor: 代码重构
test: 添加测试
chore: 构建过程或辅助工具的变动
```

#### 4.2.3 开发流程
```bash
# 1. 创建功能分支
git checkout -b feature/user-management

# 2. 开发代码
# ... 编写代码 ...

# 3. 提交代码
git add .
git commit -m "feat: 添加用户管理功能"

# 4. 推送到远程
git push origin feature/user-management

# 5. 创建Pull Request
# 在GitHub上创建PR，等待代码审查
```

### 4.3 测试策略

#### 4.3.1 单元测试
```python
# tests/test_user_service.py
import pytest
from app.services.user_service import UserService

class TestUserService:
    def test_create_user_success(self):
        """测试成功创建用户"""
        service = UserService()
        user_data = {
            "username": "test_user",
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        result = service.create_user(user_data)
        
        assert result is not None
        assert result.username == "test_user"
        assert result.email == "<EMAIL>"

    def test_create_user_duplicate_username(self):
        """测试创建重复用户名"""
        service = UserService()
        user_data = {
            "username": "existing_user",
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        with pytest.raises(ValueError):
            service.create_user(user_data)
```

#### 4.3.2 集成测试
```python
# tests/test_api.py
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_create_user_api():
    """测试用户创建API"""
    response = client.post(
        "/api/v1/auth/register",
        json={
            "username": "test_user",
            "email": "<EMAIL>",
            "password": "password123",
            "confirm_password": "password123"
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 200
    assert "user_id" in data["data"]
```

#### 4.3.3 前端测试
```typescript
// tests/components/UserProfile.test.ts
import { mount } from '@vue/test-utils'
import UserProfile from '@/components/UserProfile.vue'

describe('UserProfile', () => {
  it('displays user information correctly', () => {
    const user = {
      id: 1,
      name: '测试用户',
      email: '<EMAIL>'
    }
    
    const wrapper = mount(UserProfile, {
      props: { user }
    })
    
    expect(wrapper.text()).toContain('测试用户')
    expect(wrapper.text()).toContain('<EMAIL>')
  })
})
```

## 5. 部署指南

### 5.1 开发环境部署
```bash
# 1. 启动所有服务
docker-compose up -d

# 2. 初始化数据库
docker-compose exec api alembic upgrade head

# 3. 创建管理员用户
docker-compose exec api python -m app.scripts.create_admin

# 4. 访问应用
# 前端: http://localhost:3000
# API文档: http://localhost:8000/docs
```

### 5.2 生产环境部署
```bash
# 1. 构建生产镜像
docker-compose -f docker-compose.prod.yml build

# 2. 启动生产服务
docker-compose -f docker-compose.prod.yml up -d

# 3. 配置Nginx反向代理
# 参考: nginx/nginx.conf

# 4. 配置SSL证书
# 使用Let's Encrypt或其他SSL证书
```

## 6. 监控和日志

### 6.1 日志配置
```python
# backend/app/core/logging.py
import logging
from loguru import logger

# 配置日志格式
logger.add(
    "logs/app.log",
    rotation="100 MB",
    retention="30 days",
    level="INFO",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
)
```

### 6.2 性能监控
```python
# backend/app/core/monitoring.py
from prometheus_client import Counter, Histogram
import time

# 定义指标
REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests')
REQUEST_LATENCY = Histogram('http_request_duration_seconds', 'HTTP request latency')

# 中间件
@app.middleware("http")
async def monitor_requests(request: Request, call_next):
    start_time = time.time()
    
    response = await call_next(request)
    
    REQUEST_COUNT.inc()
    REQUEST_LATENCY.observe(time.time() - start_time)
    
    return response
```

## 7. 常见问题

### 7.1 数据库连接问题
```bash
# 检查数据库状态
docker-compose exec db psql -U user -d qlib_platform

# 重置数据库
docker-compose down -v
docker-compose up -d db
docker-compose exec api alembic upgrade head
```

### 7.2 Redis连接问题
```bash
# 检查Redis状态
docker-compose exec redis redis-cli ping

# 清空Redis缓存
docker-compose exec redis redis-cli flushall
```

### 7.3 Qlib数据问题
```bash
# 下载Qlib数据
python -m qlib.run.get_data qlib_data --target_dir ~/.qlib/qlib_data/cn_data --region cn

# 检查数据健康状态
python scripts/check_data_health.py check_data --qlib_dir ~/.qlib/qlib_data/cn_data
```

### 7.4 前端构建问题
```bash
# 清理缓存
npm run clean

# 重新安装依赖
rm -rf node_modules package-lock.json
npm install

# 重新构建
npm run build
```

## 8. 开发工具推荐

### 8.1 VS Code扩展
- **Python**: Python, Pylance
- **Vue**: Vetur, Vue Language Features
- **Git**: GitLens
- **Docker**: Docker
- **数据库**: SQLTools

### 8.2 有用的命令
```bash
# 查看服务日志
docker-compose logs -f [service_name]

# 进入容器
docker-compose exec [service_name] bash

# 数据库迁移
docker-compose exec api alembic revision --autogenerate -m "description"
docker-compose exec api alembic upgrade head

# 运行测试
docker-compose exec api pytest
npm run test:unit

# 代码格式化
black backend/
npm run lint:fix
```

## 9. 下一步

1. **熟悉代码结构**: 阅读主要模块的代码
2. **运行测试**: 确保所有测试通过
3. **查看文档**: 阅读API文档和用户手册
4. **开始开发**: 选择一个功能模块开始开发
5. **参与讨论**: 加入项目讨论群或论坛

## 10. 联系方式

- **项目地址**: https://github.com/your-org/qlib-platform
- **问题反馈**: https://github.com/your-org/qlib-platform/issues
- **技术讨论**: https://github.com/your-org/qlib-platform/discussions
- **邮箱**: <EMAIL>

---

**祝您开发愉快！** 🚀 