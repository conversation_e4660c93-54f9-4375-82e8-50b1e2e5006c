# 多用户量化投资平台 - 数据库设计文档

## 1. 数据库概述

### 1.1 数据库选择
- **主数据库**: PostgreSQL 13+
- **缓存数据库**: Redis 6+
- **文件存储**: MinIO/S3兼容存储

### 1.2 设计原则
- **规范化**: 遵循第三范式
- **性能优化**: 合理使用索引
- **扩展性**: 支持水平扩展
- **安全性**: 数据加密和访问控制

## 2. 数据库表结构

### 2.1 用户管理模块

#### 2.1.1 用户表 (users)
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    nickname VARCHAR(100),
    avatar_url VARCHAR(255),
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_created_at ON users(created_at);
```

#### 2.1.2 用户配额表 (user_quotas)
```sql
CREATE TABLE user_quotas (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    quota_type VARCHAR(50) NOT NULL, -- 'data_usage', 'compute_time', 'storage', 'api_calls'
    current_usage DECIMAL(15,2) DEFAULT 0,
    max_usage DECIMAL(15,2),
    reset_period VARCHAR(20), -- 'daily', 'monthly', 'yearly'
    reset_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, quota_type)
);

-- 索引
CREATE INDEX idx_user_quotas_user_id ON user_quotas(user_id);
CREATE INDEX idx_user_quotas_type ON user_quotas(quota_type);
```

#### 2.1.3 用户角色表 (user_roles)
```sql
CREATE TABLE user_roles (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_name VARCHAR(50) NOT NULL, -- 'admin', 'premium', 'basic'
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    granted_by INTEGER REFERENCES users(id),
    expires_at TIMESTAMP,
    UNIQUE(user_id, role_name)
);

-- 索引
CREATE INDEX idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX idx_user_roles_role_name ON user_roles(role_name);
```

### 2.2 数据管理模块

#### 2.2.1 数据源表 (data_sources)
```sql
CREATE TABLE data_sources (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    source_type VARCHAR(50) NOT NULL, -- 'stock', 'future', 'option', 'index'
    market VARCHAR(20) NOT NULL, -- 'cn', 'us', 'hk'
    data_frequency VARCHAR(20), -- '1min', '5min', '1d', '1w'
    is_active BOOLEAN DEFAULT TRUE,
    last_update_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_data_sources_type ON data_sources(source_type);
CREATE INDEX idx_data_sources_market ON data_sources(market);
```

#### 2.2.2 数据字段表 (data_fields)
```sql
CREATE TABLE data_fields (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL, -- 'price', 'volume', 'indicator', 'fundamental'
    data_type VARCHAR(20) NOT NULL, -- 'float', 'integer', 'string', 'datetime'
    unit VARCHAR(20),
    is_available BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_data_fields_category ON data_fields(category);
CREATE INDEX idx_data_fields_name ON data_fields(name);
```

#### 2.2.3 数据订阅表 (data_subscriptions)
```sql
CREATE TABLE data_subscriptions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    source_id INTEGER NOT NULL REFERENCES data_sources(id),
    instruments TEXT[], -- 订阅的股票代码列表
    fields TEXT[], -- 订阅的字段列表
    start_date DATE,
    end_date DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_data_subscriptions_user_id ON data_subscriptions(user_id);
CREATE INDEX idx_data_subscriptions_source_id ON data_subscriptions(source_id);
```

### 2.3 策略管理模块

#### 2.3.1 策略表 (strategies)
```sql
CREATE TABLE strategies (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    strategy_type VARCHAR(50) NOT NULL, -- 'alpha', 'portfolio', 'execution'
    code TEXT NOT NULL, -- 策略代码
    config JSONB, -- 策略配置
    status VARCHAR(20) DEFAULT 'draft', -- 'draft', 'active', 'paused', 'deleted'
    is_public BOOLEAN DEFAULT FALSE,
    tags TEXT[],
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_strategies_user_id ON strategies(user_id);
CREATE INDEX idx_strategies_type ON strategies(strategy_type);
CREATE INDEX idx_strategies_status ON strategies(status);
CREATE INDEX idx_strategies_created_at ON strategies(created_at);
```

#### 2.3.2 策略版本表 (strategy_versions)
```sql
CREATE TABLE strategy_versions (
    id SERIAL PRIMARY KEY,
    strategy_id INTEGER NOT NULL REFERENCES strategies(id) ON DELETE CASCADE,
    version VARCHAR(20) NOT NULL,
    code TEXT NOT NULL,
    config JSONB,
    change_log TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(strategy_id, version)
);

-- 索引
CREATE INDEX idx_strategy_versions_strategy_id ON strategy_versions(strategy_id);
```

#### 2.3.3 回测记录表 (backtests)
```sql
CREATE TABLE backtests (
    id SERIAL PRIMARY KEY,
    strategy_id INTEGER NOT NULL REFERENCES strategies(id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    config JSONB NOT NULL, -- 回测配置
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'running', 'completed', 'failed'
    progress DECIMAL(5,2) DEFAULT 0, -- 进度百分比
    results JSONB, -- 回测结果
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP,
    completed_at TIMESTAMP
);

-- 索引
CREATE INDEX idx_backtests_strategy_id ON backtests(strategy_id);
CREATE INDEX idx_backtests_user_id ON backtests(user_id);
CREATE INDEX idx_backtests_status ON backtests(status);
CREATE INDEX idx_backtests_created_at ON backtests(created_at);
```

#### 2.3.4 回测结果详情表 (backtest_results)
```sql
CREATE TABLE backtest_results (
    id SERIAL PRIMARY KEY,
    backtest_id INTEGER NOT NULL REFERENCES backtests(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    portfolio_value DECIMAL(15,2),
    cash DECIMAL(15,2),
    positions_value DECIMAL(15,2),
    daily_return DECIMAL(10,6),
    cumulative_return DECIMAL(10,6),
    benchmark_return DECIMAL(10,6),
    benchmark_cumulative_return DECIMAL(10,6),
    positions JSONB, -- 持仓详情
    trades JSONB, -- 交易记录
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_backtest_results_backtest_id ON backtest_results(backtest_id);
CREATE INDEX idx_backtest_results_date ON backtest_results(date);
```

### 2.4 模型管理模块

#### 2.4.1 模型表 (models)
```sql
CREATE TABLE models (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    model_type VARCHAR(50) NOT NULL, -- 'lightgbm', 'xgboost', 'lstm', 'transformer'
    config JSONB NOT NULL, -- 模型配置
    status VARCHAR(20) DEFAULT 'training', -- 'training', 'trained', 'deployed', 'failed'
    metrics JSONB, -- 模型指标
    model_path VARCHAR(255), -- 模型文件路径
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_models_user_id ON models(user_id);
CREATE INDEX idx_models_type ON models(model_type);
CREATE INDEX idx_models_status ON models(status);
```

#### 2.4.2 模型版本表 (model_versions)
```sql
CREATE TABLE model_versions (
    id SERIAL PRIMARY KEY,
    model_id INTEGER NOT NULL REFERENCES models(id) ON DELETE CASCADE,
    version VARCHAR(20) NOT NULL,
    config JSONB,
    metrics JSONB,
    model_path VARCHAR(255),
    is_deployed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(model_id, version)
);

-- 索引
CREATE INDEX idx_model_versions_model_id ON model_versions(model_id);
```

#### 2.4.3 模型预测表 (model_predictions)
```sql
CREATE TABLE model_predictions (
    id SERIAL PRIMARY KEY,
    model_id INTEGER NOT NULL REFERENCES models(id) ON DELETE CASCADE,
    instrument VARCHAR(20) NOT NULL,
    date DATE NOT NULL,
    prediction DECIMAL(10,6),
    confidence DECIMAL(10,6),
    features JSONB, -- 特征数据
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_model_predictions_model_id ON model_predictions(model_id);
CREATE INDEX idx_model_predictions_instrument ON model_predictions(instrument);
CREATE INDEX idx_model_predictions_date ON model_predictions(date);
```

### 2.5 交易管理模块

#### 2.5.1 投资组合表 (portfolios)
```sql
CREATE TABLE portfolios (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    initial_capital DECIMAL(15,2) NOT NULL,
    current_capital DECIMAL(15,2),
    cash DECIMAL(15,2),
    total_value DECIMAL(15,2),
    status VARCHAR(20) DEFAULT 'active', -- 'active', 'paused', 'closed'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_portfolios_user_id ON portfolios(user_id);
CREATE INDEX idx_portfolios_status ON portfolios(status);
```

#### 2.5.2 持仓表 (positions)
```sql
CREATE TABLE positions (
    id SERIAL PRIMARY KEY,
    portfolio_id INTEGER NOT NULL REFERENCES portfolios(id) ON DELETE CASCADE,
    instrument VARCHAR(20) NOT NULL,
    quantity INTEGER NOT NULL,
    avg_price DECIMAL(10,4) NOT NULL,
    current_price DECIMAL(10,4),
    market_value DECIMAL(15,2),
    unrealized_pnl DECIMAL(15,2),
    unrealized_pnl_pct DECIMAL(10,6),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(portfolio_id, instrument)
);

-- 索引
CREATE INDEX idx_positions_portfolio_id ON positions(portfolio_id);
CREATE INDEX idx_positions_instrument ON positions(instrument);
```

#### 2.5.3 订单表 (orders)
```sql
CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    portfolio_id INTEGER NOT NULL REFERENCES portfolios(id) ON DELETE CASCADE,
    strategy_id INTEGER REFERENCES strategies(id),
    instrument VARCHAR(20) NOT NULL,
    side VARCHAR(10) NOT NULL, -- 'buy', 'sell'
    order_type VARCHAR(20) NOT NULL, -- 'market', 'limit', 'stop'
    quantity INTEGER NOT NULL,
    price DECIMAL(10,4),
    filled_quantity INTEGER DEFAULT 0,
    filled_price DECIMAL(10,4),
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'filled', 'cancelled', 'rejected'
    commission DECIMAL(10,4) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    filled_at TIMESTAMP,
    cancelled_at TIMESTAMP
);

-- 索引
CREATE INDEX idx_orders_portfolio_id ON orders(portfolio_id);
CREATE INDEX idx_orders_strategy_id ON orders(strategy_id);
CREATE INDEX idx_orders_instrument ON orders(instrument);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
```

#### 2.5.4 交易记录表 (trades)
```sql
CREATE TABLE trades (
    id SERIAL PRIMARY KEY,
    order_id INTEGER NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    instrument VARCHAR(20) NOT NULL,
    side VARCHAR(10) NOT NULL,
    quantity INTEGER NOT NULL,
    price DECIMAL(10,4) NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    commission DECIMAL(10,4) DEFAULT 0,
    trade_time TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_trades_order_id ON trades(order_id);
CREATE INDEX idx_trades_instrument ON trades(instrument);
CREATE INDEX idx_trades_trade_time ON trades(trade_time);
```

### 2.6 系统管理模块

#### 2.6.1 系统配置表 (system_configs)
```sql
CREATE TABLE system_configs (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_system_configs_key ON system_configs(config_key);
```

#### 2.6.2 任务表 (tasks)
```sql
CREATE TABLE tasks (
    id SERIAL PRIMARY KEY,
    task_id VARCHAR(100) UNIQUE NOT NULL,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    task_type VARCHAR(50) NOT NULL, -- 'backtest', 'model_training', 'data_import'
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'running', 'completed', 'failed'
    progress DECIMAL(5,2) DEFAULT 0,
    config JSONB,
    result JSONB,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP,
    completed_at TIMESTAMP
);

-- 索引
CREATE INDEX idx_tasks_user_id ON tasks(user_id);
CREATE INDEX idx_tasks_type ON tasks(task_type);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_created_at ON tasks(created_at);
```

#### 2.6.3 操作日志表 (operation_logs)
```sql
CREATE TABLE operation_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    operation_type VARCHAR(50) NOT NULL, -- 'login', 'logout', 'create', 'update', 'delete'
    resource_type VARCHAR(50), -- 'strategy', 'model', 'order'
    resource_id VARCHAR(100),
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_operation_logs_user_id ON operation_logs(user_id);
CREATE INDEX idx_operation_logs_type ON operation_logs(operation_type);
CREATE INDEX idx_operation_logs_created_at ON operation_logs(created_at);
```

## 3. 数据库视图

### 3.1 用户统计视图
```sql
CREATE VIEW user_statistics AS
SELECT 
    u.id,
    u.username,
    u.email,
    u.created_at,
    COUNT(DISTINCT s.id) as strategy_count,
    COUNT(DISTINCT m.id) as model_count,
    COUNT(DISTINCT b.id) as backtest_count,
    COUNT(DISTINCT p.id) as portfolio_count
FROM users u
LEFT JOIN strategies s ON u.id = s.user_id
LEFT JOIN models m ON u.id = m.user_id
LEFT JOIN backtests b ON u.id = b.user_id
LEFT JOIN portfolios p ON u.id = p.user_id
GROUP BY u.id, u.username, u.email, u.created_at;
```

### 3.2 策略性能视图
```sql
CREATE VIEW strategy_performance AS
SELECT 
    s.id,
    s.name,
    s.strategy_type,
    s.user_id,
    COUNT(b.id) as backtest_count,
    AVG((b.results->>'total_return')::DECIMAL) as avg_total_return,
    AVG((b.results->>'sharpe_ratio')::DECIMAL) as avg_sharpe_ratio,
    AVG((b.results->>'max_drawdown')::DECIMAL) as avg_max_drawdown
FROM strategies s
LEFT JOIN backtests b ON s.id = b.strategy_id AND b.status = 'completed'
GROUP BY s.id, s.name, s.strategy_type, s.user_id;
```

### 3.3 投资组合概览视图
```sql
CREATE VIEW portfolio_overview AS
SELECT 
    p.id,
    p.name,
    p.user_id,
    p.initial_capital,
    p.current_capital,
    p.cash,
    p.total_value,
    COALESCE(SUM(pos.market_value), 0) as positions_value,
    COALESCE(SUM(pos.unrealized_pnl), 0) as total_unrealized_pnl,
    CASE 
        WHEN p.initial_capital > 0 
        THEN (p.total_value - p.initial_capital) / p.initial_capital 
        ELSE 0 
    END as total_return_pct
FROM portfolios p
LEFT JOIN positions pos ON p.id = pos.portfolio_id
GROUP BY p.id, p.name, p.user_id, p.initial_capital, p.current_capital, p.cash, p.total_value;
```

## 4. 存储过程

### 4.1 更新投资组合价值
```sql
CREATE OR REPLACE FUNCTION update_portfolio_value(portfolio_id_param INTEGER)
RETURNS VOID AS $$
BEGIN
    UPDATE portfolios 
    SET 
        total_value = cash + COALESCE(
            (SELECT SUM(market_value) FROM positions WHERE portfolio_id = portfolio_id_param), 
            0
        ),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = portfolio_id_param;
END;
$$ LANGUAGE plpgsql;
```

### 4.2 计算持仓盈亏
```sql
CREATE OR REPLACE FUNCTION calculate_position_pnl()
RETURNS VOID AS $$
BEGIN
    UPDATE positions 
    SET 
        unrealized_pnl = (current_price - avg_price) * quantity,
        unrealized_pnl_pct = CASE 
            WHEN avg_price > 0 THEN (current_price - avg_price) / avg_price 
            ELSE 0 
        END,
        updated_at = CURRENT_TIMESTAMP
    WHERE current_price IS NOT NULL;
END;
$$ LANGUAGE plpgsql;
```

## 5. 触发器

### 5.1 自动更新时间戳
```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为需要自动更新时间的表创建触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_strategies_updated_at BEFORE UPDATE ON strategies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_models_updated_at BEFORE UPDATE ON models
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 5.2 订单状态变更日志
```sql
CREATE OR REPLACE FUNCTION log_order_status_change()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.status != NEW.status THEN
        INSERT INTO operation_logs (
            user_id, 
            operation_type, 
            resource_type, 
            resource_id, 
            details
        ) VALUES (
            (SELECT user_id FROM portfolios WHERE id = NEW.portfolio_id),
            'update',
            'order',
            NEW.id::TEXT,
            jsonb_build_object(
                'old_status', OLD.status,
                'new_status', NEW.status,
                'instrument', NEW.instrument,
                'quantity', NEW.quantity
            )
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER log_order_status_change AFTER UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION log_order_status_change();
```

## 6. 索引优化

### 6.1 复合索引
```sql
-- 策略查询优化
CREATE INDEX idx_strategies_user_type_status ON strategies(user_id, strategy_type, status);

-- 回测查询优化
CREATE INDEX idx_backtests_user_status_date ON backtests(user_id, status, created_at);

-- 订单查询优化
CREATE INDEX idx_orders_portfolio_status_date ON orders(portfolio_id, status, created_at);

-- 持仓查询优化
CREATE INDEX idx_positions_portfolio_instrument ON positions(portfolio_id, instrument);
```

### 6.2 部分索引
```sql
-- 只对活跃用户创建索引
CREATE INDEX idx_users_active ON users(id) WHERE is_active = TRUE;

-- 只对完成的回测创建索引
CREATE INDEX idx_backtests_completed ON backtests(id) WHERE status = 'completed';

-- 只对活跃策略创建索引
CREATE INDEX idx_strategies_active ON strategies(id) WHERE status = 'active';
```

## 7. 数据分区

### 7.1 按时间分区的表
```sql
-- 回测结果按时间分区
CREATE TABLE backtest_results_2024 PARTITION OF backtest_results
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');

CREATE TABLE backtest_results_2025 PARTITION OF backtest_results
FOR VALUES FROM ('2025-01-01') TO ('2026-01-01');

-- 交易记录按时间分区
CREATE TABLE trades_2024 PARTITION OF trades
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');

CREATE TABLE trades_2025 PARTITION OF trades
FOR VALUES FROM ('2025-01-01') TO ('2026-01-01');
```

## 8. 数据备份策略

### 8.1 备份脚本
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/postgresql"
DB_NAME="qlib_platform"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
pg_dump -h localhost -U postgres -d $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/backup_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +7 -delete

echo "Backup completed: backup_$DATE.sql.gz"
```

### 8.2 恢复脚本
```bash
#!/bin/bash
# restore.sh

BACKUP_FILE=$1
DB_NAME="qlib_platform"

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup_file>"
    exit 1
fi

# 解压备份文件
gunzip -c $BACKUP_FILE > temp_backup.sql

# 恢复数据库
psql -h localhost -U postgres -d $DB_NAME < temp_backup.sql

# 清理临时文件
rm temp_backup.sql

echo "Restore completed from $BACKUP_FILE"
```

## 9. 性能监控

### 9.1 慢查询监控
```sql
-- 启用慢查询日志
ALTER SYSTEM SET log_min_duration_statement = 1000; -- 1秒
ALTER SYSTEM SET log_statement = 'all';
SELECT pg_reload_conf();
```

### 9.2 表大小监控
```sql
-- 查看表大小
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

### 9.3 索引使用情况
```sql
-- 查看索引使用情况
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;
```

## 10. 安全配置

### 10.1 用户权限管理
```sql
-- 创建只读用户
CREATE USER readonly_user WITH PASSWORD 'password';
GRANT CONNECT ON DATABASE qlib_platform TO readonly_user;
GRANT USAGE ON SCHEMA public TO readonly_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO readonly_user;

-- 创建应用用户
CREATE USER app_user WITH PASSWORD 'password';
GRANT CONNECT ON DATABASE qlib_platform TO app_user;
GRANT USAGE ON SCHEMA public TO app_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO app_user;
```

### 10.2 数据加密
```sql
-- 启用行级安全
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- 创建策略
CREATE POLICY user_isolation ON users
    FOR ALL
    USING (id = current_setting('app.current_user_id')::INTEGER);
```

## 11. 总结

本数据库设计文档详细描述了多用户量化投资平台的数据库结构，包括：

1. **完整的表结构设计**：涵盖用户管理、数据管理、策略管理、模型管理、交易管理等核心模块
2. **性能优化策略**：合理的索引设计、分区策略、视图和存储过程
3. **安全配置**：用户权限管理、数据加密、审计日志
4. **监控和维护**：性能监控、备份恢复、慢查询分析

该设计遵循了数据库设计的最佳实践，确保了系统的可扩展性、安全性和性能。在实际部署时，可以根据具体的业务需求和性能要求进行适当的调整。 