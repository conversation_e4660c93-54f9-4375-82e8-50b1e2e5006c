# Qlib 项目部署指南

## 环境信息
- **操作系统**: macOS (Darwin 24.5.0)
- **架构**: ARM64 (Apple Silicon)
- **Python版本**: 3.10.18
- **包管理工具**: Conda + pip

## 部署步骤

### 1. 创建虚拟环境

```bash
# 创建conda虚拟环境
conda create -n qlib-env python=3.11 -y

# 激活环境
conda activate qlib-env

# 验证Python版本
python --version  # 应该显示 Python 3.10.18
```

### 2. 安装依赖

```bash
# 安装编译依赖
pip install numpy
pip install --upgrade cython

# 从源码安装Qlib (推荐方式)
cd /path/to/qlib  # 进入qlib项目目录
pip install -e .
```

### 3. 解决依赖问题

#### LightGBM问题 (ARM64 macOS)
在ARM64 macOS上，LightGBM可能存在OpenMP兼容性问题：

```bash
# 如果遇到LightGBM问题，可以选择卸载
pip uninstall lightgbm -y

# 或者尝试安装OpenMP (需要Homebrew)
brew install libomp
```

**注意**: 在ARM64 macOS上，某些预编译的LightGBM包可能不兼容。建议使用其他模型如LinearModel进行测试。

### 4. 下载数据

```bash
# 在Python中下载示例数据
python -c "
from qlib.tests.data import GetData
from qlib.constant import REG_CN

provider_uri = '~/.qlib/qlib_data/cn_data'
GetData().qlib_data(target_dir=provider_uri, region=REG_CN, exists_skip=True)
print('数据下载完成!')
"
```

### 5. 验证安装

```bash
# 验证Qlib安装
python -c "import qlib; print('Qlib version:', qlib.__version__)"

# 测试qrun命令
qrun --help

# 运行示例工作流
qrun examples/benchmarks/Linear/workflow_config_linear_Alpha158_csi500.yaml
```

## 成功运行示例

### 线性模型示例
```bash
qrun examples/benchmarks/Linear/workflow_config_linear_Alpha158_csi500.yaml
```

**运行结果**:
- IC: 0.021 (信息系数)
- ICIR: 0.164 (信息系数比率)
- 年化收益率: 2.33%
- 信息比率: 0.331
- 最大回撤: -10.13%

## 项目结构

```
qlib/
├── qlib/                    # 核心库
│   ├── data/               # 数据处理模块
│   ├── model/              # 模型模块
│   ├── strategy/           # 策略模块
│   ├── backtest/           # 回测模块
│   ├── workflow/           # 工作流模块
│   └── contrib/            # 贡献模块
├── examples/               # 示例代码
│   ├── benchmarks/         # 基准模型
│   └── workflow_by_code.py # 代码方式工作流
├── scripts/                # 脚本工具
└── tests/                  # 测试代码
```

## 可用模型

### 已验证可用的模型
- ✅ **LinearModel**: 线性回归模型
- ✅ **数据处理**: Alpha158特征集
- ✅ **回测系统**: 完整的回测流程

### 需要额外依赖的模型
- ⚠️ **LightGBM**: 需要解决OpenMP问题 (ARM64 macOS)
- ⚠️ **XGBoost**: 需要单独安装 `pip install xgboost`
- ⚠️ **CatBoost**: 需要单独安装 `pip install catboost`
- ⚠️ **PyTorch模型**: 需要安装PyTorch `pip install torch`

## 使用方式

### 1. 配置文件方式 (推荐)
```bash
# 使用YAML配置文件
qrun examples/benchmarks/Linear/workflow_config_linear_Alpha158_csi500.yaml
```

### 2. 代码方式
```python
import qlib
from qlib.constant import REG_CN
from qlib.utils import init_instance_by_config

# 初始化
qlib.init(provider_uri="~/.qlib/qlib_data/cn_data", region=REG_CN)

# 配置模型和数据集
config = {
    'model': {'class': 'LinearModel', 'module_path': 'qlib.contrib.model.linear'},
    'dataset': {...}  # 数据集配置
}

# 训练和预测
model = init_instance_by_config(config['model'])
dataset = init_instance_by_config(config['dataset'])
model.fit(dataset)
pred = model.predict(dataset)
```

## 常见问题

### 1. LightGBM导入错误
**错误**: `Library not loaded: @rpath/libomp.dylib`

**解决方案**:
```bash
# 方案1: 卸载LightGBM，使用其他模型
pip uninstall lightgbm -y

# 方案2: 安装OpenMP (如果使用Homebrew)
brew install libomp
```

### 2. 数据下载失败
**解决方案**:
- 检查网络连接
- 使用 `exists_skip=False` 强制重新下载
- 手动下载数据文件

### 3. 内存不足
**解决方案**:
- 减少数据时间范围
- 使用更少的特征
- 增加系统内存

## 性能优化

### 1. 数据缓存
```python
# Qlib会自动缓存处理过的数据
# 缓存位置: ~/.qlib/qlib_data/cn_data/features_cache/
```

### 2. 并行处理
```python
# 在配置中设置并行数
qlib.init(
    provider_uri="~/.qlib/qlib_data/cn_data",
    region=REG_CN,
    kernels=4  # 设置并行进程数
)
```

### 3. 内存管理
```python
# 清理内存缓存
from qlib.data.cache import H
H.clear()
```

## 扩展功能

### 1. 自定义模型
继承 `qlib.model.base.Model` 类实现自定义模型

### 2. 自定义策略
继承 `qlib.strategy.base.BaseStrategy` 类实现自定义策略

### 3. 自定义数据处理
实现自定义的数据处理器和特征工程

## 部署建议

### 开发环境
- 使用conda管理Python环境
- 启用代码热重载进行快速开发
- 使用Jupyter Notebook进行交互式开发

### 生产环境
- 使用Docker容器化部署
- 配置数据持久化存储
- 设置监控和日志系统
- 使用负载均衡处理高并发

## 总结

Qlib已成功部署并可以正常运行：

1. ✅ **环境搭建**: Python 3.10 + Conda环境
2. ✅ **核心功能**: 数据处理、模型训练、回测分析
3. ✅ **示例运行**: 线性模型完整工作流程
4. ✅ **命令行工具**: qrun工具正常工作

**下一步建议**:
- 安装额外的机器学习库 (XGBoost, PyTorch等)
- 探索更多模型和策略
- 开发自定义功能模块
- 集成到生产环境 