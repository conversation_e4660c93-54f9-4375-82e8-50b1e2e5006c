# Alpha158和Alpha360因子计算公式文档

## 概述

本文档详细描述了Qlib量化投资平台中Alpha158和Alpha360数据集的因子计算公式。这些因子集被广泛用于股票预测模型的训练和评估。

## Alpha158因子集

Alpha158包含158个技术分析指标，分为四大类：K线特征、价格特征、成交量特征和滚动窗口技术指标。

### 1. K线基础特征 (9个)

K线基础特征基于单日OHLC数据计算，反映价格形态特征：

| 因子名称 | 计算公式 | 含义 |
|---------|---------|------|
| KMID | `($close-$open)/$open` | 收盘与开盘价差比例，反映日内涨跌幅 |
| KLEN | `($high-$low)/$open` | 最高最低价差比例，反映日内波动幅度 |
| KMID2 | `($close-$open)/($high-$low+1e-12)` | 收开差与振幅比，反映实体占比 |
| KUP | `($high-Greater($open, $close))/$open` | 上影线比例，反映上方压力 |
| KUP2 | `($high-Greater($open, $close))/($high-$low+1e-12)` | 上影线占总振幅比例 |
| KLOW | `(Less($open, $close)-$low)/$open` | 下影线比例，反映下方支撑 |
| KLOW2 | `(Less($open, $close)-$low)/($high-$low+1e-12)` | 下影线占总振幅比例 |
| KSFT | `(2*$close-$high-$low)/$open` | 收盘价相对于中点位置 |
| KSFT2 | `(2*$close-$high-$low)/($high-$low+1e-12)` | 收盘价在振幅中的相对位置 |

### 2. 价格特征 (4个)

基础价格特征，标准化为相对当前收盘价的比值：

| 因子名称 | 计算公式 | 含义 |
|---------|---------|------|
| OPEN0 | `$open/$close` | 当日开盘价相对收盘价比值 |
| HIGH0 | `$high/$close` | 当日最高价相对收盘价比值 |
| LOW0 | `$low/$close` | 当日最低价相对收盘价比值 |
| VWAP0 | `$vwap/$close` | 当日成交均价相对收盘价比值 |

### 3. 滚动窗口技术指标 (145个)

使用5,10,20,30,60天滚动窗口计算的技术指标，每个指标生成5个特征，共29类指标。

#### 3.1 价格趋势与动量类

| 因子类别 | 计算公式 | 含义 | 参考链接 |
|---------|---------|------|----------|
| ROC | `Ref($close, d)/$close` | d日前价格变化率 | [Rate of Change](https://www.investopedia.com/terms/r/rateofchange.asp) |
| MA | `Mean($close, d)/$close` | d日简单移动平均 | [Moving Average](https://www.investopedia.com/ask/answers/071414/whats-difference-between-moving-average-and-weighted-moving-average.asp) |
| STD | `Std($close, d)/$close` | d日价格标准差，反映波动性 | - |
| BETA | `Slope($close, d)/$close` | d日价格趋势斜率 | - |
| RSQR | `Rsquare($close, d)` | d日线性回归R²值，反映趋势线性度 | - |
| RESI | `Resi($close, d)/$close` | d日线性回归残差 | - |

#### 3.2 价格位置类

| 因子类别 | 计算公式 | 含义 |
|---------|---------|------|
| MAX | `Max($high, d)/$close` | d日内最高价 |
| MIN | `Min($low, d)/$close` | d日内最低价 |
| QTLU | `Quantile($close, d, 0.8)/$close` | d日80%分位数 |
| QTLD | `Quantile($close, d, 0.2)/$close` | d日20%分位数 |
| RANK | `Rank($close, d)` | 当前价格在d日内排名百分位 |
| RSV | `($close-Min($low, d))/(Max($high, d)-Min($low, d)+1e-12)` | 相对强弱值，KDJ指标基础 |

#### 3.3 时间序列位置类

| 因子类别 | 计算公式 | 含义 | 参考链接 |
|---------|---------|------|----------|
| IMAX | `IdxMax($high, d)/d` | 最高价出现的相对位置 | [Aroon Indicator](https://www.investopedia.com/terms/a/aroon.asp) |
| IMIN | `IdxMin($low, d)/d` | 最低价出现的相对位置 | [Aroon Indicator](https://www.investopedia.com/terms/a/aroon.asp) |
| IMXD | `(IdxMax($high, d)-IdxMin($low, d))/d` | 高低点时间差，反映下跌动量 | - |

#### 3.4 价格-成交量关联类

| 因子类别 | 计算公式 | 含义 |
|---------|---------|------|
| CORR | `Corr($close, Log($volume+1), d)` | 价格与成交量相关性 |
| CORD | `Corr($close/Ref($close,1), Log($volume/Ref($volume, 1)+1), d)` | 价格变化与成交量变化相关性 |

#### 3.5 涨跌统计类

| 因子类别 | 计算公式 | 含义 |
|---------|---------|------|
| CNTP | `Mean($close>Ref($close, 1), d)` | d日内上涨天数比例 |
| CNTN | `Mean($close<Ref($close, 1), d)` | d日内下跌天数比例 |
| CNTD | `CNTP - CNTN` | 涨跌天数差 |

#### 3.6 RSI类指标

| 因子类别 | 计算公式 | 含义 | 参考链接 |
|---------|---------|------|----------|
| SUMP | `Sum(Greater($close-Ref($close, 1), 0), d)/(Sum(Abs($close-Ref($close, 1)), d)+1e-12)` | 总收益占总变动比例 | [RSI Indicator](https://www.investopedia.com/terms/r/rsi.asp) |
| SUMN | `Sum(Greater(Ref($close, 1)-$close, 0), d)/(Sum(Abs($close-Ref($close, 1)), d)+1e-12)` | 总亏损占总变动比例 | [RSI Indicator](https://www.investopedia.com/terms/r/rsi.asp) |
| SUMD | `SUMP - SUMN` | 收益亏损差值比，类RSI指标 | [RSI Indicator](https://www.investopedia.com/terms/r/rsi.asp) |

#### 3.7 成交量技术指标

| 因子类别 | 计算公式 | 含义 | 参考链接 |
|---------|---------|------|----------|
| VMA | `Mean($volume, d)/($volume+1e-12)` | 成交量移动平均 | [Volume Moving Average](https://www.barchart.com/education/technical-indicators/volume_moving_average) |
| VSTD | `Std($volume, d)/($volume+1e-12)` | 成交量标准差 | - |
| WVMA | `Std(Abs($close/Ref($close, 1)-1)*$volume, d)/(Mean(Abs($close/Ref($close, 1)-1)*$volume, d)+1e-12)` | 成交量加权价格变动波动率 | - |
| VSUMP | `Sum(Greater($volume-Ref($volume, 1), 0), d)/(Sum(Abs($volume-Ref($volume, 1)), d)+1e-12)` | 成交量增加占比 | - |
| VSUMN | `Sum(Greater(Ref($volume, 1)-$volume, 0), d)/(Sum(Abs($volume-Ref($volume, 1)), d)+1e-12)` | 成交量减少占比 | - |
| VSUMD | `VSUMP - VSUMN` | 成交量变化差值比 | - |

### 滚动窗口说明

所有滚动指标都使用以下窗口大小：**[5, 10, 20, 30, 60]天**

因此每个滚动指标会生成5个特征，例如：
- ROC5, ROC10, ROC20, ROC30, ROC60
- MA5, MA10, MA20, MA30, MA60
- STD5, STD10, STD20, STD30, STD60
- ...以此类推

## Alpha360因子集

Alpha360提供360个原始价格特征，包含过去60天的完整OHLCV数据序列。

### 设计理念

- **原始数据保留**：提供原始价格数据而非工程化特征
- **统一标准化**：所有价格通过最新收盘价标准化，成交量通过最新成交量标准化
- **时间序列完整性**：保持60天完整时间序列，便于深度学习模型使用

### 因子构成

Alpha360包含6类因子，每类60个特征，共360个：

#### 1. 收盘价序列 (60个)

```
CLOSE59, CLOSE58, CLOSE57, ..., CLOSE2, CLOSE1, CLOSE0
```

**计算公式**：
- CLOSE59: `Ref($close, 59)/$close` (59天前收盘价/当前收盘价)
- CLOSE58: `Ref($close, 58)/$close` (58天前收盘价/当前收盘价)
- ...
- CLOSE1: `Ref($close, 1)/$close` (1天前收盘价/当前收盘价)
- CLOSE0: `$close/$close = 1` (当前收盘价，标准化后恒为1)

#### 2. 开盘价序列 (60个)

```
OPEN59, OPEN58, OPEN57, ..., OPEN2, OPEN1, OPEN0
```

**计算公式**：`Ref($open, i)/$close` (i从59到0)

#### 3. 最高价序列 (60个)

```
HIGH59, HIGH58, HIGH57, ..., HIGH2, HIGH1, HIGH0
```

**计算公式**：`Ref($high, i)/$close` (i从59到0)

#### 4. 最低价序列 (60个)

```
LOW59, LOW58, LOW57, ..., LOW2, LOW1, LOW0
```

**计算公式**：`Ref($low, i)/$close` (i从59到0)

#### 5. 成交均价序列 (60个)

```
VWAP59, VWAP58, VWAP57, ..., VWAP2, VWAP1, VWAP0
```

**计算公式**：`Ref($vwap, i)/$close` (i从59到0)

#### 6. 成交量序列 (60个)

```
VOLUME59, VOLUME58, VOLUME57, ..., VOLUME2, VOLUME1, VOLUME0
```

**计算公式**：`Ref($volume, i)/($volume+1e-12)` (i从59到0)

### 标准化说明

- **价格标准化**：所有价格(收盘、开盘、最高、最低、VWAP)都除以当前收盘价
- **成交量标准化**：所有成交量都除以当前成交量(加1e-12防止除零)
- **标准化结果**：CLOSE0恒为1，VOLUME0恒为1(进一步标准化后可能为0)

## 标签定义

两个因子集都使用相同的标签定义：

```python
["Ref($close, -2)/Ref($close, -1) - 1"], ["LABEL0"]
```

**含义**：预测未来1天的收益率
- 分子：`Ref($close, -2)` = t+2日的收盘价
- 分母：`Ref($close, -1)` = t+1日的收盘价  
- 标签：(t+2日收盘价 / t+1日收盘价) - 1

## 使用场景对比

### Alpha158适用场景

- **传统机器学习模型**：LightGBM、XGBoost、线性回归等
- **特征工程完备**：包含丰富的技术分析指标
- **解释性强**：每个特征都有明确的金融含义
- **计算效率高**：预计算的技术指标，模型训练快速

### Alpha360适用场景  

- **深度学习模型**：LSTM、GRU、Transformer等时间序列模型
- **序列建模**：保留完整的时间序列信息
- **自动特征提取**：模型自动学习价格模式
- **端到端学习**：从原始价格数据直接学习预测

## 数据质量与预处理

### 缺失值处理

- Alpha158中包含除法运算，分母加入`1e-12`防止除零
- 成交量相关计算使用`($volume+1e-12)`处理零成交量情况
- 建议训练前进行NaN值检查和填充

### 异常值处理

- 价格跳空、停牌可能导致极值，建议进行异常值检测
- 成交量为0的情况已通过`1e-12`处理
- 建议对特征进行winsorize处理，削减极端值影响

### 标准化建议

- Alpha158：建议进一步Z-score标准化
- Alpha360：已经过相对标准化，但建议根据模型需求进一步处理

## 实现代码位置

- **Alpha158实现**：`qlib/contrib/data/loader.py:72-310` (`Alpha158DL.get_feature_config()`)
- **Alpha360实现**：`qlib/contrib/data/loader.py:16-58` (`Alpha360DL.get_feature_config()`)
- **数据处理器**：`qlib/contrib/data/handler.py:48-158` (`Alpha158`和`Alpha360`类)

## 参考文献

1. [Qlib官方文档](https://qlib.readthedocs.io/)
2. [Rate of Change Indicator](https://www.investopedia.com/terms/r/rateofchange.asp)
3. [Moving Average](https://www.investopedia.com/ask/answers/071414/whats-difference-between-moving-average-and-weighted-moving-average.asp)
4. [RSI Indicator](https://www.investopedia.com/terms/r/rsi.asp)
5. [Aroon Indicator](https://www.investopedia.com/terms/a/aroon.asp)
6. [Volume Moving Average](https://www.barchart.com/education/technical-indicators/volume_moving_average)

---

*本文档基于Qlib源码分析生成，版本信息以实际代码为准。*