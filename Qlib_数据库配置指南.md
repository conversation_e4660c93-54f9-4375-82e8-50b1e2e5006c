# Qlib 数据库和存储配置指南

## 🗄️ Qlib数据存储架构概览

Qlib **不使用传统的关系型数据库**，而是采用了一套高性能的**文件存储系统**和**缓存机制**。

### 核心存储方式

1. **主要存储**: 基于文件的二进制存储 (`.bin` 格式)
2. **缓存系统**: 内存缓存 + 磁盘缓存 + Redis缓存
3. **实验管理**: MLflow (SQLite/MySQL/PostgreSQL)
4. **任务管理**: MongoDB (可选)

## 📁 文件存储系统

### 1. 数据目录结构
```
~/.qlib/qlib_data/cn_data/
├── calendars/          # 交易日历
│   └── day.txt
├── features/           # 股票特征数据
│   ├── sh600000/      # 每只股票一个目录
│   │   ├── close.day.bin    # 收盘价
│   │   ├── open.day.bin     # 开盘价
│   │   ├── high.day.bin     # 最高价
│   │   ├── low.day.bin      # 最低价
│   │   ├── volume.day.bin   # 成交量
│   │   ├── change.day.bin   # 涨跌幅
│   │   └── factor.day.bin   # 复权因子
│   └── ...
└── instruments/        # 股票列表
    ├── all.txt         # 全部股票
    ├── csi300.txt      # 沪深300
    └── csi500.txt      # 中证500
```

### 2. 二进制文件格式
- **文件类型**: `.bin` (高效的二进制格式)
- **数据类型**: IEEE 754双精度浮点数
- **索引方式**: 基于时间的顺序索引
- **压缩**: 无压缩，追求访问速度

## ⚙️ 配置方式

### 1. 基础配置 (qlib.init())

```python
import qlib
from qlib.constant import REG_CN

# 基础配置
qlib.init(
    provider_uri="~/.qlib/qlib_data/cn_data",  # 数据路径
    region=REG_CN,                             # 市场区域
    expression_cache=None,                     # 表达式缓存
    dataset_cache=None,                        # 数据集缓存
    redis_host="127.0.0.1",                   # Redis主机
    redis_port=6379,                          # Redis端口
)
```

### 2. 多频率数据配置

```python
# 支持多个时间频率的数据
provider_uri_map = {
    "1min": "~/.qlib/qlib_data/cn_data_1min",  # 分钟数据
    "day": "~/.qlib/qlib_data/cn_data",        # 日线数据
}

qlib.init(
    provider_uri=provider_uri_map,
    region=REG_CN
)
```

### 3. 高级配置

```python
qlib.init(
    provider_uri="~/.qlib/qlib_data/cn_data",
    region=REG_CN,
    
    # 数据提供者配置
    calendar_provider="LocalCalendarProvider",
    instrument_provider="LocalInstrumentProvider", 
    feature_provider="LocalFeatureProvider",
    dataset_provider="LocalDatasetProvider",
    
    # 缓存配置
    expression_cache="DiskExpressionCache",      # 磁盘表达式缓存
    dataset_cache="DiskDatasetCache",            # 磁盘数据集缓存
    mem_cache_size_limit=500,                    # 内存缓存大小限制
    
    # Redis配置 (用于分布式缓存)
    redis_host="127.0.0.1",
    redis_port=6379,
    redis_task_db=1,
    
    # 性能配置
    kernels=8,                                   # 并行处理核心数
    joblib_backend="multiprocessing",           # 并行后端
)
```

## 🗂️ 数据提供者类型

### 1. Local Provider (本地文件)
```python
# 默认配置 - 本地文件存储
"provider": "LocalProvider"
"calendar_provider": "LocalCalendarProvider"
"instrument_provider": "LocalInstrumentProvider"
"feature_provider": "LocalFeatureProvider"
```

### 2. 自定义Backend配置
```python
# 使用自定义存储后端
qlib.init(
    feature_provider={
        "class": "LocalFeatureProvider",
        "module_path": "qlib.data.data",
        "kwargs": {
            "backend": {
                "class": "FileFeatureStorage",
                "module_path": "qlib.data.storage.file_storage",
                "kwargs": {"provider_uri_map": provider_uri_map},
            },
        },
    }
)
```

## 💾 缓存系统配置

### 1. 内存缓存
```python
# 内存缓存配置
"mem_cache_size_limit": 500,        # 缓存条目数限制
"mem_cache_limit_type": "length",   # 限制类型
"mem_cache_expire": 3600,           # 过期时间(秒)
```

### 2. 磁盘缓存
```python
# 磁盘缓存配置
"expression_cache": "DiskExpressionCache",
"dataset_cache": "DiskDatasetCache", 
"local_cache_path": "~/.cache/qlib_simple_cache",
"default_disk_cache": 1,             # 0:跳过/1:使用
```

### 3. Redis缓存 (分布式)
```python
# Redis配置
"redis_host": "127.0.0.1",
"redis_port": 6379,
"redis_task_db": 1,
"redis_password": None,
```

## 🔧 配置文件方式

### 1. 通过配置对象
```python
from qlib.config import C

# 直接修改配置
C["provider_uri"] = "~/my_data_path"
C["redis_host"] = "*************"
C["mem_cache_size_limit"] = 1000
```

### 2. 环境变量配置
```bash
# 设置环境变量
export QLIB_DATA_PATH="~/.qlib/qlib_data/cn_data"
export QLIB_REDIS_HOST="127.0.0.1"
export QLIB_REDIS_PORT="6379"
```

### 3. YAML配置文件
```yaml
# qlib_config.yaml
provider_uri: "~/.qlib/qlib_data/cn_data"
region: "cn"
redis_host: "127.0.0.1"
redis_port: 6379
expression_cache: "DiskExpressionCache"
dataset_cache: "DiskDatasetCache"
mem_cache_size_limit: 500
```

## 🗄️ 实验管理数据库 (MLflow)

### 1. 默认配置 (SQLite)
```python
# MLflow默认使用SQLite
"exp_manager": {
    "class": "MLflowExpManager",
    "module_path": "qlib.workflow.expm",
    "kwargs": {
        "uri": "file:./mlruns",           # SQLite文件路径
        "default_exp_name": "Experiment",
    },
}
```

### 2. MySQL配置
```python
# 使用MySQL作为MLflow后端
qlib.init(
    # ... 其他配置
    exp_manager={
        "class": "MLflowExpManager", 
        "module_path": "qlib.workflow.expm",
        "kwargs": {
            "uri": "mysql://user:password@host:port/database",
            "default_exp_name": "Experiment",
        },
    }
)
```

### 3. PostgreSQL配置
```python
# 使用PostgreSQL作为MLflow后端
exp_manager={
    "class": "MLflowExpManager",
    "module_path": "qlib.workflow.expm", 
    "kwargs": {
        "uri": "postgresql://user:password@host:port/database",
        "default_exp_name": "Experiment",
    },
}
```

## 🍃 MongoDB配置 (任务管理)

### 1. MongoDB配置
```python
# 任务管理MongoDB配置
from qlib.config import C

C["mongo"] = {
    "task_url": "mongodb://localhost:27017/",
    "task_db_name": "qlib_task_db"
}
```

### 2. 在qlib.init()中配置
```python
qlib.init(
    # ... 其他配置
    mongo={
        "task_url": "********************************:port/",
        "task_db_name": "qlib_tasks"
    }
)
```

## 📊 数据路径管理

### 1. 数据路径优先级
```
1. backend_config: backend_obj["kwargs"]["provider_uri"]
2. backend_config: backend_obj["kwargs"]["provider_uri_map"] 
3. qlib.init(): provider_uri参数
4. 环境变量: QLIB_DATA_PATH
```

### 2. 多路径配置
```python
# 支持多个数据源
provider_uri_map = {
    "day": "/data/qlib/cn_data",           # 日线数据
    "1min": "/data/qlib/cn_data_1min",     # 分钟数据
    "tick": "/data/qlib/cn_data_tick",     # Tick数据
}

qlib.init(provider_uri=provider_uri_map)
```

## 🔍 配置检查和调试

### 1. 查看当前配置
```python
from qlib.config import C

print("当前配置:")
print(f"数据路径: {C.provider_uri}")
print(f"缓存配置: {C.expression_cache}")
print(f"Redis配置: {C.redis_host}:{C.redis_port}")
```

### 2. 验证数据连接
```python
from qlib.data import D

# 测试数据访问
instruments = D.list_instruments(D.instruments())
print(f"可用股票数量: {len(instruments)}")

# 测试数据读取
data = D.features(['SH600000'], ['$close'], 
                 start_time='2020-01-01', end_time='2020-01-10')
print(f"数据读取成功: {data.shape}")
```

## 🚀 性能优化配置

### 1. 并行处理配置
```python
qlib.init(
    kernels=16,                          # CPU核心数
    joblib_backend="multiprocessing",    # 并行后端
    maxtasksperchild=None,              # 进程任务数限制
)
```

### 2. 缓存优化
```python
qlib.init(
    # 内存缓存
    mem_cache_size_limit=1000,          # 增大内存缓存
    mem_cache_expire=7200,              # 延长过期时间
    
    # 磁盘缓存
    expression_cache="DiskExpressionCache",
    dataset_cache="DiskDatasetCache",
    default_disk_cache=1,
)
```

### 3. Redis集群配置
```python
# Redis集群配置 (高级用法)
qlib.init(
    redis_host="redis-cluster-host",
    redis_port=6379,
    redis_password="your_password",
    redis_task_db=1,
)
```

## 📝 配置示例

### 1. 开发环境配置
```python
# 开发环境 - 注重灵活性
qlib.init(
    provider_uri="~/.qlib/qlib_data/cn_data",
    region=REG_CN,
    expression_cache=None,              # 禁用缓存便于调试
    dataset_cache=None,
    logging_level=logging.DEBUG,        # 详细日志
)
```

### 2. 生产环境配置
```python
# 生产环境 - 注重性能
qlib.init(
    provider_uri="/data/qlib/cn_data",
    region=REG_CN,
    
    # 启用所有缓存
    expression_cache="DiskExpressionCache",
    dataset_cache="DiskDatasetCache",
    mem_cache_size_limit=2000,
    
    # Redis缓存
    redis_host="redis.example.com",
    redis_port=6379,
    
    # 性能优化
    kernels=32,
    joblib_backend="multiprocessing",
    
    # 实验管理
    exp_manager={
        "class": "MLflowExpManager",
        "kwargs": {
            "uri": "mysql://user:<EMAIL>/mlflow",
        },
    }
)
```

### 3. 高频交易配置
```python
# 高频交易环境
qlib.init(
    provider_uri={
        "1min": "/ssd/qlib/cn_data_1min",   # SSD存储
        "tick": "/ssd/qlib/cn_data_tick",
    },
    region=REG_CN,
    
    # 高频优化
    kernels=64,
    maxtasksperchild=1,                 # 高频数据推荐设置
    mem_cache_size_limit=5000,          # 大内存缓存
    
    # Redis缓存集群
    redis_host="redis-cluster.example.com",
)
```

## 🛠️ 故障排除

### 1. 数据路径问题
```python
# 检查数据路径
from qlib.config import C
print(f"数据路径: {C.dpm.get_data_uri()}")

# 检查文件是否存在
import os
data_path = C.dpm.get_data_uri()
print(f"路径存在: {os.path.exists(data_path)}")
```

### 2. 缓存问题
```python
# 清理缓存
from qlib.data.cache import H
H.clear()

# 重新初始化
qlib.init(provider_uri="~/.qlib/qlib_data/cn_data", region=REG_CN)
```

### 3. Redis连接问题
```python
# 测试Redis连接
import redis
try:
    r = redis.Redis(host='127.0.0.1', port=6379, db=1)
    r.ping()
    print("Redis连接成功")
except:
    print("Redis连接失败")
```

## 📚 相关资源

- **官方文档**: https://qlib.readthedocs.io/en/latest/component/data.html
- **存储架构**: https://qlib.readthedocs.io/en/latest/advanced/data.html
- **缓存机制**: https://qlib.readthedocs.io/en/latest/component/cache.html
- **MLflow集成**: https://qlib.readthedocs.io/en/latest/component/recorder.html

---

## 📋 总结

Qlib的数据存储系统特点：

1. **文件存储**: 基于二进制文件，高性能访问
2. **多级缓存**: 内存 + 磁盘 + Redis缓存
3. **灵活配置**: 支持多种配置方式
4. **可扩展**: 支持自定义存储后端
5. **高性能**: 针对量化投资场景优化

**关键配置位置**:
- 主配置: `qlib.init()` 参数
- 运行时配置: `qlib.config.C` 对象  
- 环境变量: `QLIB_*` 系列
- 配置文件: YAML格式 