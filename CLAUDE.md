# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Qlib is Microsoft's AI-oriented quantitative investment platform that provides a complete machine learning pipeline for quantitative trading. It covers the entire chain: data processing, model training, backtesting, portfolio optimization, and order execution.

**Official Documentation**: https://qlib.readthedocs.io/ - This is the comprehensive documentation for Qlib and should be referenced for detailed API documentation, tutorials, and advanced usage patterns.

## Architecture

### Core Components

- **Data Layer** (`qlib/data/`): High-performance data storage and processing with custom binary format
  - `handler.py`: Data handlers like Alpha158/Alpha360 feature extractors
  - `dataset/`: Dataset management and caching
  - `ops.py`: Financial operators and technical indicators
  - `_libs/`: Cython-optimized rolling/expanding window operations

- **Models** (`qlib/model/`, `qlib/contrib/model/`): 
  - Base model interfaces in `qlib/model/base.py`
  - 50+ SOTA models in `qlib/contrib/model/` (LSTM, Transformer, LightGBM, etc.)
  - Meta-learning and ensemble frameworks

- **Workflow** (`qlib/workflow/`): Experiment management and MLflow integration
  - `cli.py`: Main entry point for `qrun` command
  - `recorder.py`: Experiment tracking and artifact management

- **Backtest** (`qlib/backtest/`): Portfolio simulation and performance analysis
  - `executor.py`: Order execution simulation
  - `account.py`: Portfolio accounting
  - `report.py`: Performance analysis and visualization

- **Strategy** (`qlib/strategy/`): Trading strategy framework
- **RL** (`qlib/rl/`): Reinforcement learning for order execution and portfolio optimization

### Key Design Patterns

- **Handler-Dataset Pattern**: Data handlers (Alpha158) extract features, DatasetH manages train/valid/test splits
- **Recorder Pattern**: All experiments tracked via `R.start()` with MLflow backend
- **Executor Pattern**: Nested executors for multi-level trading strategies
- **Config-driven Workflows**: YAML configs define complete ML pipelines

## Development Commands

### Setup and Installation
```bash
# Install prerequisites (builds Cython extensions)
make prerequisite

# Install in development mode with all dependencies
make dev

# Install specific dependency groups
make lint        # linting tools
make docs        # documentation
make test        # testing dependencies
make rl          # reinforcement learning
```

### Code Quality
```bash
# Run all linting checks
make lint

# Individual checks
make black       # code formatting
make pylint      # static analysis  
make flake8      # style guide enforcement
make mypy        # type checking
make nbqa        # notebook quality checks
```

### Testing
```bash
# Run tests (use pytest directly)
pytest tests/

# Run specific test categories
pytest tests/data_mid_layer_tests/
pytest tests/model/
pytest -m "not slow"  # skip slow tests
```

### Data Management
```bash
# Download sample data (community source)
python scripts/get_data.py qlib_data --target_dir ~/.qlib/qlib_data/cn_data --region cn

# Check data health
python scripts/check_data_health.py check_data --qlib_dir ~/.qlib/qlib_data/cn_data
```

### Running Experiments
```bash
# Run workflow with config file
cd examples
qrun benchmarks/LightGBM/workflow_config_lightgbm_Alpha158.yaml

# Debug mode
python -m pdb qlib/workflow/cli.py examples/benchmarks/LightGBM/workflow_config_lightgbm_Alpha158.yaml

# Run multiple models
python examples/run_all_model.py run --models=lightgbm,linear
```

### Data Handler Usage

When working with data handlers, the correct pattern is:

```python
from qlib.contrib.data.handler import Alpha158

# Initialize handler
handler = Alpha158(
    instruments='csi300',
    start_time='2020-01-01', 
    end_time='2020-12-31',
    freq='day'
)

# Fetch data with segments for train/valid/test splits
data = handler.fetch(
    segments={
        'train': ('2020-01-01', '2020-06-30'),
        'valid': ('2020-07-01', '2020-09-30'), 
        'test': ('2020-10-01', '2020-12-31')
    }
)

# Access features and labels
X_train = data['train']['feature']
y_train = data['train']['label']['LABEL0']
```

The `fetch()` method WITHOUT segments returns raw data without train/valid/test structure.

### Build and Package
```bash
# Clean build artifacts
make clean

# Build wheel
make build

# Generate documentation
make docs-gen
```

## Development Notes

### MySQL Integration
This repository includes custom MySQL task management in `qlib/workflow/task/mysql_manager.py` for production deployments.

### Performance Considerations
- Data operations use Cython extensions in `qlib/data/_libs/`
- Memory caching system in `qlib/data/cache.py`
- High-frequency data processing optimized for speed

### Configuration
- Main config in `qlib/config.py` 
- Default initialization: `qlib.init(mount_path="~/.qlib/qlib_data/cn_data", region="cn")`
- MLflow experiments stored in `mlruns/` directory

### Key Files to Reference
- `examples/workflow_by_code.py`: Complete workflow example
- `examples/benchmarks/*/workflow_config_*.yaml`: Model configurations  
- `qlib/contrib/data/handler.py`: Feature engineering patterns
- `qlib/workflow/cli.py`: Main CLI entry point

### Documentation Resources
- **Main Documentation**: https://qlib.readthedocs.io/ - Complete API reference and tutorials
- **Quick Start**: https://qlib.readthedocs.io/en/latest/introduction/quick.html
- **Component Guides**: 
  - Data: https://qlib.readthedocs.io/en/latest/component/data.html
  - Models: https://qlib.readthedocs.io/en/latest/component/model.html
  - Workflow: https://qlib.readthedocs.io/en/latest/component/workflow.html
  - Backtest: https://qlib.readthedocs.io/en/latest/component/report.html
  - RL Framework: https://qlib.readthedocs.io/en/latest/component/rl.html

### Testing Strategy
- Unit tests in `tests/` organized by component
- Integration tests in `test_all_pipeline.py`
- Performance tests marked with `@pytest.mark.slow`
- Data layer tests require local data setup

This is a research-oriented codebase with emphasis on experiment reproducibility and performance optimization for financial data processing.

## Common Issues and Solutions

### Dataset Structure Issue: `'feature'` KeyError

**Problem**: When using `DatasetH.prepare()`, attempting to access `train_data['feature']` and `train_data['label']` raises a KeyError.

**Root Cause**: The `prepare()` method returns a pandas DataFrame directly, not a dictionary with 'feature' and 'label' keys.

**Solution**: 
```python
# ❌ Wrong approach (causes KeyError)
train_data = dataset.prepare('train')
features = train_data['feature']  # KeyError: 'feature'
labels = train_data['label']      # KeyError: 'label'

# ✅ Correct approach
train_data = dataset.prepare('train')

# Separate features and labels by column names
label_cols = [col for col in train_data.columns if 'LABEL' in str(col)]
feature_cols = [col for col in train_data.columns if 'LABEL' not in str(col)]

features = train_data[feature_cols]  # Shape: (samples, 158) for Alpha158
labels = train_data[label_cols]      # Shape: (samples, 1)
```

**Data Structure Details**:
- Returns: `pandas.DataFrame` with MultiIndex (2 levels: datetime, instrument)
- Alpha158: 158 feature columns + 1 label column (LABEL0)
- Alpha360: 360 feature columns + 1 label column (LABEL0)
- Features: Technical indicators with names like 'KMID', 'ROC5', 'MA20', etc.
- Labels: Always named 'LABEL0' (forward returns)

**Files Affected**: Any script using `DatasetH` with Alpha158/Alpha360 handlers, especially demo scripts in `demo/` directory.

### Date Range Issue: Invalid Day for Month

**Problem**: Date range errors like `day is out of range for month: 2020-02-31` when using programmatically generated date strings.

**Root Cause**: Naive date string generation that doesn't account for different month lengths.

**Solution**:
```python
# ❌ Wrong approach (causes date errors)
for i in range(3):
    data = D.features(
        start_time=f'2020-0{i+1}-01',
        end_time=f'2020-0{i+1}-31',  # February only has 28/29 days!
    )

# ✅ Correct approach
date_ranges = [
    ('2020-01-01', '2020-01-31'),
    ('2020-02-01', '2020-02-29'),  # Account for leap year
    ('2020-03-01', '2020-03-31')
]
for start_date, end_date in date_ranges:
    data = D.features(start_time=start_date, end_time=end_date)
```

**Files Affected**: Any script with hardcoded date ranges or programmatic date generation.

### Custom Handler Implementation Issues

**Problem**: Creating custom data handlers by inheriting directly from `DataHandlerLP` causes complex configuration issues with processors and data loaders.

**Root Cause**: `DataHandlerLP` requires complex `data_loader` configuration and proper processor setup with fit times, which is error-prone.

**Solution**: Inherit from `Alpha158` instead and override `get_feature_config()` and `get_label_config()` methods:

```python
# ❌ Complex approach (causes configuration errors)
class CustomHandler(DataHandlerLP):
    def __init__(self, ...):
        # Complex data_loader setup
        # Processor configuration with fit times
        # Error-prone initialization

# ✅ Simplified approach
class CustomHandler(Alpha158):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
    
    def get_feature_config(self):
        """Override feature configuration"""
        conf = {
            "kbar": {},
            "price": {"windows": [0], "feature": ["CLOSE", "OPEN"]},
            "rolling": {"windows": [5, 20], "feature": ["ROC", "MA"]},
        }
        from qlib.contrib.data.loader import Alpha158DL
        return Alpha158DL.get_feature_config(conf)
    
    def get_label_config(self):
        """Override label configuration"""
        return ["Ref($close, -1) / $close - 1"], ["LABEL0"]
```

**Benefits of Alpha158 Inheritance**:
- Stable processor configuration
- Automatic feature expansion via Alpha158DL
- Proper data loader setup
- Consistent with Qlib patterns

**Files Affected**: Custom handler implementations, especially in demo scripts.

### NaN Handling in Machine Learning Pipelines

**Problem**: `LinearRegression does not accept missing values encoded as NaN natively` error when training sklearn models with Qlib data.

**Root Cause**: Qlib datasets often contain NaN values, and scikit-learn models require clean numerical data.

**Solution**: Implement robust NaN handling before model training:

```python
# ❌ Basic approach (may still fail)
X_train = X_train.fillna(X_train.mean())
y_train = y_train.fillna(y_train.mean())

# ✅ Robust approach
def handle_nan_values(X_train, X_test, y_train, y_test):
    """Robust NaN handling for ML pipelines"""
    print("Processing missing values...")
    
    # Handle feature NaN values
    X_train_clean = X_train.fillna(X_train.mean())
    X_train_clean = X_train_clean.fillna(0)  # Handle cases where mean is NaN
    
    # Use training statistics for test set
    X_test_clean = X_test.fillna(X_train.mean())
    X_test_clean = X_test_clean.fillna(0)
    
    # Handle label NaN values (use median for robustness)
    y_train_clean = y_train.fillna(y_train.median())
    y_test_clean = y_test.fillna(y_train.median())
    
    # Verify no NaN values remain
    assert not X_train_clean.isna().any().any(), "Training features still contain NaN"
    assert not y_train_clean.isna().any(), "Training labels still contain NaN"
    
    return X_train_clean, X_test_clean, y_train_clean, y_test_clean

# Usage in feature selection functions
def safe_feature_selection(X, y, method='filter'):
    """Feature selection with NaN validation"""
    assert not X.isna().any().any(), "Features contain NaN values"
    assert not y.isna().any(), "Labels contain NaN values"
    # ... proceed with feature selection
```

**Key Points**:
- Always check for NaN values before sklearn operations
- Use training set statistics to fill test set NaN values
- Have fallback values (like 0) when statistics are NaN
- Use median for labels (more robust than mean)
- Add assertions to verify data cleanliness

**Files Affected**: Any script using sklearn with Qlib data, especially feature selection and model training scripts.

### Empty Array Error in ML Metrics

**Problem**: `ValueError: Found array with 0 sample(s) (shape=(0,)) while a minimum of 1 is required` when computing metrics like MSE.

**Root Cause**: Prediction arrays or target arrays are empty due to data filtering, incorrect data access, or mismatched data segments.

**Solution**: Add data validation before metric computation:

```python
# ❌ Direct computation (may fail with empty arrays)
mse = mean_squared_error(y_test, predictions)

# ✅ Safe computation with validation
def safe_metric_computation(y_true, y_pred, metric_func):
    """Safely compute metrics with validation"""
    # Ensure arrays are not empty
    if len(y_true) == 0 or len(y_pred) == 0:
        print("Warning: Empty arrays detected")
        return np.nan
    
    # Ensure matching lengths
    if len(y_true) != len(y_pred):
        print(f"Warning: Length mismatch - true: {len(y_true)}, pred: {len(y_pred)}")
        min_len = min(len(y_true), len(y_pred))
        y_true = y_true[:min_len]
        y_pred = y_pred[:min_len]
    
    # Handle different array types
    if hasattr(y_true, 'values'):
        y_true = y_true.values
    if hasattr(y_pred, 'values'):
        y_pred = y_pred.values
        
    return metric_func(y_true, y_pred)

# Usage
mse = safe_metric_computation(y_test, predictions, mean_squared_error)
```

**Additional Validation Steps**:
```python
# Data segment validation
print(f"Data shapes - Train: {X_train.shape}, Test: {X_test.shape}")
if X_train.shape[0] == 0 or X_test.shape[0] == 0:
    raise ValueError("Empty dataset detected - check data splitting parameters")

# Prediction validation
pred_values = model.predict(X_test)
print(f"Prediction shape: {pred_values.shape}")
if len(pred_values) == 0:
    raise ValueError("Model returned empty predictions")
```

**Files Affected**: Any script computing ML metrics, especially hyperparameter optimization and model evaluation scripts.