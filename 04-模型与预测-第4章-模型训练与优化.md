# 第4章：模型训练与优化

## 学习目标

通过本章学习，您将能够：
- 掌握量化投资模型的训练流程和最佳实践
- 理解模型评估指标的含义和计算方法
- 学会模型超参数调优和特征选择技术
- 掌握模型部署和在线更新的方法
- 理解模型监控和维护的重要性

## 4.1 模型训练流程

### 4.1.1 数据分割策略

#### 时间序列数据分割

在量化投资中，数据分割需要考虑时间序列的特性，避免未来信息泄露。

**1. 时间序列分割原则**
```python
import pandas as pd
import numpy as np
from sklearn.model_selection import TimeSeriesSplit

def time_series_split(data, train_ratio=0.6, valid_ratio=0.2, test_ratio=0.2):
    """时间序列数据分割"""
    total_length = len(data)
    train_end = int(total_length * train_ratio)
    valid_end = int(total_length * (train_ratio + valid_ratio))
    
    train_data = data[:train_end]
    valid_data = data[train_end:valid_end]
    test_data = data[valid_end:]
    
    return train_data, valid_data, test_data

# 使用示例
data = pd.DataFrame({
    'feature1': np.random.randn(1000),
    'feature2': np.random.randn(1000),
    'label': np.random.randn(1000)
}, index=pd.date_range('2020-01-01', periods=1000, freq='D'))

train_data, valid_data, test_data = time_series_split(data)
print(f"训练集: {len(train_data)}, 验证集: {len(valid_data)}, 测试集: {len(test_data)}")
```

**2. 滚动窗口分割**
```python
def rolling_window_split(data, window_size=252, step_size=63):
    """滚动窗口分割"""
    splits = []
    
    for i in range(0, len(data) - window_size, step_size):
        train_start = i
        train_end = i + window_size
        valid_end = min(train_end + step_size, len(data))
        
        train_data = data[train_start:train_end]
        valid_data = data[train_end:valid_end]
        
        splits.append((train_data, valid_data))
    
    return splits

# 使用示例
splits = rolling_window_split(data, window_size=252, step_size=63)
print(f"生成了 {len(splits)} 个滚动窗口")
```

**3. 扩展窗口分割**
```python
def expanding_window_split(data, min_train_size=252, step_size=63):
    """扩展窗口分割"""
    splits = []
    
    for i in range(min_train_size, len(data), step_size):
        train_data = data[:i]
        valid_data = data[i:i+step_size]
        
        splits.append((train_data, valid_data))
    
    return splits

# 使用示例
splits = expanding_window_split(data, min_train_size=252, step_size=63)
print(f"生成了 {len(splits)} 个扩展窗口")
```

#### 股票数据分割

**1. 按股票分组分割**
```python
def stock_group_split(data, stock_column='instrument', train_ratio=0.6):
    """按股票分组分割数据"""
    unique_stocks = data[stock_column].unique()
    train_stocks = unique_stocks[:int(len(unique_stocks) * train_ratio)]
    
    train_data = data[data[stock_column].isin(train_stocks)]
    test_data = data[~data[stock_column].isin(train_stocks)]
    
    return train_data, test_data
```

**2. 分层抽样分割**
```python
def stratified_split(data, label_column='label', n_bins=10):
    """分层抽样分割"""
    # 将连续标签分箱
    data['label_bin'] = pd.cut(data[label_column], bins=n_bins, labels=False)
    
    from sklearn.model_selection import StratifiedShuffleSplit
    
    splitter = StratifiedShuffleSplit(n_splits=1, test_size=0.2, random_state=42)
    
    for train_idx, test_idx in splitter.split(data, data['label_bin']):
        train_data = data.iloc[train_idx]
        test_data = data.iloc[test_idx]
    
    return train_data, test_data
```

### 4.1.2 特征选择和工程

#### 特征选择方法

**1. 过滤法（Filter Methods）**
```python
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression

def filter_feature_selection(X, y, method='f_regression', k=50):
    """过滤法特征选择"""
    if method == 'f_regression':
        selector = SelectKBest(score_func=f_regression, k=k)
    elif method == 'mutual_info':
        selector = SelectKBest(score_func=mutual_info_regression, k=k)
    else:
        raise ValueError(f"不支持的方法: {method}")
    
    X_selected = selector.fit_transform(X, y)
    selected_features = X.columns[selector.get_support()].tolist()
    
    return X_selected, selected_features, selector.scores_

# 使用示例
X = data[['feature1', 'feature2', 'feature3']]
y = data['label']

X_selected, selected_features, scores = filter_feature_selection(X, y, k=2)
print(f"选择的特征: {selected_features}")
print(f"特征得分: {scores}")
```

**2. 包装法（Wrapper Methods）**
```python
from sklearn.feature_selection import RFE
from sklearn.linear_model import LinearRegression

def wrapper_feature_selection(X, y, n_features=50):
    """包装法特征选择"""
    estimator = LinearRegression()
    selector = RFE(estimator, n_features_to_select=n_features)
    
    X_selected = selector.fit_transform(X, y)
    selected_features = X.columns[selector.support_].tolist()
    
    return X_selected, selected_features

# 使用示例
X_selected, selected_features = wrapper_feature_selection(X, y, n_features=2)
print(f"选择的特征: {selected_features}")
```

**3. 嵌入法（Embedded Methods）**
```python
from sklearn.linear_model import Lasso, Ridge
from sklearn.ensemble import RandomForestRegressor

def embedded_feature_selection(X, y, method='lasso', threshold=0.01):
    """嵌入法特征选择"""
    if method == 'lasso':
        model = Lasso(alpha=0.01)
    elif method == 'ridge':
        model = Ridge(alpha=1.0)
    elif method == 'random_forest':
        model = RandomForestRegressor(n_estimators=100, random_state=42)
    else:
        raise ValueError(f"不支持的方法: {method}")
    
    model.fit(X, y)
    
    if method in ['lasso', 'ridge']:
        # 线性模型：基于系数绝对值
        feature_importance = np.abs(model.coef_)
    else:
        # 随机森林：基于特征重要性
        feature_importance = model.feature_importances_
    
    # 选择重要性大于阈值的特征
    selected_mask = feature_importance > threshold
    selected_features = X.columns[selected_mask].tolist()
    X_selected = X.iloc[:, selected_mask]
    
    return X_selected, selected_features, feature_importance

# 使用示例
X_selected, selected_features, importance = embedded_feature_selection(X, y, method='lasso')
print(f"选择的特征: {selected_features}")
print(f"特征重要性: {importance}")
```

#### 特征工程技术

**1. 技术指标特征**
```python
def create_technical_features(data):
    """创建技术指标特征"""
    features = data.copy()
    
    # 移动平均线
    features['MA5'] = data['close'].rolling(window=5).mean()
    features['MA20'] = data['close'].rolling(window=20).mean()
    features['MA60'] = data['close'].rolling(window=60).mean()
    
    # 相对强弱指数(RSI)
    delta = data['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    features['RSI'] = 100 - (100 / (1 + rs))
    
    # 布林带
    features['BB_middle'] = data['close'].rolling(window=20).mean()
    bb_std = data['close'].rolling(window=20).std()
    features['BB_upper'] = features['BB_middle'] + (bb_std * 2)
    features['BB_lower'] = features['BB_middle'] - (bb_std * 2)
    
    # 成交量指标
    features['volume_ma5'] = data['volume'].rolling(window=5).mean()
    features['volume_ratio'] = data['volume'] / features['volume_ma5']
    
    return features

# 使用示例
technical_features = create_technical_features(data)
print(f"技术指标特征数量: {len(technical_features.columns)}")
```

**2. 统计特征**
```python
def create_statistical_features(data, window_sizes=[5, 10, 20]):
    """创建统计特征"""
    features = data.copy()
    
    for window in window_sizes:
        # 滚动统计量
        features[f'mean_{window}'] = data['close'].rolling(window=window).mean()
        features[f'std_{window}'] = data['close'].rolling(window=window).std()
        features[f'min_{window}'] = data['close'].rolling(window=window).min()
        features[f'max_{window}'] = data['close'].rolling(window=window).max()
        features[f'skew_{window}'] = data['close'].rolling(window=window).skew()
        features[f'kurt_{window}'] = data['close'].rolling(window=window).kurt()
    
    return features

# 使用示例
statistical_features = create_statistical_features(data)
print(f"统计特征数量: {len(statistical_features.columns)}")
```

**3. 交互特征**
```python
def create_interaction_features(data):
    """创建交互特征"""
    features = data.copy()
    
    # 价格与成交量的交互
    features['price_volume'] = data['close'] * data['volume']
    features['price_volume_ratio'] = data['close'] / data['volume']
    
    # 技术指标交互
    features['ma_cross'] = (data['close'] > features['MA20']).astype(int)
    features['bb_position'] = (data['close'] - features['BB_lower']) / (features['BB_upper'] - features['BB_lower'])
    
    return features

# 使用示例
interaction_features = create_interaction_features(data)
print(f"交互特征数量: {len(interaction_features.columns)}")
```

### 4.1.3 超参数调优

#### 网格搜索

**1. 基础网格搜索**
```python
from sklearn.model_selection import GridSearchCV
from sklearn.ensemble import RandomForestRegressor

def grid_search_optimization(X_train, y_train, X_val, y_val):
    """网格搜索超参数优化"""
    # 定义参数网格
    param_grid = {
        'n_estimators': [50, 100, 200],
        'max_depth': [5, 10, 15, None],
        'min_samples_split': [2, 5, 10],
        'min_samples_leaf': [1, 2, 4]
    }
    
    # 创建模型
    model = RandomForestRegressor(random_state=42)
    
    # 网格搜索
    grid_search = GridSearchCV(
        estimator=model,
        param_grid=param_grid,
        cv=5,
        scoring='neg_mean_squared_error',
        n_jobs=-1,
        verbose=1
    )
    
    # 训练
    grid_search.fit(X_train, y_train)
    
    # 最佳参数
    best_params = grid_search.best_params_
    best_score = grid_search.best_score_
    
    print(f"最佳参数: {best_params}")
    print(f"最佳得分: {best_score}")
    
    return grid_search.best_estimator_, best_params

# 使用示例
best_model, best_params = grid_search_optimization(X_train, y_train, X_val, y_val)
```

**2. 随机搜索**
```python
from sklearn.model_selection import RandomizedSearchCV
from scipy.stats import uniform, randint

def random_search_optimization(X_train, y_train, X_val, y_val):
    """随机搜索超参数优化"""
    # 定义参数分布
    param_distributions = {
        'n_estimators': randint(50, 300),
        'max_depth': randint(5, 20),
        'min_samples_split': randint(2, 20),
        'min_samples_leaf': randint(1, 10),
        'max_features': ['sqrt', 'log2', None]
    }
    
    # 创建模型
    model = RandomForestRegressor(random_state=42)
    
    # 随机搜索
    random_search = RandomizedSearchCV(
        estimator=model,
        param_distributions=param_distributions,
        n_iter=100,
        cv=5,
        scoring='neg_mean_squared_error',
        n_jobs=-1,
        random_state=42,
        verbose=1
    )
    
    # 训练
    random_search.fit(X_train, y_train)
    
    # 最佳参数
    best_params = random_search.best_params_
    best_score = random_search.best_score_
    
    print(f"最佳参数: {best_params}")
    print(f"最佳得分: {best_score}")
    
    return random_search.best_estimator_, best_params

# 使用示例
best_model, best_params = random_search_optimization(X_train, y_train, X_val, y_val)
```

#### 贝叶斯优化

**1. 使用Optuna进行贝叶斯优化**
```python
import optuna
from sklearn.model_selection import cross_val_score

def objective(trial):
    """优化目标函数"""
    # 定义超参数空间
    params = {
        'n_estimators': trial.suggest_int('n_estimators', 50, 300),
        'max_depth': trial.suggest_int('max_depth', 5, 20),
        'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),
        'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 10),
        'max_features': trial.suggest_categorical('max_features', ['sqrt', 'log2', None])
    }
    
    # 创建模型
    model = RandomForestRegressor(**params, random_state=42)
    
    # 交叉验证
    scores = cross_val_score(model, X_train, y_train, cv=5, scoring='neg_mean_squared_error')
    
    return scores.mean()

def bayesian_optimization(X_train, y_train, n_trials=100):
    """贝叶斯优化"""
    # 创建研究
    study = optuna.create_study(direction='maximize')
    
    # 优化
    study.optimize(objective, n_trials=n_trials)
    
    # 最佳参数
    best_params = study.best_params
    best_score = study.best_value
    
    print(f"最佳参数: {best_params}")
    print(f"最佳得分: {best_score}")
    
    return best_params, best_score

# 使用示例
best_params, best_score = bayesian_optimization(X_train, y_train, n_trials=50)
```

### 4.1.4 模型验证方法

#### 交叉验证

**1. 时间序列交叉验证**
```python
from sklearn.model_selection import TimeSeriesSplit

def time_series_cv_validation(X, y, model, n_splits=5):
    """时间序列交叉验证"""
    tscv = TimeSeriesSplit(n_splits=n_splits)
    
    scores = []
    for train_idx, val_idx in tscv.split(X):
        X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
        y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
        
        # 训练模型
        model.fit(X_train, y_train)
        
        # 预测
        y_pred = model.predict(X_val)
        
        # 计算得分
        score = calculate_ic(y_val, y_pred)
        scores.append(score)
    
    return np.mean(scores), np.std(scores)

# 使用示例
mean_score, std_score = time_series_cv_validation(X, y, model)
print(f"交叉验证得分: {mean_score:.4f} ± {std_score:.4f}")
```

**2. 分层K折交叉验证**
```python
from sklearn.model_selection import StratifiedKFold

def stratified_cv_validation(X, y, model, n_splits=5):
    """分层K折交叉验证"""
    # 将连续标签分箱用于分层
    y_binned = pd.cut(y, bins=10, labels=False)
    
    skf = StratifiedKFold(n_splits=n_splits, shuffle=True, random_state=42)
    
    scores = []
    for train_idx, val_idx in skf.split(X, y_binned):
        X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
        y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
        
        # 训练模型
        model.fit(X_train, y_train)
        
        # 预测
        y_pred = model.predict(X_val)
        
        # 计算得分
        score = calculate_ic(y_val, y_pred)
        scores.append(score)
    
    return np.mean(scores), np.std(scores)

# 使用示例
mean_score, std_score = stratified_cv_validation(X, y, model)
print(f"分层交叉验证得分: {mean_score:.4f} ± {std_score:.4f}")
```

#### 模型稳定性验证

**1. 不同时间段的验证**
```python
def temporal_stability_validation(data, model, time_periods):
    """时间稳定性验证"""
    stability_scores = []
    
    for start_date, end_date in time_periods:
        # 分割数据
        period_data = data[(data.index >= start_date) & (data.index <= end_date)]
        
        if len(period_data) < 100:  # 数据量太少则跳过
            continue
        
        # 准备特征和标签
        X = period_data.drop(['label'], axis=1)
        y = period_data['label']
        
        # 训练模型
        model.fit(X, y)
        
        # 计算得分
        y_pred = model.predict(X)
        score = calculate_ic(y, y_pred)
        stability_scores.append(score)
    
    return np.mean(stability_scores), np.std(stability_scores)

# 使用示例
time_periods = [
    ('2020-01-01', '2020-03-31'),
    ('2020-04-01', '2020-06-30'),
    ('2020-07-01', '2020-09-30'),
    ('2020-10-01', '2020-12-31')
]

mean_score, std_score = temporal_stability_validation(data, model, time_periods)
print(f"时间稳定性得分: {mean_score:.4f} ± {std_score:.4f}")
```

**2. 不同股票组的验证**
```python
def cross_stock_validation(data, model, stock_groups):
    """跨股票组验证"""
    cross_scores = []
    
    for train_stocks, test_stocks in stock_groups:
        # 训练数据
        train_data = data[data['instrument'].isin(train_stocks)]
        X_train = train_data.drop(['label', 'instrument'], axis=1)
        y_train = train_data['label']
        
        # 测试数据
        test_data = data[data['instrument'].isin(test_stocks)]
        X_test = test_data.drop(['label', 'instrument'], axis=1)
        y_test = test_data['label']
        
        # 训练模型
        model.fit(X_train, y_train)
        
        # 预测
        y_pred = model.predict(X_test)
        
        # 计算得分
        score = calculate_ic(y_test, y_pred)
        cross_scores.append(score)
    
    return np.mean(cross_scores), np.std(cross_scores)

# 使用示例
stock_groups = [
    (['SH600000', 'SH600036'], ['SH600519', 'SH600887']),
    (['SH600519', 'SH600887'], ['SH600000', 'SH600036'])
]

mean_score, std_score = cross_stock_validation(data, model, stock_groups)
print(f"跨股票验证得分: {mean_score:.4f} ± {std_score:.4f}")
```

## 4.2 模型评估指标

### 4.2.1 IC (Information Coefficient)指标

#### IC计算原理

**IC（Information Coefficient）**是量化投资中最常用的评估指标，衡量预测值与实际收益的相关性。

**计算公式：**
```
IC = Corr(Prediction, Return)
```

**1. 基础IC计算**
```python
import numpy as np
import pandas as pd
from scipy.stats import pearsonr

def calculate_ic(predictions, returns):
    """计算IC值"""
    # 移除缺失值
    valid_mask = ~(np.isnan(predictions) | np.isnan(returns))
    pred_clean = predictions[valid_mask]
    ret_clean = returns[valid_mask]
    
    if len(pred_clean) == 0:
        return np.nan
    
    # 计算相关系数
    ic, p_value = pearsonr(pred_clean, ret_clean)
    
    return ic

# 使用示例
ic_value = calculate_ic(y_pred, y_true)
print(f"IC值: {ic_value:.4f}")
```

**2. 时间序列IC计算**
```python
def calculate_time_series_ic(predictions, returns, dates):
    """计算时间序列IC"""
    # 创建DataFrame
    df = pd.DataFrame({
        'date': dates,
        'prediction': predictions,
        'return': returns
    })
    
    # 按日期分组计算IC
    ic_series = df.groupby('date').apply(
        lambda x: calculate_ic(x['prediction'], x['return'])
    )
    
    return ic_series

# 使用示例
ic_series = calculate_time_series_ic(y_pred, y_true, dates)
print(f"IC时间序列长度: {len(ic_series)}")
print(f"平均IC: {ic_series.mean():.4f}")
```

#### ICIR计算

**ICIR（Information Coefficient Information Ratio）**是IC的标准化版本，考虑了IC的波动性。

**计算公式：**
```
ICIR = Mean(IC) / Std(IC)
```

```python
def calculate_icir(ic_series):
    """计算ICIR"""
    mean_ic = ic_series.mean()
    std_ic = ic_series.std()
    
    if std_ic == 0:
        return np.nan
    
    icir = mean_ic / std_ic
    return icir

# 使用示例
icir_value = calculate_icir(ic_series)
print(f"ICIR值: {icir_value:.4f}")
```

### 4.2.2 Rank IC和Rank ICIR

#### Rank IC计算

**Rank IC**是基于排序的IC，对异常值更鲁棒。

```python
from scipy.stats import spearmanr

def calculate_rank_ic(predictions, returns):
    """计算Rank IC"""
    # 移除缺失值
    valid_mask = ~(np.isnan(predictions) | np.isnan(returns))
    pred_clean = predictions[valid_mask]
    ret_clean = returns[valid_mask]
    
    if len(pred_clean) == 0:
        return np.nan
    
    # 计算Spearman相关系数
    rank_ic, p_value = spearmanr(pred_clean, ret_clean)
    
    return rank_ic

# 使用示例
rank_ic_value = calculate_rank_ic(y_pred, y_true)
print(f"Rank IC值: {rank_ic_value:.4f}")
```

#### Rank ICIR计算

```python
def calculate_rank_icir(rank_ic_series):
    """计算Rank ICIR"""
    mean_rank_ic = rank_ic_series.mean()
    std_rank_ic = rank_ic_series.std()
    
    if std_rank_ic == 0:
        return np.nan
    
    rank_icir = mean_rank_ic / std_rank_ic
    return rank_icir

# 使用示例
rank_icir_value = calculate_rank_icir(rank_ic_series)
print(f"Rank ICIR值: {rank_icir_value:.4f}")
```

### 4.2.3 模型性能对比分析

#### 综合评估指标

```python
def comprehensive_model_evaluation(predictions, returns, dates):
    """综合模型评估"""
    # 计算各种IC指标
    ic_series = calculate_time_series_ic(predictions, returns, dates)
    rank_ic_series = calculate_time_series_rank_ic(predictions, returns, dates)
    
    # 计算统计指标
    evaluation_metrics = {
        'Mean_IC': ic_series.mean(),
        'Std_IC': ic_series.std(),
        'ICIR': calculate_icir(ic_series),
        'Mean_Rank_IC': rank_ic_series.mean(),
        'Std_Rank_IC': rank_ic_series.std(),
        'Rank_ICIR': calculate_rank_icir(rank_ic_series),
        'IC_Positive_Rate': (ic_series > 0).mean(),
        'Rank_IC_Positive_Rate': (rank_ic_series > 0).mean()
    }
    
    return evaluation_metrics

# 使用示例
metrics = comprehensive_model_evaluation(y_pred, y_true, dates)
for metric, value in metrics.items():
    print(f"{metric}: {value:.4f}")
```

#### 模型比较框架

```python
def compare_models(model_results):
    """比较多个模型的性能"""
    comparison_df = pd.DataFrame()
    
    for model_name, results in model_results.items():
        metrics = comprehensive_model_evaluation(
            results['predictions'],
            results['returns'],
            results['dates']
        )
        
        comparison_df[model_name] = pd.Series(metrics)
    
    return comparison_df

# 使用示例
model_results = {
    'LightGBM': {'predictions': lgb_pred, 'returns': y_true, 'dates': dates},
    'XGBoost': {'predictions': xgb_pred, 'returns': y_true, 'dates': dates},
    'LSTM': {'predictions': lstm_pred, 'returns': y_true, 'dates': dates}
}

comparison = compare_models(model_results)
print(comparison)
```

## 4.3 模型部署与更新

### 4.3.1 模型序列化

#### 模型保存

**1. 使用pickle保存**
```python
import pickle
import joblib

def save_model_pickle(model, filepath):
    """使用pickle保存模型"""
    with open(filepath, 'wb') as f:
        pickle.dump(model, f)
    print(f"模型已保存到: {filepath}")

def load_model_pickle(filepath):
    """使用pickle加载模型"""
    with open(filepath, 'rb') as f:
        model = pickle.load(f)
    return model

# 使用示例
save_model_pickle(model, 'model.pkl')
loaded_model = load_model_pickle('model.pkl')
```

**2. 使用joblib保存**
```python
def save_model_joblib(model, filepath):
    """使用joblib保存模型"""
    joblib.dump(model, filepath)
    print(f"模型已保存到: {filepath}")

def load_model_joblib(filepath):
    """使用joblib加载模型"""
    model = joblib.load(filepath)
    return model

# 使用示例
save_model_joblib(model, 'model.joblib')
loaded_model = load_model_joblib('model.joblib')
```

**3. 保存模型元数据**
```python
import json
from datetime import datetime

def save_model_with_metadata(model, filepath, metadata):
    """保存模型和元数据"""
    # 保存模型
    joblib.dump(model, filepath)
    
    # 保存元数据
    metadata_file = filepath.replace('.joblib', '_metadata.json')
    metadata['save_time'] = datetime.now().isoformat()
    
    with open(metadata_file, 'w') as f:
        json.dump(metadata, f, indent=2)
    
    print(f"模型和元数据已保存到: {filepath}")

# 使用示例
metadata = {
    'model_type': 'LightGBM',
    'features': list(X.columns),
    'hyperparameters': model.get_params(),
    'training_date': '2023-01-01',
    'performance': {'IC': 0.05, 'ICIR': 1.2}
}

save_model_with_metadata(model, 'model.joblib', metadata)
```

### 4.3.2 在线模型更新

#### 增量学习

**1. 在线更新框架**
```python
class OnlineModelUpdater:
    def __init__(self, base_model, update_frequency=1):
        self.base_model = base_model
        self.update_frequency = update_frequency
        self.update_count = 0
        
    def update_model(self, new_data, new_labels):
        """更新模型"""
        self.update_count += 1
        
        if self.update_count % self.update_frequency == 0:
            # 增量训练
            self.base_model.fit(new_data, new_labels)
            print(f"模型已更新，更新次数: {self.update_count}")
        
        return self.base_model
    
    def predict(self, data):
        """预测"""
        return self.base_model.predict(data)

# 使用示例
updater = OnlineModelUpdater(model, update_frequency=5)
updated_model = updater.update_model(new_X, new_y)
predictions = updater.predict(test_X)
```

**2. 滑动窗口更新**
```python
class SlidingWindowUpdater:
    def __init__(self, base_model, window_size=252):
        self.base_model = base_model
        self.window_size = window_size
        self.data_buffer = []
        self.label_buffer = []
    
    def add_data(self, new_data, new_labels):
        """添加新数据"""
        self.data_buffer.append(new_data)
        self.label_buffer.append(new_labels)
        
        # 保持窗口大小
        if len(self.data_buffer) > self.window_size:
            self.data_buffer.pop(0)
            self.label_buffer.pop(0)
    
    def update_model(self):
        """更新模型"""
        if len(self.data_buffer) >= self.window_size:
            # 合并数据
            X = pd.concat(self.data_buffer)
            y = pd.concat(self.label_buffer)
            
            # 重新训练模型
            self.base_model.fit(X, y)
            print(f"模型已更新，使用 {len(X)} 个样本")
        
        return self.base_model

# 使用示例
updater = SlidingWindowUpdater(model, window_size=252)
updater.add_data(new_X, new_y)
updated_model = updater.update_model()
```

### 4.3.3 模型版本管理

#### 版本控制系统

```python
import os
import shutil
from datetime import datetime

class ModelVersionManager:
    def __init__(self, model_dir='models'):
        self.model_dir = model_dir
        os.makedirs(model_dir, exist_ok=True)
    
    def save_version(self, model, version, metadata):
        """保存模型版本"""
        version_dir = os.path.join(self.model_dir, f"v{version}")
        os.makedirs(version_dir, exist_ok=True)
        
        # 保存模型
        model_path = os.path.join(version_dir, 'model.joblib')
        joblib.dump(model, model_path)
        
        # 保存元数据
        metadata_path = os.path.join(version_dir, 'metadata.json')
        metadata['version'] = version
        metadata['save_time'] = datetime.now().isoformat()
        
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        print(f"模型版本 {version} 已保存")
    
    def load_version(self, version):
        """加载模型版本"""
        version_dir = os.path.join(self.model_dir, f"v{version}")
        model_path = os.path.join(version_dir, 'model.joblib')
        metadata_path = os.path.join(version_dir, 'metadata.json')
        
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型版本 {version} 不存在")
        
        # 加载模型
        model = joblib.load(model_path)
        
        # 加载元数据
        with open(metadata_path, 'r') as f:
            metadata = json.load(f)
        
        return model, metadata
    
    def list_versions(self):
        """列出所有版本"""
        versions = []
        for item in os.listdir(self.model_dir):
            if item.startswith('v'):
                version = item[1:]  # 移除'v'前缀
                versions.append(version)
        
        return sorted(versions)

# 使用示例
version_manager = ModelVersionManager()

# 保存模型版本
version_manager.save_version(model, '1.0', metadata)

# 加载模型版本
loaded_model, loaded_metadata = version_manager.load_version('1.0')

# 列出所有版本
versions = version_manager.list_versions()
print(f"可用版本: {versions}")
```

### 4.3.4 模型监控和维护

#### 性能监控

```python
class ModelMonitor:
    def __init__(self, model, threshold_ic=0.01):
        self.model = model
        self.threshold_ic = threshold_ic
        self.performance_history = []
    
    def monitor_performance(self, predictions, returns, dates):
        """监控模型性能"""
        # 计算当前性能
        ic_series = calculate_time_series_ic(predictions, returns, dates)
        current_ic = ic_series.mean()
        current_icir = calculate_icir(ic_series)
        
        # 记录性能
        performance_record = {
            'date': datetime.now(),
            'ic': current_ic,
            'icir': current_icir,
            'ic_std': ic_series.std(),
            'positive_rate': (ic_series > 0).mean()
        }
        
        self.performance_history.append(performance_record)
        
        # 检查性能是否下降
        if current_ic < self.threshold_ic:
            print(f"警告: 模型性能下降，当前IC: {current_ic:.4f}")
            return False
        
        return True
    
    def get_performance_trend(self):
        """获取性能趋势"""
        if len(self.performance_history) < 2:
            return "数据不足"
        
        recent_ic = [record['ic'] for record in self.performance_history[-10:]]
        trend = np.polyfit(range(len(recent_ic)), recent_ic, 1)[0]
        
        if trend > 0.001:
            return "上升"
        elif trend < -0.001:
            return "下降"
        else:
            return "稳定"

# 使用示例
monitor = ModelMonitor(model, threshold_ic=0.01)
is_performing = monitor.monitor_performance(y_pred, y_true, dates)
trend = monitor.get_performance_trend()
print(f"性能趋势: {trend}")
```

#### 自动重训练

```python
class AutoRetrainer:
    def __init__(self, model, retrain_threshold=0.005, retrain_frequency=30):
        self.model = model
        self.retrain_threshold = retrain_threshold
        self.retrain_frequency = retrain_frequency
        self.last_retrain = datetime.now()
        self.monitor = ModelMonitor(model)
    
    def check_and_retrain(self, new_data, new_labels, current_performance):
        """检查是否需要重训练"""
        days_since_retrain = (datetime.now() - self.last_retrain).days
        
        # 检查性能是否下降
        performance_decline = current_performance < self.retrain_threshold
        
        # 检查是否达到重训练频率
        time_to_retrain = days_since_retrain >= self.retrain_frequency
        
        if performance_decline or time_to_retrain:
            print("开始模型重训练...")
            self.model.fit(new_data, new_labels)
            self.last_retrain = datetime.now()
            print("模型重训练完成")
            return True
        
        return False

# 使用示例
auto_retrainer = AutoRetrainer(model, retrain_threshold=0.005, retrain_frequency=30)
retrained = auto_retrainer.check_and_retrain(new_X, new_y, current_ic)
```

## 本章小结

本章详细介绍了模型训练与优化的各个方面，包括：

1. **数据分割策略**：时间序列分割、滚动窗口分割等
2. **特征工程**：特征选择、技术指标、统计特征等
3. **超参数调优**：网格搜索、随机搜索、贝叶斯优化等
4. **模型验证**：交叉验证、稳定性验证等
5. **评估指标**：IC、ICIR、Rank IC等量化指标
6. **模型部署**：序列化、在线更新、版本管理等

## 课后练习

### 练习1：模型训练流程
1. 实现完整的数据分割和特征工程流程
2. 使用不同超参数调优方法优化模型
3. 进行交叉验证和稳定性验证

### 练习2：评估指标计算
1. 实现IC、ICIR、Rank IC等指标的计算
2. 比较不同模型的性能
3. 分析模型性能的稳定性

### 练习3：模型部署
1. 实现模型的序列化和加载
2. 设计在线更新机制
3. 建立模型版本管理系统

## 扩展阅读

1. **模型评估理论**
   - 《量化投资策略与技术》
   - 《机器学习模型评估》

2. **特征工程相关**
   - 《特征工程：从入门到实践》
   - 《Python数据科学手册》

3. **模型部署相关**
   - 《机器学习系统设计》
   - 《生产环境机器学习》 