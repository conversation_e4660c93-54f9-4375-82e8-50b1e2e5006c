# 第5章：投资策略设计

## 学习目标

通过本章学习，您将能够：
- 理解Qlib策略框架的设计原理
- 掌握常见投资策略的实现方法
- 学会设计和开发自定义策略
- 理解策略参数配置和优化方法
- 掌握高级策略技术和嵌套决策框架

## 5.1 策略框架介绍

### 5.1.1 Qlib策略基类设计

#### 策略基类结构

Qlib提供了统一的策略基类，所有投资策略都继承自这个基类。

```python
from qlib.strategy.base import BaseStrategy
from qlib.backtest import backtest, executor
from qlib.contrib.evaluate import risk_analysis

class MyStrategy(BaseStrategy):
    """自定义策略基类"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.risk_degree = 0.95  # 风险度
        self.benchmark = "SH000300"  # 基准
        self.universe = "csi300"  # 股票池
    
    def generate_trade_decision(self, score):
        """生成交易决策"""
        # 子类需要实现的具体逻辑
        pass
    
    def get_score(self, score):
        """获取预测分数"""
        return score
    
    def get_risk_degree(self):
        """获取风险度"""
        return self.risk_degree
```

#### 策略接口规范

**1. 核心方法**
```python
class StrategyInterface:
    """策略接口规范"""
    
    def __init__(self, **kwargs):
        """初始化策略"""
        pass
    
    def generate_trade_decision(self, score):
        """生成交易决策"""
        # 输入：预测分数
        # 输出：交易决策
        pass
    
    def get_score(self, score):
        """处理预测分数"""
        # 输入：原始分数
        # 输出：处理后的分数
        pass
    
    def get_risk_degree(self):
        """获取风险度"""
        # 输出：风险度参数
        pass
```

**2. 策略配置**
```python
class StrategyConfig:
    """策略配置类"""
    
    def __init__(self):
        self.risk_degree = 0.95
        self.benchmark = "SH000300"
        self.universe = "csi300"
        self.start_time = "2020-01-01"
        self.end_time = "2020-12-31"
        self.freq = "day"
        self.model = None
        self.data_handler = None
```

### 5.1.2 策略接口规范

#### 标准策略接口

```python
from abc import ABC, abstractmethod
import pandas as pd
import numpy as np

class BaseStrategy(ABC):
    """策略基类"""
    
    def __init__(self, risk_degree=0.95, benchmark="SH000300", universe="csi300"):
        self.risk_degree = risk_degree
        self.benchmark = benchmark
        self.universe = universe
        self.positions = {}
    
    @abstractmethod
    def generate_trade_decision(self, score):
        """生成交易决策 - 子类必须实现"""
        pass
    
    def get_score(self, score):
        """获取预测分数 - 默认实现"""
        return score
    
    def get_risk_degree(self):
        """获取风险度"""
        return self.risk_degree
    
    def update_positions(self, positions):
        """更新持仓"""
        self.positions = positions
    
    def get_positions(self):
        """获取当前持仓"""
        return self.positions
```

#### 策略工厂模式

```python
class StrategyFactory:
    """策略工厂类"""
    
    _strategies = {}
    
    @classmethod
    def register(cls, name, strategy_class):
        """注册策略"""
        cls._strategies[name] = strategy_class
    
    @classmethod
    def create(cls, name, **kwargs):
        """创建策略实例"""
        if name not in cls._strategies:
            raise ValueError(f"策略 {name} 不存在")
        
        return cls._strategies[name](**kwargs)
    
    @classmethod
    def list_strategies(cls):
        """列出所有可用策略"""
        return list(cls._strategies.keys())

# 注册策略
StrategyFactory.register("TopkDropout", TopkDropoutStrategy)
StrategyFactory.register("WeightStrategy", WeightStrategy)
```

### 5.1.3 策略参数配置

#### 参数配置系统

```python
import yaml
from dataclasses import dataclass

@dataclass
class StrategyParams:
    """策略参数配置"""
    risk_degree: float = 0.95
    benchmark: str = "SH000300"
    universe: str = "csi300"
    topk: int = 50
    n_drop: int = 5
    method_sell: str = "bottom"
    method_buy: str = "top"
    close_cost: float = 0.0005
    open_cost: float = 0.0005
    trade_unit: int = 100
    limit_threshold: float = 0.095
    max_weight: float = 0.05

def load_strategy_config(config_path):
    """加载策略配置"""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    return StrategyParams(**config)

def save_strategy_config(params, config_path):
    """保存策略配置"""
    config = {
        'risk_degree': params.risk_degree,
        'benchmark': params.benchmark,
        'universe': params.universe,
        'topk': params.topk,
        'n_drop': params.n_drop,
        'method_sell': params.method_sell,
        'method_buy': params.method_buy,
        'close_cost': params.close_cost,
        'open_cost': params.open_cost,
        'trade_unit': params.trade_unit,
        'limit_threshold': params.limit_threshold,
        'max_weight': params.max_weight
    }
    
    with open(config_path, 'w') as f:
        yaml.dump(config, f, default_flow_style=False)
```

#### 动态参数调整

```python
class AdaptiveStrategyParams:
    """自适应策略参数"""
    
    def __init__(self, base_params):
        self.base_params = base_params
        self.adaptation_history = []
    
    def adapt_to_market_conditions(self, market_data):
        """根据市场条件调整参数"""
        # 计算市场波动率
        volatility = self.calculate_market_volatility(market_data)
        
        # 调整风险度
        if volatility > 0.3:  # 高波动率
            self.base_params.risk_degree *= 0.9
        elif volatility < 0.1:  # 低波动率
            self.base_params.risk_degree *= 1.1
        
        # 限制风险度范围
        self.base_params.risk_degree = max(0.5, min(0.99, self.base_params.risk_degree))
        
        # 记录调整历史
        self.adaptation_history.append({
            'volatility': volatility,
            'risk_degree': self.base_params.risk_degree,
            'timestamp': pd.Timestamp.now()
        })
    
    def calculate_market_volatility(self, market_data):
        """计算市场波动率"""
        returns = market_data['close'].pct_change().dropna()
        return returns.std() * np.sqrt(252)
```

### 5.1.4 策略组合方法

#### 多策略组合

```python
class StrategyEnsemble:
    """策略组合类"""
    
    def __init__(self, strategies, weights=None):
        self.strategies = strategies
        self.weights = weights or [1/len(strategies)] * len(strategies)
        
        if len(self.weights) != len(self.strategies):
            raise ValueError("策略数量和权重数量不匹配")
    
    def generate_ensemble_decision(self, scores):
        """生成组合决策"""
        decisions = []
        
        for strategy, score in zip(self.strategies, scores):
            decision = strategy.generate_trade_decision(score)
            decisions.append(decision)
        
        # 加权组合决策
        ensemble_decision = self.combine_decisions(decisions)
        return ensemble_decision
    
    def combine_decisions(self, decisions):
        """组合多个决策"""
        # 简单加权平均
        combined_decision = {}
        
        for decision in decisions:
            for stock, weight in decision.items():
                if stock not in combined_decision:
                    combined_decision[stock] = 0
                combined_decision[stock] += weight
        
        # 归一化
        total_weight = sum(combined_decision.values())
        if total_weight > 0:
            combined_decision = {k: v/total_weight for k, v in combined_decision.items()}
        
        return combined_decision
```

#### 分层策略组合

```python
class HierarchicalStrategy:
    """分层策略组合"""
    
    def __init__(self, asset_allocation_strategy, stock_selection_strategies):
        self.asset_allocation_strategy = asset_allocation_strategy
        self.stock_selection_strategies = stock_selection_strategies
    
    def generate_hierarchical_decision(self, market_data, stock_scores):
        """生成分层决策"""
        # 第一层：资产配置
        asset_allocation = self.asset_allocation_strategy.generate_allocation(market_data)
        
        # 第二层：股票选择
        stock_selections = {}
        for asset_class, allocation_weight in asset_allocation.items():
            if asset_class in self.stock_selection_strategies:
                strategy = self.stock_selection_strategies[asset_class]
                stock_weights = strategy.generate_trade_decision(stock_scores[asset_class])
                
                # 调整权重
                adjusted_weights = {stock: weight * allocation_weight 
                                  for stock, weight in stock_weights.items()}
                stock_selections.update(adjusted_weights)
        
        return stock_selections
```

## 5.2 常见策略实现

### 5.2.1 多因子策略

#### 多因子模型原理

多因子策略基于多个因子来预测股票收益，是量化投资中最常用的策略之一。

```python
class MultiFactorStrategy(BaseStrategy):
    """多因子策略"""
    
    def __init__(self, factors, factor_weights=None, **kwargs):
        super().__init__(**kwargs)
        self.factors = factors
        self.factor_weights = factor_weights or [1/len(factors)] * len(factors)
    
    def calculate_factor_scores(self, data):
        """计算因子分数"""
        factor_scores = {}
        
        for factor_name in self.factors:
            if factor_name == 'momentum':
                factor_scores[factor_name] = self.calculate_momentum_factor(data)
            elif factor_name == 'value':
                factor_scores[factor_name] = self.calculate_value_factor(data)
            elif factor_name == 'quality':
                factor_scores[factor_name] = self.calculate_quality_factor(data)
            elif factor_name == 'size':
                factor_scores[factor_name] = self.calculate_size_factor(data)
        
        return factor_scores
    
    def calculate_momentum_factor(self, data):
        """计算动量因子"""
        # 计算过去20日收益率
        returns = data['close'].pct_change(periods=20)
        return returns.rank(pct=True)  # 标准化排名
    
    def calculate_value_factor(self, data):
        """计算价值因子"""
        # 使用P/E的倒数作为价值因子
        pe_ratio = data['close'] / data['eps']
        value_factor = 1 / pe_ratio
        return value_factor.rank(pct=True)
    
    def calculate_quality_factor(self, data):
        """计算质量因子"""
        # 使用ROE作为质量因子
        roe = data['net_income'] / data['equity']
        return roe.rank(pct=True)
    
    def calculate_size_factor(self, data):
        """计算规模因子"""
        # 使用市值的倒数作为规模因子
        market_cap = data['close'] * data['shares_outstanding']
        size_factor = 1 / market_cap
        return size_factor.rank(pct=True)
    
    def generate_trade_decision(self, factor_scores):
        """生成交易决策"""
        # 计算综合分数
        composite_score = pd.DataFrame()
        
        for factor_name, factor_score in factor_scores.items():
            weight = self.factor_weights[self.factors.index(factor_name)]
            composite_score[factor_name] = factor_score * weight
        
        # 综合分数
        total_score = composite_score.sum(axis=1)
        
        # 选择top-k股票
        top_stocks = total_score.nlargest(self.topk).index
        
        # 生成权重
        weights = {}
        for stock in top_stocks:
            weights[stock] = total_score[stock] / total_score[top_stocks].sum()
        
        return weights
```

#### 因子有效性检验

```python
class FactorAnalysis:
    """因子分析类"""
    
    def __init__(self):
        self.factor_ic_history = {}
    
    def calculate_factor_ic(self, factor_scores, future_returns):
        """计算因子IC值"""
        ic_values = {}
        
        for factor_name, factor_score in factor_scores.items():
            # 计算因子与未来收益的相关性
            ic = factor_score.corr(future_returns)
            ic_values[factor_name] = ic
        
        return ic_values
    
    def calculate_factor_icir(self, factor_scores, future_returns, window=252):
        """计算因子ICIR值"""
        ic_series = pd.DataFrame()
        
        for factor_name, factor_score in factor_scores.items():
            # 滚动计算IC
            rolling_ic = factor_score.rolling(window).corr(future_returns)
            ic_series[factor_name] = rolling_ic
        
        # 计算ICIR
        icir = ic_series.mean() / ic_series.std()
        return icir
    
    def factor_turnover_analysis(self, factor_scores, window=20):
        """因子换手率分析"""
        turnover_rates = {}
        
        for factor_name, factor_score in factor_scores.items():
            # 计算因子排名变化
            rank_changes = factor_score.rank(pct=True).diff().abs()
            turnover = rank_changes.rolling(window).mean()
            turnover_rates[factor_name] = turnover
        
        return turnover_rates
```

### 5.2.2 动量策略

#### 动量策略实现

```python
class MomentumStrategy(BaseStrategy):
    """动量策略"""
    
    def __init__(self, lookback_period=20, momentum_type='price', **kwargs):
        super().__init__(**kwargs)
        self.lookback_period = lookback_period
        self.momentum_type = momentum_type
    
    def calculate_momentum_scores(self, data):
        """计算动量分数"""
        if self.momentum_type == 'price':
            # 价格动量
            momentum = data['close'].pct_change(periods=self.lookback_period)
        elif self.momentum_type == 'return':
            # 收益率动量
            returns = data['close'].pct_change()
            momentum = returns.rolling(self.lookback_period).mean()
        elif self.momentum_type == 'relative':
            # 相对动量
            benchmark_returns = data['benchmark'].pct_change(periods=self.lookback_period)
            stock_returns = data['close'].pct_change(periods=self.lookback_period)
            momentum = stock_returns - benchmark_returns
        else:
            raise ValueError(f"不支持的动量类型: {self.momentum_type}")
        
        return momentum.rank(pct=True)
    
    def generate_trade_decision(self, momentum_scores):
        """生成交易决策"""
        # 选择动量最强的股票
        top_momentum = momentum_scores.nlargest(self.topk)
        
        # 生成权重
        weights = {}
        for stock in top_momentum.index:
            weights[stock] = momentum_scores[stock] / top_momentum.sum()
        
        return weights
```

#### 动量策略变体

```python
class DualMomentumStrategy(MomentumStrategy):
    """双重动量策略"""
    
    def __init__(self, short_period=10, long_period=60, **kwargs):
        super().__init__(**kwargs)
        self.short_period = short_period
        self.long_period = long_period
    
    def calculate_dual_momentum(self, data):
        """计算双重动量"""
        # 短期动量
        short_momentum = data['close'].pct_change(periods=self.short_period)
        
        # 长期动量
        long_momentum = data['close'].pct_change(periods=self.long_period)
        
        # 双重动量信号
        dual_momentum = (short_momentum > 0) & (long_momentum > 0)
        
        return dual_momentum.astype(int)
    
    def generate_trade_decision(self, dual_momentum_scores):
        """生成交易决策"""
        # 只选择双重动量为正的股票
        positive_momentum = dual_momentum_scores[dual_momentum_scores > 0]
        
        if len(positive_momentum) == 0:
            return {}  # 没有符合条件的股票
        
        # 选择动量最强的股票
        top_stocks = positive_momentum.nlargest(self.topk)
        
        # 等权重分配
        weights = {stock: 1/len(top_stocks) for stock in top_stocks.index}
        
        return weights
```

### 5.2.3 均值回归策略

#### 均值回归原理

均值回归策略基于价格会回归到均值的假设，在价格偏离均值较大时进行反向操作。

```python
class MeanReversionStrategy(BaseStrategy):
    """均值回归策略"""
    
    def __init__(self, lookback_period=60, std_threshold=2.0, **kwargs):
        super().__init__(**kwargs)
        self.lookback_period = lookback_period
        self.std_threshold = std_threshold
    
    def calculate_mean_reversion_signal(self, data):
        """计算均值回归信号"""
        # 计算移动平均
        ma = data['close'].rolling(self.lookback_period).mean()
        
        # 计算标准差
        std = data['close'].rolling(self.lookback_period).std()
        
        # 计算Z-score
        z_score = (data['close'] - ma) / std
        
        # 生成信号
        # 当价格显著低于均值时买入（Z-score < -threshold）
        # 当价格显著高于均值时卖出（Z-score > threshold）
        buy_signal = z_score < -self.std_threshold
        sell_signal = z_score > self.std_threshold
        
        return buy_signal, sell_signal, z_score
    
    def generate_trade_decision(self, signals):
        """生成交易决策"""
        buy_signal, sell_signal, z_score = signals
        
        # 选择买入信号最强的股票
        buy_candidates = z_score[buy_signal]
        
        if len(buy_candidates) == 0:
            return {}  # 没有买入机会
        
        # 选择Z-score最低的股票（偏离均值最大）
        top_buy = buy_candidates.nsmallest(self.topk)
        
        # 权重与Z-score绝对值成正比
        weights = {}
        total_weight = abs(top_buy).sum()
        
        for stock in top_buy.index:
            weights[stock] = abs(z_score[stock]) / total_weight
        
        return weights
```

#### 配对交易策略

```python
class PairsTradingStrategy(BaseStrategy):
    """配对交易策略"""
    
    def __init__(self, lookback_period=252, entry_threshold=2.0, exit_threshold=0.5, **kwargs):
        super().__init__(**kwargs)
        self.lookback_period = lookback_period
        self.entry_threshold = entry_threshold
        self.exit_threshold = exit_threshold
        self.pairs = []
    
    def find_cointegrated_pairs(self, price_data, pvalue_threshold=0.05):
        """寻找协整配对"""
        from statsmodels.tsa.stattools import coint
        
        n_stocks = len(price_data.columns)
        pairs = []
        
        for i in range(n_stocks):
            for j in range(i+1, n_stocks):
                stock1 = price_data.iloc[:, i]
                stock2 = price_data.iloc[:, j]
                
                # 协整检验
                score, pvalue, _ = coint(stock1, stock2)
                
                if pvalue < pvalue_threshold:
                    pairs.append((stock1.name, stock2.name, score, pvalue))
        
        # 按pvalue排序
        pairs.sort(key=lambda x: x[3])
        self.pairs = pairs
        
        return pairs
    
    def calculate_spread(self, stock1_prices, stock2_prices):
        """计算价差"""
        # 使用线性回归计算价差
        from sklearn.linear_model import LinearRegression
        
        model = LinearRegression()
        model.fit(stock1_prices.values.reshape(-1, 1), stock2_prices.values)
        
        # 价差 = stock2 - beta * stock1
        spread = stock2_prices - model.coef_[0] * stock1_prices
        
        return spread, model.coef_[0]
    
    def generate_trade_decision(self, price_data):
        """生成交易决策"""
        if not self.pairs:
            self.find_cointegrated_pairs(price_data)
        
        decisions = {}
        
        for stock1, stock2, score, pvalue in self.pairs[:10]:  # 选择前10个配对
            if stock1 not in price_data.columns or stock2 not in price_data.columns:
                continue
            
            # 计算价差
            spread, beta = self.calculate_spread(price_data[stock1], price_data[stock2])
            
            # 计算价差的Z-score
            spread_mean = spread.rolling(self.lookback_period).mean()
            spread_std = spread.rolling(self.lookback_period).std()
            z_score = (spread - spread_mean) / spread_std
            
            current_z = z_score.iloc[-1]
            
            # 生成交易信号
            if current_z > self.entry_threshold:
                # 价差过大，做空stock2，做多stock1
                decisions[stock1] = 1.0
                decisions[stock2] = -1.0
            elif current_z < -self.entry_threshold:
                # 价差过小，做多stock2，做空stock1
                decisions[stock1] = -1.0
                decisions[stock2] = 1.0
        
        return decisions
```

### 5.2.4 套利策略

#### 统计套利策略

```python
class StatisticalArbitrageStrategy(BaseStrategy):
    """统计套利策略"""
    
    def __init__(self, lookback_period=252, entry_threshold=2.0, **kwargs):
        super().__init__(**kwargs)
        self.lookback_period = lookback_period
        self.entry_threshold = entry_threshold
    
    def calculate_statistical_signals(self, data):
        """计算统计套利信号"""
        signals = {}
        
        for stock in data.columns:
            prices = data[stock]
            
            # 计算移动平均和标准差
            ma = prices.rolling(self.lookback_period).mean()
            std = prices.rolling(self.lookback_period).std()
            
            # 计算Z-score
            z_score = (prices - ma) / std
            
            # 生成信号
            current_z = z_score.iloc[-1]
            
            if current_z > self.entry_threshold:
                signals[stock] = -1  # 做空信号
            elif current_z < -self.entry_threshold:
                signals[stock] = 1   # 做多信号
            else:
                signals[stock] = 0   # 无信号
        
        return signals
    
    def generate_trade_decision(self, signals):
        """生成交易决策"""
        # 分离做多和做空信号
        long_signals = {stock: signal for stock, signal in signals.items() if signal == 1}
        short_signals = {stock: signal for stock, signal in signals.items() if signal == -1}
        
        decisions = {}
        
        # 分配做多权重
        if long_signals:
            long_weight = 0.5 / len(long_signals)
            for stock in long_signals:
                decisions[stock] = long_weight
        
        # 分配做空权重
        if short_signals:
            short_weight = -0.5 / len(short_signals)
            for stock in short_signals:
                decisions[stock] = short_weight
        
        return decisions
```

## 5.3 高级策略技术

### 5.3.1 嵌套决策框架

#### 嵌套决策原理

嵌套决策框架允许策略在不同层次进行决策，从大类资产配置到具体股票选择。

```python
class NestedDecisionFramework:
    """嵌套决策框架"""
    
    def __init__(self, asset_allocation_strategy, sector_allocation_strategy, stock_selection_strategy):
        self.asset_allocation_strategy = asset_allocation_strategy
        self.sector_allocation_strategy = sector_allocation_strategy
        self.stock_selection_strategy = stock_selection_strategy
    
    def generate_nested_decision(self, market_data, sector_data, stock_data):
        """生成嵌套决策"""
        # 第一层：大类资产配置
        asset_allocation = self.asset_allocation_strategy.allocate(market_data)
        
        # 第二层：行业配置
        sector_allocation = {}
        for asset_class, asset_weight in asset_allocation.items():
            if asset_class == 'equity':
                sector_weights = self.sector_allocation_strategy.allocate(sector_data)
                # 调整行业权重
                adjusted_sector_weights = {sector: weight * asset_weight 
                                        for sector, weight in sector_weights.items()}
                sector_allocation.update(adjusted_sector_weights)
        
        # 第三层：个股选择
        final_weights = {}
        for sector, sector_weight in sector_allocation.items():
            sector_stocks = stock_data[stock_data['sector'] == sector]
            if len(sector_stocks) > 0:
                stock_weights = self.stock_selection_strategy.select(sector_stocks)
                # 调整个股权重
                adjusted_stock_weights = {stock: weight * sector_weight 
                                       for stock, weight in stock_weights.items()}
                final_weights.update(adjusted_stock_weights)
        
        return final_weights
```

#### 动态资产配置

```python
class DynamicAssetAllocation:
    """动态资产配置策略"""
    
    def __init__(self, risk_free_rate=0.02, target_volatility=0.15):
        self.risk_free_rate = risk_free_rate
        self.target_volatility = target_volatility
    
    def calculate_optimal_allocation(self, returns_data):
        """计算最优资产配置"""
        # 计算预期收益率和协方差矩阵
        expected_returns = returns_data.mean()
        cov_matrix = returns_data.cov()
        
        # 使用Markowitz优化
        from scipy.optimize import minimize
        
        def objective(weights):
            portfolio_return = np.sum(weights * expected_returns)
            portfolio_vol = np.sqrt(np.dot(weights.T, np.dot(cov_matrix, weights)))
            sharpe_ratio = (portfolio_return - self.risk_free_rate) / portfolio_vol
            return -sharpe_ratio  # 最大化夏普比率
        
        # 约束条件
        constraints = [
            {'type': 'eq', 'fun': lambda x: np.sum(x) - 1},  # 权重和为1
        ]
        
        bounds = [(0, 1) for _ in range(len(expected_returns))]  # 权重在0-1之间
        
        # 初始权重
        initial_weights = np.array([1/len(expected_returns)] * len(expected_returns))
        
        # 优化
        result = minimize(objective, initial_weights, method='SLSQP', 
                        bounds=bounds, constraints=constraints)
        
        return dict(zip(returns_data.columns, result.x))
```

### 5.3.2 高频交易策略

#### 高频策略框架

```python
class HighFrequencyStrategy(BaseStrategy):
    """高频交易策略"""
    
    def __init__(self, tick_data_handler, order_manager, **kwargs):
        super().__init__(**kwargs)
        self.tick_data_handler = tick_data_handler
        self.order_manager = order_manager
        self.positions = {}
        self.pending_orders = []
    
    def process_tick_data(self, tick_data):
        """处理tick数据"""
        # 更新市场数据
        self.update_market_data(tick_data)
        
        # 生成交易信号
        signals = self.generate_signals(tick_data)
        
        # 执行交易
        self.execute_trades(signals)
    
    def update_market_data(self, tick_data):
        """更新市场数据"""
        # 更新价格、成交量、订单簿等数据
        pass
    
    def generate_signals(self, tick_data):
        """生成交易信号"""
        signals = {}
        
        # 基于订单簿的信号
        orderbook_signals = self.analyze_orderbook(tick_data)
        
        # 基于价格动量的信号
        momentum_signals = self.analyze_momentum(tick_data)
        
        # 基于统计套利的信号
        stat_arb_signals = self.analyze_statistical_arbitrage(tick_data)
        
        # 综合信号
        for stock in tick_data.keys():
            signals[stock] = self.combine_signals(
                orderbook_signals.get(stock, 0),
                momentum_signals.get(stock, 0),
                stat_arb_signals.get(stock, 0)
            )
        
        return signals
    
    def analyze_orderbook(self, tick_data):
        """分析订单簿"""
        signals = {}
        
        for stock, data in tick_data.items():
            if 'orderbook' in data:
                orderbook = data['orderbook']
                
                # 计算买卖压力
                bid_pressure = sum(orderbook['bids'].values())
                ask_pressure = sum(orderbook['asks'].values())
                
                # 计算压力比率
                pressure_ratio = bid_pressure / ask_pressure if ask_pressure > 0 else 1
                
                # 生成信号
                if pressure_ratio > 1.2:
                    signals[stock] = 1  # 买入信号
                elif pressure_ratio < 0.8:
                    signals[stock] = -1  # 卖出信号
                else:
                    signals[stock] = 0  # 无信号
        
        return signals
    
    def analyze_momentum(self, tick_data):
        """分析价格动量"""
        signals = {}
        
        for stock, data in tick_data.items():
            if 'price_history' in data:
                prices = data['price_history']
                
                # 计算短期动量
                short_momentum = (prices[-1] - prices[-10]) / prices[-10] if len(prices) >= 10 else 0
                
                # 生成信号
                if short_momentum > 0.001:  # 0.1%的动量
                    signals[stock] = 1
                elif short_momentum < -0.001:
                    signals[stock] = -1
                else:
                    signals[stock] = 0
        
        return signals
    
    def execute_trades(self, signals):
        """执行交易"""
        for stock, signal in signals.items():
            if signal != 0:
                # 计算交易量
                position = self.positions.get(stock, 0)
                target_position = signal * self.trade_unit
                
                if target_position != position:
                    # 生成订单
                    order = {
                        'stock': stock,
                        'side': 'buy' if target_position > position else 'sell',
                        'quantity': abs(target_position - position),
                        'price': self.get_market_price(stock),
                        'timestamp': pd.Timestamp.now()
                    }
                    
                    # 提交订单
                    self.order_manager.submit_order(order)
```

### 5.3.3 动态资产配置

#### 风险平价策略

```python
class RiskParityStrategy(BaseStrategy):
    """风险平价策略"""
    
    def __init__(self, target_volatility=0.10, rebalance_frequency=30, **kwargs):
        super().__init__(**kwargs)
        self.target_volatility = target_volatility
        self.rebalance_frequency = rebalance_frequency
        self.last_rebalance = None
    
    def calculate_risk_contributions(self, returns_data):
        """计算风险贡献"""
        # 计算协方差矩阵
        cov_matrix = returns_data.cov()
        
        # 计算各资产的风险贡献
        n_assets = len(returns_data.columns)
        risk_contributions = {}
        
        for i, asset in enumerate(returns_data.columns):
            # 计算边际风险贡献
            marginal_risk = cov_matrix.iloc[i, :].values
            
            # 风险贡献 = 权重 * 边际风险贡献
            risk_contributions[asset] = marginal_risk
        
        return risk_contributions
    
    def optimize_risk_parity(self, returns_data):
        """优化风险平价权重"""
        from scipy.optimize import minimize
        
        def objective(weights):
            # 计算组合风险
            cov_matrix = returns_data.cov()
            portfolio_risk = np.sqrt(np.dot(weights.T, np.dot(cov_matrix, weights)))
            
            # 计算各资产的风险贡献
            risk_contributions = np.dot(cov_matrix, weights) * weights / portfolio_risk
            
            # 风险平价目标：所有资产的风险贡献相等
            target_contribution = portfolio_risk / len(weights)
            risk_parity_error = np.sum((risk_contributions - target_contribution) ** 2)
            
            return risk_parity_error
        
        # 约束条件
        constraints = [
            {'type': 'eq', 'fun': lambda x: np.sum(x) - 1},  # 权重和为1
        ]
        
        bounds = [(0, 1) for _ in range(len(returns_data.columns))]
        
        # 初始权重
        initial_weights = np.array([1/len(returns_data.columns)] * len(returns_data.columns))
        
        # 优化
        result = minimize(objective, initial_weights, method='SLSQP', 
                        bounds=bounds, constraints=constraints)
        
        return dict(zip(returns_data.columns, result.x))
    
    def generate_trade_decision(self, returns_data):
        """生成交易决策"""
        # 检查是否需要再平衡
        if self.should_rebalance():
            # 计算风险平价权重
            weights = self.optimize_risk_parity(returns_data)
            self.last_rebalance = pd.Timestamp.now()
            return weights
        else:
            # 保持当前权重
            return self.current_weights
    
    def should_rebalance(self):
        """检查是否需要再平衡"""
        if self.last_rebalance is None:
            return True
        
        days_since_rebalance = (pd.Timestamp.now() - self.last_rebalance).days
        return days_since_rebalance >= self.rebalance_frequency
```

#### 目标风险策略

```python
class TargetRiskStrategy(BaseStrategy):
    """目标风险策略"""
    
    def __init__(self, target_volatility=0.15, risk_free_rate=0.02, **kwargs):
        super().__init__(**kwargs)
        self.target_volatility = target_volatility
        self.risk_free_rate = risk_free_rate
    
    def calculate_optimal_weights(self, returns_data):
        """计算最优权重"""
        # 计算预期收益率和协方差矩阵
        expected_returns = returns_data.mean()
        cov_matrix = returns_data.cov()
        
        # 使用最大夏普比率优化
        from scipy.optimize import minimize
        
        def objective(weights):
            portfolio_return = np.sum(weights * expected_returns)
            portfolio_vol = np.sqrt(np.dot(weights.T, np.dot(cov_matrix, weights)))
            
            # 目标：最大化夏普比率，同时控制风险
            sharpe_ratio = (portfolio_return - self.risk_free_rate) / portfolio_vol
            risk_penalty = max(0, portfolio_vol - self.target_volatility) ** 2
            
            return -(sharpe_ratio - risk_penalty)
        
        # 约束条件
        constraints = [
            {'type': 'eq', 'fun': lambda x: np.sum(x) - 1},  # 权重和为1
        ]
        
        bounds = [(0, 1) for _ in range(len(expected_returns))]
        
        # 初始权重
        initial_weights = np.array([1/len(expected_returns)] * len(expected_returns))
        
        # 优化
        result = minimize(objective, initial_weights, method='SLSQP', 
                        bounds=bounds, constraints=constraints)
        
        return dict(zip(returns_data.columns, result.x))
    
    def generate_trade_decision(self, returns_data):
        """生成交易决策"""
        # 计算最优权重
        weights = self.calculate_optimal_weights(returns_data)
        
        # 应用风险度调整
        adjusted_weights = {stock: weight * self.risk_degree 
                          for stock, weight in weights.items()}
        
        return adjusted_weights
```

## 本章小结

本章详细介绍了Qlib中的投资策略设计，包括：

1. **策略框架**：策略基类设计、接口规范、参数配置
2. **常见策略**：多因子策略、动量策略、均值回归策略、套利策略
3. **高级技术**：嵌套决策框架、高频交易、动态资产配置

## 课后练习

### 练习1：策略实现
1. 实现一个简单的多因子策略
2. 开发动量策略的变体
3. 构建均值回归策略

### 练习2：策略优化
1. 使用不同参数测试策略性能
2. 实现策略组合方法
3. 分析策略的稳定性

### 练习3：高级策略
1. 实现嵌套决策框架
2. 开发风险平价策略
3. 设计动态资产配置策略

## 扩展阅读

1. **策略设计理论**
   - 《量化投资策略与技术》
   - 《主动投资组合管理》

2. **多因子模型**
   - 《多因子投资策略》
   - 《因子投资：理论与实践》

3. **高频交易**
   - 《高频交易算法》
   - 《市场微观结构》 