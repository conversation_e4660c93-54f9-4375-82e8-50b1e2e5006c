#!/usr/bin/env python3
"""
测试特征选择修复是否有效
"""

import qlib
import numpy as np
import pandas as pd
from qlib.contrib.data.handler import Alpha158
from qlib.data.dataset import DatasetH
from sklearn.feature_selection import SelectKBest, f_regression
from sklearn.linear_model import LinearRegression
import warnings
warnings.filterwarnings('ignore')

def test_data_access_and_nan_handling():
    print("=== 测试数据访问和NaN处理 ===")
    
    # 初始化Qlib
    print("初始化Qlib...")
    qlib.init(mount_path="~/.qlib/qlib_data/cn_data", region="cn")
    
    # 准备数据处理器（使用较小的时间范围测试）
    handler = Alpha158(
        instruments='csi300',
        start_time='2020-01-01',
        end_time='2020-03-31',  # 较短时间范围
        freq='day'
    )
    
    # 创建数据集
    dataset = DatasetH(
        handler=handler,
        segments={
            'train': ('2020-01-01', '2020-02-29'),
            'test': ('2020-03-01', '2020-03-31')
        }
    )
    
    print("准备数据...")
    train_data = dataset.prepare('train')
    test_data = dataset.prepare('test')
    
    print(f"训练数据形状: {train_data.shape}")
    print(f"测试数据形状: {test_data.shape}")
    
    # 分离特征和标签（使用修复后的方法）
    label_cols = [col for col in train_data.columns if 'LABEL' in str(col)]
    feature_cols = [col for col in train_data.columns if 'LABEL' not in str(col)]
    
    print(f"特征列数量: {len(feature_cols)}")
    print(f"标签列数量: {len(label_cols)}")
    print(f"标签列名: {label_cols}")
    
    X_train = train_data[feature_cols]
    y_train = train_data[label_cols[0]]
    X_test = test_data[feature_cols]
    y_test = test_data[label_cols[0]]
    
    # 检查NaN情况
    print(f"\n原始NaN情况:")
    print(f"训练特征NaN数量: {X_train.isna().sum().sum()}")
    print(f"训练标签NaN数量: {y_train.isna().sum()}")
    print(f"测试特征NaN数量: {X_test.isna().sum().sum()}")
    print(f"测试标签NaN数量: {y_test.isna().sum()}")
    
    # 处理NaN值（使用修复后的方法）
    print("\n处理NaN值...")
    X_train_clean = X_train.fillna(X_train.mean()).fillna(0)
    X_test_clean = X_test.fillna(X_train.mean()).fillna(0)
    y_train_clean = y_train.fillna(y_train.median())
    y_test_clean = y_test.fillna(y_train.median())
    
    # 验证NaN清除
    print(f"清理后NaN情况:")
    print(f"训练特征NaN数量: {X_train_clean.isna().sum().sum()}")
    print(f"训练标签NaN数量: {y_train_clean.isna().sum()}")
    print(f"测试特征NaN数量: {X_test_clean.isna().sum().sum()}")
    print(f"测试标签NaN数量: {y_test_clean.isna().sum()}")
    
    # 测试基线模型
    print("\n测试基线模型...")
    try:
        model = LinearRegression()
        model.fit(X_train_clean, y_train_clean)
        pred = model.predict(X_test_clean)
        print("✅ 基线模型训练成功!")
        print(f"预测结果形状: {pred.shape}")
        
        # 简单的特征选择测试
        print("\n测试特征选择...")
        selector = SelectKBest(score_func=f_regression, k=10)
        X_train_selected = selector.fit_transform(X_train_clean, y_train_clean)
        X_test_selected = selector.transform(X_test_clean)
        
        selected_features = X_train_clean.columns[selector.get_support()].tolist()
        print(f"✅ 特征选择成功!")
        print(f"选择的特征数量: {len(selected_features)}")
        print(f"选择的特征: {selected_features[:5]}...")
        
        # 测试选择后的模型
        model_selected = LinearRegression()
        model_selected.fit(X_train_selected, y_train_clean)
        pred_selected = model_selected.predict(X_test_selected)
        print("✅ 特征选择后的模型训练成功!")
        
    except Exception as e:
        print(f"❌ 模型训练失败: {e}")
        return False
    
    print("\n=== 所有测试通过! ===")
    return True

if __name__ == "__main__":
    success = test_data_access_and_nan_handling()
    if success:
        print("修复有效，可以应用到完整脚本")
    else:
        print("修复存在问题，需要进一步调试")