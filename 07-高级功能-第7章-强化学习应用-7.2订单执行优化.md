# 第7.2节：订单执行优化

## 学习目标

通过本节学习，您将能够：
- 理解订单执行优化的基本原理
- 掌握TWAP策略的实现方法
- 学会使用PPO算法优化订单执行
- 掌握OPDS策略的设计和实现

## 7.2.1 TWAP策略实现

### TWAP算法原理

TWAP（Time-Weighted Average Price）是一种常用的订单执行策略，将大订单在指定时间段内均匀分布执行。

```python
class TWAPStrategy:
    """TWAP策略实现"""
    
    def __init__(self, total_quantity, time_horizon, num_slices):
        self.total_quantity = total_quantity
        self.time_horizon = time_horizon
        self.num_slices = num_slices
        self.slice_quantity = total_quantity / num_slices
        self.current_slice = 0
    
    def get_order_quantity(self, current_time):
        """获取当前时间点的订单数量"""
        if self.current_slice < self.num_slices:
            self.current_slice += 1
            return self.slice_quantity
        else:
            return 0
    
    def execute_twap(self, market_data):
        """执行TWAP策略"""
        orders = []
        
        for i in range(self.num_slices):
            # 计算时间点
            time_point = i * self.time_horizon / self.num_slices
            
            # 获取订单数量
            quantity = self.get_order_quantity(time_point)
            
            if quantity > 0:
                # 获取市场价格
                market_price = self.get_market_price(market_data, time_point)
                
                # 创建订单
                order = {
                    'quantity': quantity,
                    'price': market_price,
                    'time': time_point,
                    'type': 'MARKET'
                }
                orders.append(order)
        
        return orders
    
    def get_market_price(self, market_data, time_point):
        """获取市场价格"""
        # 根据时间点获取对应的市场价格
        # 这里简化处理，实际应该根据具体的时间索引获取
        return market_data['close'].iloc[int(time_point)]

class AdaptiveTWAP(TWAPStrategy):
    """自适应TWAP策略"""
    
    def __init__(self, total_quantity, time_horizon, num_slices, volatility_threshold=0.02):
        super().__init__(total_quantity, time_horizon, num_slices)
        self.volatility_threshold = volatility_threshold
        self.execution_history = []
    
    def calculate_volatility(self, market_data, current_time, window=20):
        """计算市场波动率"""
        if current_time >= window:
            recent_returns = market_data['close'].iloc[current_time-window:current_time].pct_change().dropna()
            volatility = recent_returns.std()
        else:
            volatility = 0
        
        return volatility
    
    def adjust_execution_speed(self, volatility, base_quantity):
        """根据波动率调整执行速度"""
        if volatility > self.volatility_threshold:
            # 高波动时减少执行数量
            adjusted_quantity = base_quantity * (1 - volatility * 2)
        else:
            # 低波动时正常执行
            adjusted_quantity = base_quantity
        
        return max(0, adjusted_quantity)
    
    def execute_adaptive_twap(self, market_data):
        """执行自适应TWAP策略"""
        orders = []
        
        for i in range(self.num_slices):
            time_point = i * self.time_horizon / self.num_slices
            current_time = int(time_point)
            
            # 计算市场波动率
            volatility = self.calculate_volatility(market_data, current_time)
            
            # 获取基础订单数量
            base_quantity = self.get_order_quantity(time_point)
            
            # 根据波动率调整执行数量
            adjusted_quantity = self.adjust_execution_speed(volatility, base_quantity)
            
            if adjusted_quantity > 0:
                market_price = self.get_market_price(market_data, time_point)
                
                order = {
                    'quantity': adjusted_quantity,
                    'price': market_price,
                    'time': time_point,
                    'volatility': volatility,
                    'type': 'ADAPTIVE_MARKET'
                }
                orders.append(order)
        
        return orders
```

### 强化学习TWAP优化

```python
class RL_TWAPStrategy:
    """强化学习TWAP策略"""
    
    def __init__(self, total_quantity, time_horizon, num_slices):
        self.total_quantity = total_quantity
        self.time_horizon = time_horizon
        self.num_slices = num_slices
        self.remaining_quantity = total_quantity
        self.current_slice = 0
    
    def get_state(self, market_data, current_time):
        """获取状态"""
        # 市场状态
        current_price = market_data['close'].iloc[current_time]
        current_volume = market_data['volume'].iloc[current_time]
        
        # 策略状态
        remaining_ratio = self.remaining_quantity / self.total_quantity
        time_ratio = current_time / self.time_horizon
        slice_ratio = self.current_slice / self.num_slices
        
        state = np.array([
            current_price,
            current_volume,
            remaining_ratio,
            time_ratio,
            slice_ratio
        ])
        
        return state
    
    def get_action(self, state, model):
        """获取动作"""
        # 使用强化学习模型预测动作
        action = model.predict(state)
        
        # 动作范围：[0, 1] 表示执行比例
        execution_ratio = np.clip(action, 0, 1)
        
        return execution_ratio
    
    def execute_action(self, action, market_data, current_time):
        """执行动作"""
        # 计算执行数量
        execution_quantity = action * self.remaining_quantity
        
        # 获取市场价格
        market_price = market_data['close'].iloc[current_time]
        
        # 执行交易
        if execution_quantity > 0:
            # 计算执行成本
            execution_cost = self.calculate_execution_cost(execution_quantity, market_price)
            
            # 更新剩余数量
            self.remaining_quantity -= execution_quantity
            
            # 更新切片计数
            self.current_slice += 1
            
            return {
                'quantity': execution_quantity,
                'price': market_price,
                'cost': execution_cost
            }
        
        return None
    
    def calculate_execution_cost(self, quantity, price):
        """计算执行成本"""
        # 简化的执行成本计算
        # 实际应用中需要考虑市场冲击、滑点等因素
        base_cost = quantity * price * 0.0005  # 0.05% 手续费
        market_impact = quantity * price * 0.0001  # 0.01% 市场冲击
        
        return base_cost + market_impact
```

## 7.2.2 PPO算法应用

### PPO算法实现

```python
import torch
import torch.nn as nn
import torch.optim as optim
from torch.distributions import Normal

class PPONetwork(nn.Module):
    """PPO网络"""
    
    def __init__(self, state_dim, action_dim, hidden_dim=64):
        super().__init__()
        
        # 策略网络
        self.policy_net = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, action_dim),
            nn.Tanh()  # 输出范围 [-1, 1]
        )
        
        # 价值网络
        self.value_net = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )
    
    def forward(self, state):
        """前向传播"""
        action_mean = self.policy_net(state)
        value = self.value_net(state)
        
        return action_mean, value
    
    def get_action(self, state):
        """获取动作"""
        state = torch.FloatTensor(state)
        action_mean, value = self.forward(state)
        
        # 添加探索噪声
        action_std = 0.1
        action_dist = Normal(action_mean, action_std)
        action = action_dist.sample()
        
        return action.detach().numpy(), value.detach().numpy()

class PPOAgent:
    """PPO智能体"""
    
    def __init__(self, state_dim, action_dim, lr=3e-4, gamma=0.99, epsilon=0.2):
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.lr = lr
        self.gamma = gamma
        self.epsilon = epsilon
        
        self.network = PPONetwork(state_dim, action_dim)
        self.optimizer = optim.Adam(self.network.parameters(), lr=lr)
        
        self.memory = []
    
    def store_transition(self, state, action, reward, next_state, done):
        """存储转换"""
        self.memory.append({
            'state': state,
            'action': action,
            'reward': reward,
            'next_state': next_state,
            'done': done
        })
    
    def update(self, batch_size=64):
        """更新网络"""
        if len(self.memory) < batch_size:
            return
        
        # 采样批次数据
        batch = np.random.choice(self.memory, batch_size, replace=False)
        
        states = torch.FloatTensor([t['state'] for t in batch])
        actions = torch.FloatTensor([t['action'] for t in batch])
        rewards = torch.FloatTensor([t['reward'] for t in batch])
        next_states = torch.FloatTensor([t['next_state'] for t in batch])
        dones = torch.FloatTensor([t['done'] for t in batch])
        
        # 计算优势函数
        advantages = self.calculate_advantages(states, rewards, next_states, dones)
        
        # 计算新旧策略比率
        old_action_mean, _ = self.network(states)
        new_action_mean, _ = self.network(states)
        
        ratio = torch.exp(new_action_mean - old_action_mean)
        
        # PPO损失
        surr1 = ratio * advantages
        surr2 = torch.clamp(ratio, 1-self.epsilon, 1+self.epsilon) * advantages
        
        policy_loss = -torch.min(surr1, surr2).mean()
        value_loss = nn.MSELoss()(self.network(states)[1], rewards)
        
        total_loss = policy_loss + 0.5 * value_loss
        
        # 更新网络
        self.optimizer.zero_grad()
        total_loss.backward()
        self.optimizer.step()
        
        # 清空记忆
        self.memory = []
    
    def calculate_advantages(self, states, rewards, next_states, dones):
        """计算优势函数"""
        # 简化的优势函数计算
        # 实际应用中应该使用GAE等方法
        values = self.network(states)[1].detach()
        next_values = self.network(next_states)[1].detach()
        
        advantages = rewards + self.gamma * next_values * (1 - dones) - values
        
        return advantages

class PPOOrderExecutor:
    """PPO订单执行器"""
    
    def __init__(self, total_quantity, time_horizon, state_dim=5):
        self.total_quantity = total_quantity
        self.time_horizon = time_horizon
        self.remaining_quantity = total_quantity
        self.current_time = 0
        
        # PPO智能体
        self.agent = PPOAgent(state_dim, 1)  # 1个动作：执行比例
    
    def get_execution_state(self, market_data):
        """获取执行状态"""
        current_price = market_data['close'].iloc[self.current_time]
        current_volume = market_data['volume'].iloc[self.current_time]
        
        # 计算市场特征
        if self.current_time > 0:
            price_change = (current_price - market_data['close'].iloc[self.current_time-1]) / market_data['close'].iloc[self.current_time-1]
        else:
            price_change = 0
        
        # 计算波动率
        if self.current_time >= 20:
            recent_returns = market_data['close'].iloc[self.current_time-20:self.current_time].pct_change().dropna()
            volatility = recent_returns.std()
        else:
            volatility = 0
        
        state = np.array([
            current_price,
            current_volume,
            price_change,
            volatility,
            self.remaining_quantity / self.total_quantity
        ])
        
        return state
    
    def execute_order(self, market_data):
        """执行订单"""
        if self.remaining_quantity <= 0 or self.current_time >= self.time_horizon:
            return None
        
        # 获取状态
        state = self.get_execution_state(market_data)
        
        # 获取动作
        action, _ = self.agent.get_action(state)
        execution_ratio = np.clip(action[0], 0, 1)
        
        # 计算执行数量
        execution_quantity = execution_ratio * self.remaining_quantity
        
        # 获取市场价格
        market_price = market_data['close'].iloc[self.current_time]
        
        # 执行交易
        if execution_quantity > 0:
            execution_cost = self.calculate_execution_cost(execution_quantity, market_price)
            
            # 更新剩余数量
            self.remaining_quantity -= execution_quantity
            
            # 计算奖励
            reward = self.calculate_reward(execution_quantity, market_price)
            
            # 更新状态
            next_state = self.get_execution_state(market_data)
            
            # 存储经验
            self.agent.store_transition(state, action, reward, next_state, False)
            
            # 更新智能体
            if len(self.agent.memory) >= 32:
                self.agent.update()
            
            self.current_time += 1
            
            return {
                'quantity': execution_quantity,
                'price': market_price,
                'cost': execution_cost,
                'reward': reward
            }
        
        return None
    
    def calculate_execution_cost(self, quantity, price):
        """计算执行成本"""
        base_cost = quantity * price * 0.0005  # 手续费
        market_impact = quantity * price * (quantity / 1000000) * 0.1  # 市场冲击
        
        return base_cost + market_impact
    
    def calculate_reward(self, quantity, price):
        """计算奖励"""
        # 简化的奖励函数
        # 实际应用中应该考虑更复杂的因素
        
        # 执行效率奖励
        execution_efficiency = quantity / self.total_quantity
        
        # 成本惩罚
        cost_penalty = -self.calculate_execution_cost(quantity, price) / (quantity * price)
        
        # 总奖励
        reward = execution_efficiency + cost_penalty
        
        return reward
```

## 7.2.3 OPDS策略设计

### OPDS算法实现

```python
class OPDSStrategy:
    """OPDS (Order Placement and Dynamic Scheduling) 策略"""
    
    def __init__(self, total_quantity, time_horizon, market_impact_model):
        self.total_quantity = total_quantity
        self.time_horizon = time_horizon
        self.market_impact_model = market_impact_model
        self.remaining_quantity = total_quantity
        self.execution_history = []
    
    def get_optimal_schedule(self, market_data, current_time):
        """获取最优执行计划"""
        # 计算剩余时间和数量
        remaining_time = self.time_horizon - current_time
        remaining_quantity = self.remaining_quantity
        
        # 获取市场状态
        market_state = self.get_market_state(market_data, current_time)
        
        # 使用强化学习模型预测最优执行计划
        optimal_schedule = self.predict_optimal_schedule(market_state, remaining_time, remaining_quantity)
        
        return optimal_schedule
    
    def get_market_state(self, market_data, current_time):
        """获取市场状态"""
        # 价格信息
        current_price = market_data['close'].iloc[current_time]
        price_history = market_data['close'].iloc[max(0, current_time-20):current_time]
        
        # 成交量信息
        current_volume = market_data['volume'].iloc[current_time]
        volume_history = market_data['volume'].iloc[max(0, current_time-20):current_time]
        
        # 波动率
        if len(price_history) > 1:
            volatility = price_history.pct_change().dropna().std()
        else:
            volatility = 0
        
        # 市场冲击估计
        market_impact = self.estimate_market_impact(current_volume, self.remaining_quantity)
        
        state = {
            'current_price': current_price,
            'price_history': price_history.values,
            'current_volume': current_volume,
            'volume_history': volume_history.values,
            'volatility': volatility,
            'market_impact': market_impact,
            'remaining_quantity': self.remaining_quantity,
            'remaining_time': self.time_horizon - current_time
        }
        
        return state
    
    def predict_optimal_schedule(self, market_state, remaining_time, remaining_quantity):
        """预测最优执行计划"""
        # 这里应该使用训练好的强化学习模型
        # 简化实现，使用启发式方法
        
        # 计算基础执行速度
        base_speed = remaining_quantity / remaining_time
        
        # 根据市场状态调整执行速度
        volatility_factor = 1 + market_state['volatility'] * 10
        impact_factor = 1 - market_state['market_impact'] * 0.5
        
        adjusted_speed = base_speed * volatility_factor * impact_factor
        
        # 确保执行速度在合理范围内
        adjusted_speed = np.clip(adjusted_speed, 0, remaining_quantity)
        
        return adjusted_speed
    
    def estimate_market_impact(self, current_volume, order_quantity):
        """估计市场冲击"""
        # 简化的市场冲击模型
        # 实际应用中应该使用更复杂的模型
        
        # 订单大小相对于市场成交量的比例
        volume_ratio = order_quantity / current_volume
        
        # 市场冲击与订单大小成正比
        market_impact = volume_ratio * 0.1  # 10% 的冲击系数
        
        return market_impact
    
    def execute_order(self, order_quantity, market_data, current_time):
        """执行订单"""
        if order_quantity <= 0 or self.remaining_quantity <= 0:
            return None
        
        # 获取市场价格
        market_price = market_data['close'].iloc[current_time]
        
        # 计算实际执行数量
        actual_quantity = min(order_quantity, self.remaining_quantity)
        
        # 计算执行成本
        execution_cost = self.calculate_execution_cost(actual_quantity, market_price, market_data, current_time)
        
        # 更新剩余数量
        self.remaining_quantity -= actual_quantity
        
        # 记录执行历史
        execution_record = {
            'time': current_time,
            'quantity': actual_quantity,
            'price': market_price,
            'cost': execution_cost
        }
        self.execution_history.append(execution_record)
        
        return execution_record
    
    def calculate_execution_cost(self, quantity, price, market_data, current_time):
        """计算执行成本"""
        # 基础交易成本
        base_cost = quantity * price * 0.0005  # 0.05% 手续费
        
        # 市场冲击成本
        current_volume = market_data['volume'].iloc[current_time]
        market_impact = quantity * price * (quantity / current_volume) * 0.1
        
        # 时间成本（机会成本）
        time_cost = quantity * price * 0.0001  # 简化的时间成本
        
        total_cost = base_cost + market_impact + time_cost
        
        return total_cost

class AdaptiveOPDS(OPDSStrategy):
    """自适应OPDS策略"""
    
    def __init__(self, total_quantity, time_horizon, market_impact_model, learning_rate=0.01):
        super().__init__(total_quantity, time_horizon, market_impact_model)
        self.learning_rate = learning_rate
        self.performance_history = []
    
    def adapt_strategy(self, performance_metric):
        """根据性能指标调整策略"""
        self.performance_history.append(performance_metric)
        
        if len(self.performance_history) > 10:
            # 计算性能趋势
            recent_performance = self.performance_history[-10:]
            performance_trend = np.mean(np.diff(recent_performance))
            
            # 根据趋势调整策略参数
            if performance_trend < 0:
                # 性能下降，增加探索
                self.learning_rate *= 1.1
            else:
                # 性能提升，减少探索
                self.learning_rate *= 0.9
            
            # 限制学习率范围
            self.learning_rate = np.clip(self.learning_rate, 0.001, 0.1)
    
    def calculate_performance_metric(self):
        """计算性能指标"""
        if len(self.execution_history) < 2:
            return 0
        
        # 计算平均执行价格
        total_cost = sum(exec['cost'] for exec in self.execution_history)
        total_quantity = sum(exec['quantity'] for exec in self.execution_history)
        
        if total_quantity > 0:
            avg_price = total_cost / total_quantity
        else:
            avg_price = 0
        
        # 计算市场平均价格
        market_prices = [exec['price'] for exec in self.execution_history]
        market_avg_price = np.mean(market_prices)
        
        # 性能指标：相对于市场平均价格的改善
        performance = (market_avg_price - avg_price) / market_avg_price
        
        return performance
```

### 执行效果评估

```python
class ExecutionEvaluator:
    """执行效果评估器"""
    
    def __init__(self):
        self.evaluation_metrics = {}
    
    def evaluate_execution(self, execution_history, market_data):
        """评估执行效果"""
        # 计算执行成本
        total_cost = sum(exec['cost'] for exec in execution_history)
        total_quantity = sum(exec['quantity'] for exec in execution_history)
        
        # 计算平均执行价格
        if total_quantity > 0:
            avg_execution_price = total_cost / total_quantity
        else:
            avg_execution_price = 0
        
        # 计算市场平均价格
        execution_times = [exec['time'] for exec in execution_history]
        market_prices = [market_data['close'].iloc[time] for time in execution_times]
        market_avg_price = np.mean(market_prices)
        
        # 计算各种指标
        metrics = {
            'total_cost': total_cost,
            'total_quantity': total_quantity,
            'avg_execution_price': avg_execution_price,
            'market_avg_price': market_avg_price,
            'price_improvement': (market_avg_price - avg_execution_price) / market_avg_price,
            'execution_efficiency': total_quantity / len(execution_history),
            'cost_per_share': total_cost / total_quantity if total_quantity > 0 else 0
        }
        
        self.evaluation_metrics = metrics
        return metrics
    
    def generate_report(self):
        """生成评估报告"""
        report = f"""
        执行效果评估报告
        =================
        
        总执行成本: {self.evaluation_metrics['total_cost']:.2f}
        总执行数量: {self.evaluation_metrics['total_quantity']:.0f}
        平均执行价格: {self.evaluation_metrics['avg_execution_price']:.4f}
        市场平均价格: {self.evaluation_metrics['market_avg_price']:.4f}
        价格改善: {self.evaluation_metrics['price_improvement']:.2%}
        执行效率: {self.evaluation_metrics['execution_efficiency']:.2f}
        每股成本: {self.evaluation_metrics['cost_per_share']:.4f}
        """
        
        return report
    
    def plot_execution_analysis(self, execution_history, market_data):
        """绘制执行分析图表"""
        import matplotlib.pyplot as plt
        
        # 提取数据
        execution_times = [exec['time'] for exec in execution_history]
        execution_prices = [exec['price'] for exec in execution_history]
        market_prices = [market_data['close'].iloc[time] for time in execution_times]
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        
        # 价格对比图
        ax1.plot(execution_times, execution_prices, 'ro-', label='执行价格', markersize=8)
        ax1.plot(execution_times, market_prices, 'bo-', label='市场价格', markersize=8)
        ax1.set_title('执行价格 vs 市场价格')
        ax1.set_xlabel('时间')
        ax1.set_ylabel('价格')
        ax1.legend()
        ax1.grid(True)
        
        # 价格差异图
        price_differences = [(mp - ep) / mp for ep, mp in zip(execution_prices, market_prices)]
        ax2.bar(execution_times, price_differences, color='green', alpha=0.7)
        ax2.set_title('价格改善百分比')
        ax2.set_xlabel('时间')
        ax2.set_ylabel('价格改善 (%)')
        ax2.grid(True)
        
        plt.tight_layout()
        plt.show()
```

## 本节小结

本节详细介绍了订单执行优化的各种方法，包括：

1. **TWAP策略**：基础TWAP实现和自适应TWAP策略
2. **PPO算法**：PPO网络设计、智能体实现和订单执行应用
3. **OPDS策略**：动态调度算法和自适应优化

## 课后练习

### 练习1：TWAP策略
1. 实现基础TWAP策略
2. 添加自适应机制
3. 测试不同市场条件下的表现

### 练习2：PPO算法
1. 实现PPO网络和智能体
2. 训练智能体进行订单执行
3. 分析训练效果和性能

### 练习3：OPDS策略
1. 实现OPDS算法
2. 添加自适应机制
3. 评估执行效果

## 扩展阅读

1. **订单执行理论**
   - 《算法交易》
   - 《市场微观结构》

2. **强化学习应用**
   - 《强化学习在金融中的应用》
   - 《深度强化学习》 