#!/usr/bin/env python3

import qlib
from qlib.contrib.data.handler import Alpha158
from qlib.data.dataset import DatasetH
import pandas as pd
import numpy as np

def demo_alpha158_dataset():
    """演示Alpha158数据集"""
    print("=== Alpha158数据集演示 ===")
    
    # 初始化Qlib（请根据实际数据路径修改）
    qlib.init(mount_path="~/.qlib/qlib_data/cn_data", region="cn")
    
    print("Alpha158数据集特点:")
    print("- 特征数量: 158个")
    print("- 数据频率: 日频")
    print("- 特征类型: 价格、成交量、技术指标")
    print("- 适用场景: 传统机器学习模型")
    
    # 创建Alpha158数据处理器
    handler = Alpha158(
        instruments='csi300',
        start_time='2020-01-01',
        end_time='2020-12-31',
        fit_start_time='2020-01-01',
        fit_end_time='2020-06-30',
        freq='day'
    )
    
    print(f"\n创建Alpha158处理器:")
    print(f"- 股票池: CSI300")
    print(f"- 时间范围: 2020-01-01 到 2020-12-31")
    print(f"- 拟合时间: 2020-01-01 到 2020-06-30")
    
    # 创建数据集
    dataset = DatasetH(
        handler=handler,
        segments={
            'train': ('2020-01-01', '2020-06-30'),
            'valid': ('2020-07-01', '2020-09-30'),
            'test': ('2020-10-01', '2020-12-31')
        }
    )
    
    print(f"\n数据集分割:")
    print(f"- 训练集: 2020-01-01 到 2020-06-30")
    print(f"- 验证集: 2020-07-01 到 2020-09-30")
    print(f"- 测试集: 2020-10-01 到 2020-12-31")
    
    # 获取数据样本
    train_data = dataset.prepare('train')
    
    # 分离特征和标签
    label_cols = [col for col in train_data.columns if 'LABEL' in str(col)]
    feature_cols = [col for col in train_data.columns if 'LABEL' not in str(col)]
    
    features = train_data[feature_cols]
    labels = train_data[label_cols]
    
    print(f"\n训练集数据形状:")
    print(f"- 特征: {features.shape}")
    print(f"- 标签: {labels.shape}")
    
    # 展示特征名称（前20个）
    feature_names = features.columns.tolist()
    print(f"\n特征示例（前20个）:")
    for i, name in enumerate(feature_names[:20], 1):
        print(f"{i:2d}. {name}")
    
    print(f"... 还有{len(feature_names) - 20}个特征")
    
    # 数据统计
    print(f"\n数据统计:")
    stats = features.describe()
    print(stats.iloc[:, :5])  # 显示前5个特征的统计信息
    
    print("\n✅ Alpha158数据集演示成功完成!")

if __name__ == "__main__":
    demo_alpha158_dataset()