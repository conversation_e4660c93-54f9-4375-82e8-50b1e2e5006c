#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qlib 完整功能示例
展示数据获取、特征工程、模型训练、回测分析的完整流程
"""

import qlib
from qlib.constant import REG_CN
from qlib.data import D
from qlib.utils import init_instance_by_config
import pandas as pd
import numpy as np

def init_qlib():
    """初始化Qlib"""
    print("🚀 初始化Qlib...")
    qlib.init(provider_uri="~/.qlib/qlib_data/cn_data", region=REG_CN)
    print("✅ Qlib初始化完成")

def explore_data():
    """探索数据"""
    print("\n📊 数据探索")
    print("=" * 50)
    
    # 获取股票列表
    all_instruments = D.list_instruments(D.instruments(), as_list=True)
    print(f"总股票数量: {len(all_instruments)}")
    
    # 获取CSI300股票列表
    csi300_instruments = D.list_instruments(D.instruments("csi300"), as_list=True)
    print(f"CSI300股票数量: {len(csi300_instruments)}")
    print(f"CSI300前10只股票: {csi300_instruments[:10]}")
    
    # 获取交易日历
    calendar = D.calendar(start_time='2020-01-01', end_time='2020-01-31')
    print(f"\n2020年1月交易日数量: {len(calendar)}")
    
    # 获取样本股票数据
    sample_stocks = csi300_instruments[:5]
    print(f"\n样本股票: {sample_stocks}")
    
    # 获取基础价格数据
    price_data = D.features(
        sample_stocks, 
        ['$close', '$volume', '$high', '$low'], 
        start_time='2020-01-01', 
        end_time='2020-01-10'
    )
    print(f"\n价格数据形状: {price_data.shape}")
    print("\n价格数据样本:")
    print(price_data.head(10))
    
    return sample_stocks

def feature_engineering():
    """特征工程示例"""
    print("\n🔧 特征工程")
    print("=" * 50)
    
    # 获取Alpha158特征
    sample_stocks = D.list_instruments(D.instruments("csi300"), as_list=True)[:10]
    
    # 使用表达式计算技术指标
    expressions = [
        # 价格相关
        "($close - Ref($close, 1)) / Ref($close, 1)",  # 日收益率
        "Mean($close, 5) / $close",  # 5日均线比率
        "Std($close, 20)",  # 20日价格波动率
        # 成交量相关
        "$volume / Mean($volume, 20)",  # 成交量比率
        # 技术指标
        "($high - $low) / $close",  # 振幅
        "($close - $open) / $open",  # 日内收益率
    ]
    
    print(f"计算 {len(sample_stocks)} 只股票的 {len(expressions)} 个特征...")
    
    try:
        features = D.features(
            sample_stocks,
            expressions,
            start_time='2019-01-01',
            end_time='2020-12-31'
        )
        print(f"特征数据形状: {features.shape}")
        print("\n特征统计:")
        print(features.describe())
        
        return features
    except Exception as e:
        print(f"特征计算出错: {e}")
        return None

def create_simple_model():
    """创建简单的线性模型"""
    print("\n🤖 模型训练")
    print("=" * 50)
    
    # 配置线性模型
    model_config = {
        'class': 'LinearModel',
        'module_path': 'qlib.contrib.model.linear'
    }
    
    # 配置数据集
    dataset_config = {
        'class': 'DatasetH',
        'module_path': 'qlib.data.dataset',
        'kwargs': {
            'handler': {
                'class': 'Alpha158',
                'module_path': 'qlib.contrib.data.handler',
                'kwargs': {
                    'start_time': '2008-01-01',
                    'end_time': '2020-08-01',
                    'fit_start_time': '2008-01-01',
                    'fit_end_time': '2014-12-31',
                    'instruments': 'csi300'
                }
            },
            'segments': {
                'train': ['2008-01-01', '2014-12-31'],
                'valid': ['2015-01-01', '2016-12-31'], 
                'test': ['2017-01-01', '2020-08-01']
            }
        }
    }
    
    try:
        # 创建模型和数据集
        model = init_instance_by_config(model_config)
        dataset = init_instance_by_config(dataset_config)
        
        print("✅ 模型和数据集创建成功")
        print(f"模型类型: {type(model).__name__}")
        print(f"数据集类型: {type(dataset).__name__}")
        
        # 准备训练数据
        train_data = dataset.prepare('train', col_set=['feature', 'label'])
        print(f"\n训练数据形状: {train_data.shape}")
        print(f"特征数量: {train_data['feature'].shape[1]}")
        print(f"样本数量: {len(train_data)}")
        
        # 检查数据质量
        feature_data = train_data['feature']
        label_data = train_data['label']
        
        print(f"\n数据质量检查:")
        print(f"特征缺失值: {feature_data.isnull().sum().sum()}")
        print(f"标签缺失值: {label_data.isnull().sum().sum()}")
        print(f"标签范围: {label_data.min().iloc[0]:.4f} 到 {label_data.max().iloc[0]:.4f}")
        
        # 训练模型
        print("\n开始训练模型...")
        model.fit(dataset)
        print("✅ 模型训练完成")
        
        # 预测
        pred = model.predict(dataset, segment='test')
        print(f"\n预测结果形状: {pred.shape}")
        print("预测结果样本:")
        print(pred.head())
        
        return model, dataset, pred
        
    except Exception as e:
        print(f"模型训练出错: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

def analyze_predictions(pred):
    """分析预测结果"""
    if pred is None:
        return
        
    print("\n📈 预测分析")
    print("=" * 50)
    
    # 基础统计
    print(f"预测值统计:")
    print(pred.describe())
    
    # 计算IC (信息系数)
    try:
        from qlib.contrib.evaluate import risk_analysis
        
        # 这里需要真实的标签数据来计算IC
        print("\n注意: 完整的预测分析需要标签数据来计算IC和其他指标")
        print("预测值分布:")
        print(f"最小值: {pred.min():.4f}")
        print(f"最大值: {pred.max():.4f}")
        print(f"均值: {pred.mean():.4f}")
        print(f"标准差: {pred.std():.4f}")
        
    except Exception as e:
        print(f"分析出错: {e}")

def run_backtest():
    """运行简单回测"""
    print("\n💰 回测分析")
    print("=" * 50)
    
    print("运行线性模型回测示例...")
    
    # 这里展示如何配置回测
    backtest_config = {
        'strategy': {
            'class': 'TopkDropoutStrategy',
            'module_path': 'qlib.contrib.strategy.signal_strategy',
            'kwargs': {
                'signal': '<PRED>',  # 使用模型预测作为信号
                'topk': 50,          # 选择前50只股票
                'n_drop': 5          # 每次调仓丢弃5只股票
            }
        },
        'backtest': {
            'start_time': '2017-01-01',
            'end_time': '2020-08-01',
            'account': *********,    # 初始资金1亿
            'benchmark': 'SH000300', # 基准指数
            'exchange_kwargs': {
                'limit_threshold': 0.095,  # 涨跌停限制
                'deal_price': 'close',     # 成交价格
                'open_cost': 0.0005,       # 开仓手续费
                'close_cost': 0.0015,      # 平仓手续费
                'min_cost': 5,             # 最小手续费
            }
        }
    }
    
    print("回测配置:")
    print(f"- 策略: TopkDropoutStrategy (选择前50只股票)")
    print(f"- 回测期间: 2017-01-01 到 2020-08-01")
    print(f"- 初始资金: 1亿元")
    print(f"- 基准指数: 沪深300 (SH000300)")
    print(f"- 手续费: 开仓0.05%, 平仓0.15%")
    
    print("\n注意: 完整回测需要运行完整的工作流配置文件")

def main():
    """主函数"""
    print("🎯 Qlib 完整功能演示")
    print("=" * 80)
    
    # 1. 初始化
    init_qlib()
    
    # 2. 数据探索
    sample_stocks = explore_data()
    
    # 3. 特征工程
    features = feature_engineering()
    
    # 4. 模型训练
    model, dataset, pred = create_simple_model()
    
    # 5. 预测分析
    analyze_predictions(pred)
    
    # 6. 回测说明
    run_backtest()
    
    print("\n🎉 演示完成!")
    print("=" * 80)
    print("\n下一步建议:")
    print("1. 运行完整的工作流配置文件:")
    print("   qrun examples/benchmarks/Linear/workflow_config_linear_Alpha158_csi500.yaml")
    print("\n2. 探索更多模型:")
    print("   - 安装额外依赖: pip install torch catboost")
    print("   - 尝试深度学习模型和集成模型")
    print("\n3. 自定义开发:")
    print("   - 开发自定义特征")
    print("   - 实现自定义策略")
    print("   - 集成到生产环境")

if __name__ == "__main__":
    main() 