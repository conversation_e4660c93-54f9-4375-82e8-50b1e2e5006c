#!/usr/bin/env python3
"""
MySQL任务管理器测试脚本
"""
import sys
import time
from qlib.workflow.task.mysql_manager import MySQLTaskManager

def test_mysql_task_manager():
    """测试MySQL任务管理器功能"""
    
    print("🚀 MySQL任务管理器测试")
    print("=" * 50)
    
    # 初始化任务管理器
    mysql_uri = "mysql+pymysql://root:root@localhost:3306/qlib_tasks"
    
    try:
        task_manager = MySQLTaskManager(mysql_uri)
        print("✅ MySQL任务管理器初始化成功")
    except Exception as e:
        print(f"❌ MySQL任务管理器初始化失败: {e}")
        return False
    
    # 测试1: 创建任务
    print("\n📋 测试1: 创建任务")
    try:
        task_id1 = task_manager.create_task(
            name="线性模型训练",
            task_type="model_training",
            config={
                "model": "LinearModel",
                "dataset": "Alpha158",
                "train_period": "2008-2014",
                "test_period": "2017-2020"
            }
        )
        print(f"✅ 任务1创建成功: {task_id1}")
        
        task_id2 = task_manager.create_task(
            name="回测分析",
            task_type="backtest",
            config={
                "strategy": "TopkDropout",
                "topk": 50
            },
            depends_on=[task_id1]  # 依赖任务1
        )
        print(f"✅ 任务2创建成功: {task_id2}")
        
    except Exception as e:
        print(f"❌ 创建任务失败: {e}")
        return False
    
    # 测试2: 查询任务
    print("\n🔍 测试2: 查询任务")
    try:
        task1 = task_manager.get_task(task_id1)
        print(f"✅ 任务1信息: {task1['name']} ({task1['status']})")
        
        all_tasks = task_manager.list_tasks()
        print(f"✅ 总任务数: {len(all_tasks)}")
        
    except Exception as e:
        print(f"❌ 查询任务失败: {e}")
        return False
    
    # 测试3: 更新任务状态
    print("\n🔄 测试3: 更新任务状态")
    try:
        # 开始任务1
        task_manager.update_task_status(task_id1, "RUNNING")
        print("✅ 任务1状态更新为RUNNING")
        
        # 模拟任务执行
        time.sleep(1)
        
        # 完成任务1
        task_manager.update_task_status(task_id1, "COMPLETED")
        print("✅ 任务1状态更新为COMPLETED")
        
        # 开始任务2
        task_manager.update_task_status(task_id2, "RUNNING")
        print("✅ 任务2状态更新为RUNNING")
        
    except Exception as e:
        print(f"❌ 更新任务状态失败: {e}")
        return False
    
    # 测试4: 添加任务结果
    print("\n📊 测试4: 添加任务结果")
    try:
        # 为任务1添加结果
        task_manager.add_task_result(
            task_id1,
            "metrics",
            {
                "ic": 0.025,
                "icir": 0.18,
                "annual_return": 0.035,
                "max_drawdown": 0.12
            }
        )
        print("✅ 任务1指标结果添加成功")
        
        task_manager.add_task_result(
            task_id1,
            "params",
            {
                "model": "LinearModel",
                "alpha": 0.001,
                "max_iter": 1000
            }
        )
        print("✅ 任务1参数结果添加成功")
        
    except Exception as e:
        print(f"❌ 添加任务结果失败: {e}")
        return False
    
    # 测试5: 查询任务结果
    print("\n📈 测试5: 查询任务结果")
    try:
        results = task_manager.get_task_results(task_id1)
        print(f"✅ 任务1结果数量: {len(results)}")
        
        metrics = task_manager.get_task_results(task_id1, "metrics")
        if metrics:
            print(f"✅ 任务1指标: {metrics[0]['result_data']}")
        
    except Exception as e:
        print(f"❌ 查询任务结果失败: {e}")
        return False
    
    # 测试6: 检查依赖
    print("\n🔗 测试6: 检查任务依赖")
    try:
        dependencies = task_manager.get_task_dependencies(task_id2)
        print(f"✅ 任务2依赖: {dependencies}")
        
        deps_completed = task_manager.check_dependencies_completed(task_id2)
        print(f"✅ 任务2依赖是否完成: {deps_completed}")
        
        ready_tasks = task_manager.get_ready_tasks()
        print(f"✅ 准备运行的任务数: {len(ready_tasks)}")
        
    except Exception as e:
        print(f"❌ 检查任务依赖失败: {e}")
        return False
    
    # 测试7: 获取统计信息
    print("\n📊 测试7: 获取统计信息")
    try:
        stats = task_manager.get_task_statistics()
        print("✅ 任务统计信息:")
        print(f"   总任务数: {stats.get('total_tasks', 0)}")
        print(f"   总结果数: {stats.get('total_results', 0)}")
        print(f"   状态分布: {stats.get('status_distribution', {})}")
        print(f"   类型分布: {stats.get('type_distribution', {})}")
        
    except Exception as e:
        print(f"❌ 获取统计信息失败: {e}")
        return False
    
    print("\n🎉 所有测试通过！MySQL任务管理器功能正常")
    return True

if __name__ == "__main__":
    success = test_mysql_task_manager()
    sys.exit(0 if success else 1) 