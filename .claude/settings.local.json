{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(conda activate:*)", "<PERSON><PERSON>(python:*)", "Bash(source ~/.bashrc)", "Bash(export:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(timeout:*)", "Bash(find:*)", "Bash(grep:*)", "<PERSON><PERSON>(source:*)", "<PERSON><PERSON>(env)", "<PERSON><PERSON>(claude mcp:*)", "WebFetch(domain:qlib.readthedocs.io)", "<PERSON><PERSON>(conda run:*)"], "deny": []}}