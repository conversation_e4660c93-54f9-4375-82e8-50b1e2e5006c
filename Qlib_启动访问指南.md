# Qlib 项目启动和访问指南

## 🚀 启动方式概览

Qlib提供了多种启动和使用方式，适合不同的使用场景：

1. **命令行方式** - 快速运行预配置的工作流
2. **Python脚本方式** - 灵活的自定义开发
3. **Jupyter Notebook** - 交互式研究和分析
4. **Web界面** - 可视化的实验管理（需要额外配置）

## 📋 启动前准备

### 1. 激活环境
```bash
# 激活qlib-env虚拟环境
conda activate qlib-env

# 验证环境
python -c "import qlib; print('Qlib版本:', qlib.__version__)"
```

### 2. 进入项目目录
```bash
cd /Users/<USER>/PycharmProjects/qlib
```

## 🎯 方式一：命令行启动（推荐）

### 快速启动示例
```bash
# 运行线性模型示例（最稳定）
qrun examples/benchmarks/Linear/workflow_config_linear_Alpha158_csi500.yaml

# 运行MLP神经网络示例
qrun examples/benchmarks/MLP/workflow_config_mlp_Alpha158_csi500.yaml
```

### 可用的配置文件
```bash
# 查看所有可用的配置文件
find examples/benchmarks -name "*.yaml" | sort

# 推荐的稳定配置文件：
examples/benchmarks/Linear/workflow_config_linear_Alpha158_csi500.yaml
examples/benchmarks/MLP/workflow_config_mlp_Alpha158_csi500.yaml
```

### 命令行参数
```bash
# 指定实验名称
qrun examples/benchmarks/Linear/workflow_config_linear_Alpha158_csi500.yaml --experiment_name "我的第一个实验"

# 查看帮助
qrun --help
```

## 🐍 方式二：Python脚本启动

### 使用我们创建的完整示例
```bash
# 运行完整功能演示
python qlib_完整示例.py
```

### 创建自定义脚本
```python
#!/usr/bin/env python3
import qlib
from qlib.constant import REG_CN
from qlib.utils import init_instance_by_config

# 1. 初始化Qlib
qlib.init(provider_uri="~/.qlib/qlib_data/cn_data", region=REG_CN)

# 2. 配置模型和数据
config = {
    'model': {
        'class': 'LinearModel',
        'module_path': 'qlib.contrib.model.linear'
    },
    'dataset': {
        'class': 'DatasetH',
        'module_path': 'qlib.data.dataset',
        'kwargs': {
            'handler': {
                'class': 'Alpha158',
                'module_path': 'qlib.contrib.data.handler',
                'kwargs': {
                    'start_time': '2008-01-01',
                    'end_time': '2020-08-01',
                    'fit_start_time': '2008-01-01',
                    'fit_end_time': '2014-12-31',
                    'instruments': 'csi300'
                }
            },
            'segments': {
                'train': ['2008-01-01', '2014-12-31'],
                'valid': ['2015-01-01', '2016-12-31'], 
                'test': ['2017-01-01', '2020-08-01']
            }
        }
    }
}

# 3. 创建和训练模型
model = init_instance_by_config(config['model'])
dataset = init_instance_by_config(config['dataset'])

print("开始训练...")
model.fit(dataset)

print("开始预测...")
pred = model.predict(dataset, segment='test')
print("预测完成！")
print(pred.head())
```

## 📊 方式三：Jupyter Notebook启动

### 安装Jupyter
```bash
# 在qlib-env环境中安装
pip install jupyter notebook ipykernel

# 添加内核
python -m ipykernel install --user --name qlib-env --display-name "Qlib Environment"
```

### 启动Jupyter
```bash
# 启动Jupyter Notebook
jupyter notebook

# 或启动JupyterLab
pip install jupyterlab
jupyter lab
```

### Notebook示例代码
```python
# 第一个Cell：导入和初始化
import qlib
from qlib.constant import REG_CN
from qlib.data import D
import pandas as pd
import matplotlib.pyplot as plt

qlib.init(provider_uri="~/.qlib/qlib_data/cn_data", region=REG_CN)
print("Qlib初始化完成！")

# 第二个Cell：数据探索
instruments = D.list_instruments(D.instruments("csi300"), as_list=True)[:10]
price_data = D.features(instruments, ['$close', '$volume'], 
                       start_time='2020-01-01', end_time='2020-12-31')
print(f"数据形状: {price_data.shape}")
price_data.head()

# 第三个Cell：数据可视化
price_data.unstack(level=0)['$close'].plot(figsize=(12, 6), title='股价走势')
plt.show()
```

## 🌐 方式四：Web界面启动（可选）

### 安装Web依赖
```bash
pip install flask plotly dash
```

### 启动实验管理界面
```python
# 创建web_interface.py
from qlib.workflow import R
import mlflow

# 启动MLflow UI
# 在终端运行：mlflow ui --host 0.0.0.0 --port 5000
```

```bash
# 启动Web界面
mlflow ui --host 0.0.0.0 --port 5000
```

然后在浏览器中访问：http://localhost:5000

## 📈 实时监控和结果查看

### 1. 实验结果查看
```python
from qlib.workflow import R

# 查看所有实验
experiments = R.list_experiments()
print("所有实验:", experiments)

# 查看最新实验的结果
latest_exp = experiments[0]
records = R.list_recorders(experiment_id=latest_exp)
print("实验记录:", records)

# 获取预测结果
pred = R.load_object(recorder_id=records[0], name="pred.pkl")
print("预测结果:", pred.head())
```

### 2. 性能指标查看
```python
# 获取回测结果
portfolio_analysis = R.load_object(recorder_id=records[0], name="port_analysis_1day.pkl")
print("投资组合分析:", portfolio_analysis)

# 获取指标分析
indicator_analysis = R.load_object(recorder_id=records[0], name="indicator_analysis_1day.pkl")
print("指标分析:", indicator_analysis)
```

## 🔧 常用启动命令速查

### 数据相关
```bash
# 检查数据状态
python -c "
import qlib
from qlib.constant import REG_CN
from qlib.data import D
qlib.init(provider_uri='~/.qlib/qlib_data/cn_data', region=REG_CN)
print('股票总数:', len(D.list_instruments(D.instruments(), as_list=True)))
print('CSI300股票数:', len(D.list_instruments(D.instruments('csi300'), as_list=True)))
"

# 更新数据（如果需要）
python -c "
from qlib.tests.data import GetData
from qlib.constant import REG_CN
GetData().qlib_data(target_dir='~/.qlib/qlib_data/cn_data', region=REG_CN, exists_skip=True)
"
```

### 模型训练
```bash
# 快速训练线性模型
qrun examples/benchmarks/Linear/workflow_config_linear_Alpha158_csi500.yaml

# 训练神经网络模型
qrun examples/benchmarks/MLP/workflow_config_mlp_Alpha158_csi500.yaml

# 运行自定义脚本
python qlib_完整示例.py
```

### 结果分析
```bash
# 查看实验结果
python -c "
from qlib.workflow import R
experiments = R.list_experiments()
print('实验数量:', len(experiments))
if experiments:
    records = R.list_recorders(experiment_id=experiments[0])
    print('最新实验记录数:', len(records))
"
```

## 🚨 故障排除

### 常见问题和解决方案

#### 1. 环境问题
```bash
# 检查环境
conda info --envs
conda activate qlib-env
python -c "import qlib; print('OK')"
```

#### 2. 数据问题
```bash
# 检查数据目录
ls -la ~/.qlib/qlib_data/cn_data/

# 重新下载数据
python -c "
from qlib.tests.data import GetData
from qlib.constant import REG_CN
GetData().qlib_data(target_dir='~/.qlib/qlib_data/cn_data', region=REG_CN, exists_skip=False)
"
```

#### 3. 模型问题
```bash
# 只使用稳定的模型
qrun examples/benchmarks/Linear/workflow_config_linear_Alpha158_csi500.yaml

# 检查可用模型
python -c "
import qlib
qlib.init(provider_uri='~/.qlib/qlib_data/cn_data', region='cn')
from qlib.contrib.model import linear
print('LinearModel可用')
"
```

## 📚 学习路径建议

### 初学者路径
1. **开始**: 运行 `qrun examples/benchmarks/Linear/workflow_config_linear_Alpha158_csi500.yaml`
2. **理解**: 阅读配置文件，理解参数含义
3. **修改**: 尝试修改配置参数
4. **自定义**: 运行 `python qlib_完整示例.py`

### 进阶路径
1. **特征工程**: 学习Alpha158因子
2. **模型开发**: 尝试不同的机器学习模型
3. **策略开发**: 开发自定义交易策略
4. **生产部署**: 集成到实际交易系统

### 高级路径
1. **强化学习**: 探索RL在量化投资中的应用
2. **高频交易**: 处理高频数据和策略
3. **风险管理**: 开发风险控制模型
4. **系统架构**: 构建大规模量化交易系统

## 🎯 快速启动检查清单

- [ ] 激活conda环境：`conda activate qlib-env`
- [ ] 进入项目目录：`cd /Users/<USER>/PycharmProjects/qlib`
- [ ] 验证安装：`python -c "import qlib; print('OK')"`
- [ ] 运行示例：`qrun examples/benchmarks/Linear/workflow_config_linear_Alpha158_csi500.yaml`
- [ ] 查看结果：检查输出的IC、收益率等指标
- [ ] 探索数据：`python qlib_完整示例.py`

## 🔗 相关资源

- **项目文档**: `qlib_项目分析报告.md`
- **部署指南**: `Qlib_部署指南.md`
- **完整示例**: `qlib_完整示例.py`
- **项目总结**: `Qlib_项目总结.md`
- **官方文档**: https://qlib.readthedocs.io/
- **GitHub仓库**: https://github.com/microsoft/qlib

---

**🎉 现在您已经掌握了Qlib的所有启动方式！选择最适合您需求的方式开始量化投资之旅吧！** 