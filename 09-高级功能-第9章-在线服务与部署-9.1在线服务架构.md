# 9.1 在线服务架构

## 学习目标

通过本节学习，您将能够：
- 理解在线模式与离线模式的区别
- 掌握Qlib在线服务架构设计
- 学会数据服务的部署方法
- 掌握模型服务化的实现
- 理解实时数据处理的技术要点

## 9.1.1 在线模式vs离线模式

### 模式对比分析

在量化投资中，在线模式和离线模式各有其适用场景和优势。

```python
class TradingModeComparison:
    """交易模式对比分析"""
    
    def __init__(self):
        self.offline_advantages = [
            "历史数据回测",
            "模型训练和优化",
            "策略验证",
            "风险分析"
        ]
        
        self.online_advantages = [
            "实时交易执行",
            "市场响应",
            "动态调整",
            "实时监控"
        ]
    
    def compare_modes(self):
        """对比在线和离线模式"""
        comparison = {
            "离线模式": {
                "数据来源": "历史数据",
                "处理方式": "批量处理",
                "响应时间": "分钟级",
                "适用场景": "策略开发、回测",
                "优势": self.offline_advantages,
                "劣势": ["无法实时响应", "数据滞后"]
            },
            "在线模式": {
                "数据来源": "实时数据流",
                "处理方式": "流式处理",
                "响应时间": "毫秒级",
                "适用场景": "实盘交易、监控",
                "优势": self.online_advantages,
                "劣势": ["系统复杂", "成本较高"]
            }
        }
        
        return comparison
```

### 架构设计原则

```python
class OnlineArchitectureDesign:
    """在线架构设计"""
    
    def __init__(self):
        self.design_principles = [
            "高可用性",
            "低延迟",
            "可扩展性",
            "容错性",
            "安全性"
        ]
    
    def design_online_system(self):
        """设计在线系统架构"""
        architecture = {
            "数据层": {
                "实时数据源": ["行情数据", "新闻数据", "基本面数据"],
                "数据缓存": ["Redis", "内存数据库"],
                "数据同步": ["消息队列", "流处理"]
            },
            "计算层": {
                "模型服务": ["模型推理", "特征计算"],
                "策略引擎": ["信号生成", "风险控制"],
                "决策系统": ["订单管理", "执行优化"]
            },
            "服务层": {
                "API网关": ["请求路由", "负载均衡"],
                "监控系统": ["性能监控", "告警系统"],
                "日志系统": ["操作日志", "错误日志"]
            }
        }
        
        return architecture
```

## 9.1.2 数据服务部署

### 实时数据接入

```python
import asyncio
import websockets
import json
from qlib.contrib.online import OnlineDataHandler

class RealTimeDataService:
    """实时数据服务"""
    
    def __init__(self, data_sources):
        self.data_sources = data_sources
        self.data_cache = {}
        self.subscribers = []
    
    async def start_data_service(self):
        """启动数据服务"""
        # 启动多个数据源连接
        tasks = []
        for source in self.data_sources:
            task = asyncio.create_task(self.connect_data_source(source))
            tasks.append(task)
        
        # 等待所有连接建立
        await asyncio.gather(*tasks)
    
    async def connect_data_source(self, source):
        """连接数据源"""
        try:
            async with websockets.connect(source['url']) as websocket:
                # 订阅数据
                await websocket.send(json.dumps(source['subscription']))
                
                # 接收数据
                async for message in websocket:
                    data = json.loads(message)
                    await self.process_data(data, source['type'])
        
        except Exception as e:
            print(f"数据源连接失败: {e}")
    
    async def process_data(self, data, data_type):
        """处理接收到的数据"""
        # 数据验证
        if self.validate_data(data):
            # 数据转换
            processed_data = self.transform_data(data, data_type)
            
            # 更新缓存
            self.update_cache(processed_data)
            
            # 通知订阅者
            await self.notify_subscribers(processed_data)
    
    def validate_data(self, data):
        """验证数据有效性"""
        required_fields = ['timestamp', 'symbol', 'price']
        
        for field in required_fields:
            if field not in data:
                return False
        
        return True
    
    def transform_data(self, data, data_type):
        """转换数据格式"""
        transformed = {
            'timestamp': data['timestamp'],
            'symbol': data['symbol'],
            'type': data_type,
            'data': data
        }
        
        return transformed
    
    def update_cache(self, data):
        """更新数据缓存"""
        key = f"{data['symbol']}_{data['type']}"
        self.data_cache[key] = data
    
    async def notify_subscribers(self, data):
        """通知订阅者"""
        for subscriber in self.subscribers:
            try:
                await subscriber(data)
            except Exception as e:
                print(f"通知订阅者失败: {e}")
    
    def subscribe(self, callback):
        """订阅数据更新"""
        self.subscribers.append(callback)
```

### 数据缓存策略

```python
import redis
import pickle
from datetime import datetime, timedelta

class DataCacheManager:
    """数据缓存管理器"""
    
    def __init__(self, redis_config):
        self.redis_client = redis.Redis(**redis_config)
        self.cache_config = {
            'price_data': {'ttl': 300, 'max_size': 10000},
            'volume_data': {'ttl': 600, 'max_size': 5000},
            'news_data': {'ttl': 3600, 'max_size': 1000}
        }
    
    def cache_data(self, key, data, data_type):
        """缓存数据"""
        config = self.cache_config.get(data_type, {})
        ttl = config.get('ttl', 300)
        
        # 序列化数据
        serialized_data = pickle.dumps(data)
        
        # 存储到Redis
        self.redis_client.setex(key, ttl, serialized_data)
        
        # 更新缓存统计
        self.update_cache_stats(data_type)
    
    def get_cached_data(self, key):
        """获取缓存数据"""
        try:
            data = self.redis_client.get(key)
            if data:
                return pickle.loads(data)
        except Exception as e:
            print(f"获取缓存数据失败: {e}")
        
        return None
    
    def update_cache_stats(self, data_type):
        """更新缓存统计"""
        stats_key = f"cache_stats:{data_type}"
        current_count = self.redis_client.llen(stats_key)
        
        # 检查缓存大小限制
        config = self.cache_config.get(data_type, {})
        max_size = config.get('max_size', 1000)
        
        if current_count >= max_size:
            # 清理旧数据
            self.redis_client.lpop(stats_key)
        
        # 添加新记录
        self.redis_client.rpush(stats_key, datetime.now().isoformat())
    
    def get_cache_stats(self):
        """获取缓存统计信息"""
        stats = {}
        
        for data_type in self.cache_config:
            stats_key = f"cache_stats:{data_type}"
            count = self.redis_client.llen(stats_key)
            stats[data_type] = count
        
        return stats
```

## 9.1.3 模型服务化

### 模型服务架构

```python
from flask import Flask, request, jsonify
import torch
import numpy as np
from qlib.contrib.online import OnlineModelService

class ModelServiceAPI:
    """模型服务API"""
    
    def __init__(self, model_path, model_config):
        self.app = Flask(__name__)
        self.model = self.load_model(model_path)
        self.model_config = model_config
        self.setup_routes()
    
    def load_model(self, model_path):
        """加载模型"""
        try:
            # 加载PyTorch模型
            model = torch.load(model_path, map_location='cpu')
            model.eval()
            return model
        except Exception as e:
            print(f"模型加载失败: {e}")
            return None
    
    def setup_routes(self):
        """设置API路由"""
        
        @self.app.route('/predict', methods=['POST'])
        def predict():
            """预测接口"""
            try:
                data = request.get_json()
                features = np.array(data['features'])
                
                # 数据预处理
                processed_features = self.preprocess_features(features)
                
                # 模型预测
                with torch.no_grad():
                    predictions = self.model(torch.FloatTensor(processed_features))
                
                # 后处理
                result = self.postprocess_predictions(predictions)
                
                return jsonify({
                    'status': 'success',
                    'predictions': result.tolist(),
                    'timestamp': datetime.now().isoformat()
                })
            
            except Exception as e:
                return jsonify({
                    'status': 'error',
                    'message': str(e)
                }), 500
        
        @self.app.route('/health', methods=['GET'])
        def health_check():
            """健康检查"""
            return jsonify({
                'status': 'healthy',
                'model_loaded': self.model is not None,
                'timestamp': datetime.now().isoformat()
            })
    
    def preprocess_features(self, features):
        """特征预处理"""
        # 标准化
        features = (features - self.model_config['mean']) / self.model_config['std']
        
        # 维度调整
        if len(features.shape) == 1:
            features = features.reshape(1, -1)
        
        return features
    
    def postprocess_predictions(self, predictions):
        """预测结果后处理"""
        # 转换为numpy数组
        predictions = predictions.numpy()
        
        # 应用激活函数（如果需要）
        if self.model_config.get('activation') == 'sigmoid':
            predictions = 1 / (1 + np.exp(-predictions))
        
        return predictions
    
    def run(self, host='0.0.0.0', port=5000):
        """启动服务"""
        self.app.run(host=host, port=port, debug=False)
```

### 模型版本管理

```python
import os
import shutil
from datetime import datetime

class ModelVersionManager:
    """模型版本管理器"""
    
    def __init__(self, model_repository):
        self.model_repository = model_repository
        self.version_history = {}
    
    def deploy_model(self, model_path, version, metadata=None):
        """部署新模型版本"""
        try:
            # 创建版本目录
            version_dir = os.path.join(self.model_repository, version)
            os.makedirs(version_dir, exist_ok=True)
            
            # 复制模型文件
            model_filename = os.path.basename(model_path)
            new_model_path = os.path.join(version_dir, model_filename)
            shutil.copy2(model_path, new_model_path)
            
            # 保存元数据
            if metadata is None:
                metadata = {}
            
            metadata['deployment_time'] = datetime.now().isoformat()
            metadata['model_path'] = new_model_path
            
            self.save_metadata(version, metadata)
            
            # 更新版本历史
            self.version_history[version] = metadata
            
            print(f"模型版本 {version} 部署成功")
            return True
        
        except Exception as e:
            print(f"模型部署失败: {e}")
            return False
    
    def rollback_model(self, target_version):
        """回滚到指定版本"""
        if target_version not in self.version_history:
            print(f"版本 {target_version} 不存在")
            return False
        
        try:
            # 更新当前版本链接
            current_link = os.path.join(self.model_repository, 'current')
            target_path = os.path.join(self.model_repository, target_version)
            
            if os.path.exists(current_link):
                os.remove(current_link)
            
            os.symlink(target_path, current_link)
            
            print(f"成功回滚到版本 {target_version}")
            return True
        
        except Exception as e:
            print(f"模型回滚失败: {e}")
            return False
    
    def list_versions(self):
        """列出所有版本"""
        versions = []
        
        for version, metadata in self.version_history.items():
            version_info = {
                'version': version,
                'deployment_time': metadata.get('deployment_time'),
                'model_path': metadata.get('model_path'),
                'performance': metadata.get('performance', {})
            }
            versions.append(version_info)
        
        return sorted(versions, key=lambda x: x['deployment_time'], reverse=True)
    
    def save_metadata(self, version, metadata):
        """保存元数据"""
        metadata_path = os.path.join(self.model_repository, f"{version}_metadata.json")
        
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
```

## 9.1.4 实时数据处理

### 流式数据处理

```python
import asyncio
from collections import deque
from qlib.contrib.online import StreamProcessor

class RealTimeDataProcessor:
    """实时数据处理器"""
    
    def __init__(self, window_size=100):
        self.window_size = window_size
        self.data_buffer = deque(maxlen=window_size)
        self.processors = []
    
    def add_processor(self, processor):
        """添加数据处理器"""
        self.processors.append(processor)
    
    async def process_stream(self, data_stream):
        """处理数据流"""
        async for data in data_stream:
            # 添加到缓冲区
            self.data_buffer.append(data)
            
            # 执行处理
            processed_data = await self.execute_processors(data)
            
            # 输出结果
            yield processed_data
    
    async def execute_processors(self, data):
        """执行所有处理器"""
        processed_data = data
        
        for processor in self.processors:
            try:
                processed_data = await processor.process(processed_data, self.data_buffer)
            except Exception as e:
                print(f"处理器执行失败: {e}")
        
        return processed_data

class TechnicalIndicatorProcessor:
    """技术指标处理器"""
    
    def __init__(self):
        self.indicators = {}
    
    async def process(self, data, buffer):
        """处理技术指标"""
        if len(buffer) < 20:
            return data
        
        # 计算移动平均线
        prices = [d['price'] for d in buffer]
        data['ma_5'] = self.calculate_ma(prices, 5)
        data['ma_20'] = self.calculate_ma(prices, 20)
        
        # 计算RSI
        data['rsi'] = self.calculate_rsi(prices)
        
        # 计算布林带
        data['bollinger'] = self.calculate_bollinger(prices)
        
        return data
    
    def calculate_ma(self, prices, period):
        """计算移动平均线"""
        if len(prices) < period:
            return None
        
        return sum(prices[-period:]) / period
    
    def calculate_rsi(self, prices, period=14):
        """计算RSI"""
        if len(prices) < period + 1:
            return None
        
        gains = []
        losses = []
        
        for i in range(1, len(prices)):
            change = prices[i] - prices[i-1]
            if change > 0:
                gains.append(change)
                losses.append(0)
            else:
                gains.append(0)
                losses.append(-change)
        
        avg_gain = sum(gains[-period:]) / period
        avg_loss = sum(losses[-period:]) / period
        
        if avg_loss == 0:
            return 100
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    def calculate_bollinger(self, prices, period=20, std_dev=2):
        """计算布林带"""
        if len(prices) < period:
            return None
        
        ma = self.calculate_ma(prices, period)
        std = np.std(prices[-period:])
        
        upper_band = ma + (std_dev * std)
        lower_band = ma - (std_dev * std)
        
        return {
            'upper': upper_band,
            'middle': ma,
            'lower': lower_band
        }
```

### 事件驱动处理

```python
from abc import ABC, abstractmethod
import asyncio

class EventHandler(ABC):
    """事件处理器基类"""
    
    @abstractmethod
    async def handle_event(self, event):
        """处理事件"""
        pass

class MarketEventHandler(EventHandler):
    """市场事件处理器"""
    
    def __init__(self):
        self.event_handlers = {
            'price_alert': self.handle_price_alert,
            'volume_spike': self.handle_volume_spike,
            'news_event': self.handle_news_event
        }
    
    async def handle_event(self, event):
        """处理市场事件"""
        event_type = event.get('type')
        handler = self.event_handlers.get(event_type)
        
        if handler:
            await handler(event)
        else:
            print(f"未知事件类型: {event_type}")
    
    async def handle_price_alert(self, event):
        """处理价格警报"""
        symbol = event['symbol']
        current_price = event['price']
        threshold = event['threshold']
        
        if current_price > threshold:
            # 触发买入信号
            await self.trigger_buy_signal(symbol, current_price)
        elif current_price < threshold:
            # 触发卖出信号
            await self.trigger_sell_signal(symbol, current_price)
    
    async def handle_volume_spike(self, event):
        """处理成交量异常"""
        symbol = event['symbol']
        volume = event['volume']
        avg_volume = event['avg_volume']
        
        if volume > avg_volume * 2:
            # 成交量异常，可能需要调整策略
            await self.adjust_strategy_for_volume_spike(symbol, volume)
    
    async def handle_news_event(self, event):
        """处理新闻事件"""
        news_content = event['content']
        sentiment = self.analyze_sentiment(news_content)
        
        if sentiment > 0.7:
            # 正面新闻，可能买入
            await self.handle_positive_news(event)
        elif sentiment < 0.3:
            # 负面新闻，可能卖出
            await self.handle_negative_news(event)
    
    async def trigger_buy_signal(self, symbol, price):
        """触发买入信号"""
        print(f"买入信号: {symbol} at {price}")
        # 这里可以添加实际的交易逻辑
    
    async def trigger_sell_signal(self, symbol, price):
        """触发卖出信号"""
        print(f"卖出信号: {symbol} at {price}")
        # 这里可以添加实际的交易逻辑
    
    def analyze_sentiment(self, text):
        """分析文本情感"""
        # 简单的情感分析实现
        positive_words = ['涨', '利好', '增长', '盈利']
        negative_words = ['跌', '利空', '下降', '亏损']
        
        positive_count = sum(1 for word in positive_words if word in text)
        negative_count = sum(1 for word in negative_words if word in text)
        
        total_words = len(text.split())
        if total_words == 0:
            return 0.5
        
        sentiment = (positive_count - negative_count) / total_words + 0.5
        return max(0, min(1, sentiment))
```

## 9.1.5 实践案例

### 案例1：实时交易系统架构

```python
# 实时交易系统架构示例
class RealTimeTradingSystem:
    """实时交易系统"""
    
    def __init__(self):
        self.data_service = RealTimeDataService([
            {'url': 'ws://market-data.example.com', 'type': 'price'},
            {'url': 'ws://news-data.example.com', 'type': 'news'}
        ])
        
        self.model_service = ModelServiceAPI('models/latest_model.pth', {
            'mean': [0.0] * 158,
            'std': [1.0] * 158
        })
        
        self.event_handler = MarketEventHandler()
        self.data_processor = RealTimeDataProcessor()
    
    async def start_system(self):
        """启动系统"""
        # 启动数据服务
        await self.data_service.start_data_service()
        
        # 启动模型服务
        self.model_service.run(port=5000)
        
        # 启动数据处理
        await self.start_data_processing()
    
    async def start_data_processing(self):
        """启动数据处理"""
        # 添加技术指标处理器
        self.data_processor.add_processor(TechnicalIndicatorProcessor())
        
        # 订阅数据更新
        self.data_service.subscribe(self.handle_data_update)
    
    async def handle_data_update(self, data):
        """处理数据更新"""
        # 处理数据
        processed_data = await self.data_processor.execute_processors(data)
        
        # 生成预测
        prediction = await self.generate_prediction(processed_data)
        
        # 生成交易信号
        signal = await self.generate_trading_signal(processed_data, prediction)
        
        # 执行交易
        if signal:
            await self.execute_trade(signal)
    
    async def generate_prediction(self, data):
        """生成预测"""
        # 准备特征
        features = self.extract_features(data)
        
        # 调用模型服务
        response = await self.call_model_service(features)
        
        return response['predictions']
    
    async def generate_trading_signal(self, data, prediction):
        """生成交易信号"""
        # 基于预测和技术指标生成信号
        signal = {
            'symbol': data['symbol'],
            'action': 'buy' if prediction > 0.5 else 'sell',
            'confidence': abs(prediction - 0.5) * 2,
            'timestamp': data['timestamp']
        }
        
        return signal if signal['confidence'] > 0.7 else None
    
    async def execute_trade(self, signal):
        """执行交易"""
        print(f"执行交易: {signal}")
        # 这里添加实际的交易执行逻辑
```

### 案例2：高可用性架构

```python
# 高可用性架构示例
class HighAvailabilitySystem:
    """高可用性系统"""
    
    def __init__(self):
        self.primary_service = None
        self.backup_service = None
        self.load_balancer = None
        self.health_monitor = None
    
    def setup_ha_architecture(self):
        """设置高可用性架构"""
        # 主服务
        self.primary_service = ModelServiceAPI('models/primary.pth', {})
        
        # 备份服务
        self.backup_service = ModelServiceAPI('models/backup.pth', {})
        
        # 负载均衡器
        self.load_balancer = LoadBalancer([
            self.primary_service,
            self.backup_service
        ])
        
        # 健康监控
        self.health_monitor = HealthMonitor([
            self.primary_service,
            self.backup_service
        ])
    
    async def start_ha_system(self):
        """启动高可用性系统"""
        # 启动健康监控
        await self.health_monitor.start_monitoring()
        
        # 启动负载均衡器
        await self.load_balancer.start()
        
        print("高可用性系统启动完成")

class LoadBalancer:
    """负载均衡器"""
    
    def __init__(self, services):
        self.services = services
        self.current_index = 0
    
    async def get_service(self):
        """获取可用服务"""
        # 轮询选择服务
        service = self.services[self.current_index]
        self.current_index = (self.current_index + 1) % len(self.services)
        
        return service
    
    async def start(self):
        """启动负载均衡器"""
        print("负载均衡器启动")

class HealthMonitor:
    """健康监控器"""
    
    def __init__(self, services):
        self.services = services
        self.health_status = {}
    
    async def start_monitoring(self):
        """开始监控"""
        while True:
            for service in self.services:
                status = await self.check_service_health(service)
                self.health_status[service] = status
            
            await asyncio.sleep(30)  # 每30秒检查一次
    
    async def check_service_health(self, service):
        """检查服务健康状态"""
        try:
            # 发送健康检查请求
            response = await self.send_health_check(service)
            return response['status'] == 'healthy'
        except Exception as e:
            print(f"服务健康检查失败: {e}")
            return False
```

## 9.1.6 总结与展望

### 本节要点总结

1. **架构设计**：理解了在线服务架构的设计原则和组件
2. **数据服务**：掌握了实时数据接入和缓存策略
3. **模型服务化**：学会了模型服务API的开发和版本管理
4. **实时处理**：掌握了流式数据处理和事件驱动机制

### 实践建议

1. **系统设计**：根据实际需求设计合适的在线架构
2. **性能优化**：关注系统的延迟和吞吐量
3. **监控告警**：建立完善的监控和告警机制
4. **容错设计**：实现高可用性和故障恢复

### 进一步学习方向

1. **微服务架构**：学习微服务在量化系统中的应用
2. **容器化部署**：使用Docker和Kubernetes部署服务
3. **云原生架构**：探索云原生技术在量化系统中的应用
4. **边缘计算**：研究边缘计算在实时交易中的应用

---

*本节内容涵盖了在线服务架构的核心组件，通过数据服务、模型服务化和实时数据处理，为量化投资系统提供了完整的在线服务解决方案。* 