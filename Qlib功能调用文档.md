# Qlib功能调用文档

## 1. 概述

本文档详细介绍了如何在多用户量化投资平台中调用Qlib的各种功能，包括数据获取、模型训练、回测、策略执行等核心功能。

## 2. 初始化配置

### 2.1 基础初始化
```python
import qlib
from qlib.config import C
from qlib.data import D
from qlib.workflow import R
from qlib.contrib.evaluate import backtest_daily
from qlib.contrib.strategy import TopkDropoutStrategy

# 初始化Qlib
def init_qlib(user_id: int, data_path: str = None):
    """初始化Qlib环境"""
    if data_path is None:
        data_path = f"~/.qlib/qlib_data/cn_data"
    
    qlib.init(
        provider_uri=data_path,
        region="cn",
        auto_mount=False,
        # 用户特定配置
        user_id=user_id
    )
```

### 2.2 用户隔离配置
```python
class QlibUserIsolation:
    def __init__(self, user_id: int):
        self.user_id = user_id
        self.user_data_dir = f"/tmp/qlib_user_{user_id}"
        self.user_config_dir = f"/tmp/qlib_config_{user_id}"
    
    def setup_user_environment(self):
        """设置用户独立环境"""
        import os
        os.makedirs(self.user_data_dir, exist_ok=True)
        os.makedirs(self.user_config_dir, exist_ok=True)
        
        # 设置用户特定的配置
        config = {
            "provider_uri": self.user_data_dir,
            "mount_path": self.user_data_dir,
            "auto_mount": False,
            "user_id": self.user_id
        }
        
        return config
```

## 3. 数据获取功能

### 3.1 基础数据查询
```python
class QlibDataService:
    def __init__(self, user_id: int):
        self.user_id = user_id
        init_qlib(user_id)
    
    def get_instruments(self, market: str = "cn", start_time: str = None, end_time: str = None):
        """获取股票列表"""
        try:
            # 获取股票池
            instruments = D.instruments(market)
            stock_list = D.list_instruments(
                instruments=instruments,
                start_time=start_time,
                end_time=end_time,
                as_list=True
            )
            return stock_list
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return []
    
    def get_features(self, instruments: list, fields: list, start_time: str, end_time: str, freq: str = "1d"):
        """获取特征数据"""
        try:
            data = D.features(
                instruments=instruments,
                fields=fields,
                start_time=start_time,
                end_time=end_time,
                freq=freq
            )
            return data
        except Exception as e:
            logger.error(f"获取特征数据失败: {e}")
            return None
    
    def get_calendar(self, start_time: str, end_time: str, freq: str = "1d"):
        """获取交易日历"""
        try:
            calendar = D.calendar(
                start_time=start_time,
                end_time=end_time,
                freq=freq
            )
            return calendar
        except Exception as e:
            logger.error(f"获取交易日历失败: {e}")
            return None
```

### 3.2 高级数据查询
```python
    def get_multi_level_data(self, instruments: list, fields: list, start_time: str, end_time: str):
        """获取多层级数据"""
        try:
            # 获取基础价格数据
            price_fields = ["$open", "$high", "$low", "$close", "$volume"]
            price_data = self.get_features(instruments, price_fields, start_time, end_time)
            
            # 获取技术指标
            tech_fields = ["Ref($close, 1)", "Mean($close, 5)", "Std($close, 20)"]
            tech_data = self.get_features(instruments, tech_fields, start_time, end_time)
            
            # 合并数据
            combined_data = price_data.join(tech_data)
            return combined_data
        except Exception as e:
            logger.error(f"获取多层级数据失败: {e}")
            return None
    
    def get_alpha_factors(self, instruments: list, start_time: str, end_time: str):
        """获取Alpha因子数据"""
        try:
            # Alpha158因子
            alpha_fields = [
                "$close", "$volume", "$high", "$low", "$open",
                "Ref($close, 1)", "Ref($close, 2)", "Ref($close, 3)",
                "Mean($close, 5)", "Mean($close, 10)", "Mean($close, 20)",
                "Std($close, 5)", "Std($close, 10)", "Std($close, 20)",
                "Max($high, 5)", "Min($low, 5)",
                "Rank($close)", "Rank($volume)",
                "Corr($close, $volume, 5)", "Corr($close, $volume, 10)"
            ]
            
            alpha_data = self.get_features(instruments, alpha_fields, start_time, end_time)
            return alpha_data
        except Exception as e:
            logger.error(f"获取Alpha因子失败: {e}")
            return None
```

## 4. 数据集构建功能

### 4.1 基础数据集
```python
class QlibDatasetService:
    def __init__(self, user_id: int):
        self.user_id = user_id
        init_qlib(user_id)
    
    def create_dataset(self, instruments: list, fields: list, start_time: str, end_time: str, 
                      label_field: str = "Ref($close, -1)/$close - 1"):
        """创建数据集"""
        try:
            from qlib.data.dataset import DatasetH
            
            # 构建数据集配置
            dataset_config = {
                "class": "DatasetH",
                "module_path": "qlib.data.dataset",
                "kwargs": {
                    "handler": {
                        "class": "Alpha158",
                        "module_path": "qlib.contrib.data.handler",
                        "kwargs": {
                            "start_time": start_time,
                            "end_time": end_time,
                            "fit_start_time": start_time,
                            "fit_end_time": end_time,
                            "instruments": instruments,
                            "infer_processors": [
                                {"class": "RobustZScoreNorm", "kwargs": {"fields_group": "feature", "clip_outlier": True}},
                                {"class": "Fillna", "kwargs": {"fields_group": "feature"}}
                            ],
                            "learn_processors": [
                                {"class": "DropnaLabel"},
                                {"class": "CSRankNorm", "kwargs": {"fields_group": "label"}}
                            ]
                        }
                    },
                    "segments": {
                        "train": (start_time, end_time),
                        "valid": (end_time, end_time),
                        "test": (end_time, end_time)
                    }
                }
            }
            
            dataset = DatasetH(dataset_config)
            return dataset
        except Exception as e:
            logger.error(f"创建数据集失败: {e}")
            return None
```

### 4.2 自定义数据集
```python
    def create_custom_dataset(self, instruments: list, features: list, labels: list, 
                            start_time: str, end_time: str, freq: str = "1d"):
        """创建自定义数据集"""
        try:
            from qlib.data.dataset import DatasetH
            
            # 获取特征数据
            data_service = QlibDataService(self.user_id)
            feature_data = data_service.get_features(instruments, features, start_time, end_time, freq)
            label_data = data_service.get_features(instruments, labels, start_time, end_time, freq)
            
            # 合并特征和标签
            combined_data = feature_data.join(label_data)
            
            # 构建数据集
            dataset_config = {
                "class": "DatasetH",
                "module_path": "qlib.data.dataset",
                "kwargs": {
                    "handler": {
                        "class": "DataHandlerLP",
                        "module_path": "qlib.data.dataset",
                        "kwargs": {
                            "data_loader": {
                                "class": "QlibDataLoader",
                                "kwargs": {
                                    "config": {
                                        "feature": features,
                                        "label": labels
                                    }
                                }
                            },
                            "instruments": instruments,
                            "start_time": start_time,
                            "end_time": end_time,
                            "freq": freq,
                            "infer_processors": [
                                {"class": "RobustZScoreNorm", "kwargs": {"fields_group": "feature"}},
                                {"class": "Fillna", "kwargs": {"fields_group": "feature"}}
                            ],
                            "learn_processors": [
                                {"class": "DropnaLabel"},
                                {"class": "CSRankNorm", "kwargs": {"fields_group": "label"}}
                            ]
                        }
                    }
                }
            }
            
            dataset = DatasetH(dataset_config)
            return dataset
        except Exception as e:
            logger.error(f"创建自定义数据集失败: {e}")
            return None
```

## 5. 模型训练功能

### 5.1 基础模型训练
```python
class QlibModelService:
    def __init__(self, user_id: int):
        self.user_id = user_id
        init_qlib(user_id)
    
    def train_lightgbm(self, dataset, model_config: dict = None):
        """训练LightGBM模型"""
        try:
            from qlib.contrib.model.gbdt import LGBModel
            
            if model_config is None:
                model_config = {
                    "loss": "mse",
                    "colsample_bytree": 0.8879,
                    "learning_rate": 0.2,
                    "subsample": 0.8789,
                    "lambda_l1": 205.6999,
                    "lambda_l2": 580.9768,
                    "max_depth": 8,
                    "num_leaves": 210,
                    "num_threads": 20
                }
            
            model = LGBModel(**model_config)
            
            with R.start(experiment_name=f"lightgbm_training_{self.user_id}"):
                model.fit(dataset)
                # 保存模型
                R.save_objects(model=model)
                
                # 获取训练结果
                artifacts = R.list_artifacts()
                return {
                    "model": model,
                    "artifacts": artifacts,
                    "experiment_id": R.get_exp().experiment_id
                }
        except Exception as e:
            logger.error(f"训练LightGBM模型失败: {e}")
            return None
    
    def train_xgboost(self, dataset, model_config: dict = None):
        """训练XGBoost模型"""
        try:
            from qlib.contrib.model.gbdt import XGBModel
            
            if model_config is None:
                model_config = {
                    "loss": "mse",
                    "colsample_bytree": 0.8879,
                    "learning_rate": 0.2,
                    "subsample": 0.8789,
                    "lambda_l1": 205.6999,
                    "lambda_l2": 580.9768,
                    "max_depth": 8,
                    "num_leaves": 210,
                    "num_threads": 20
                }
            
            model = XGBModel(**model_config)
            
            with R.start(experiment_name=f"xgboost_training_{self.user_id}"):
                model.fit(dataset)
                R.save_objects(model=model)
                
                artifacts = R.list_artifacts()
                return {
                    "model": model,
                    "artifacts": artifacts,
                    "experiment_id": R.get_exp().experiment_id
                }
        except Exception as e:
            logger.error(f"训练XGBoost模型失败: {e}")
            return None
```

### 5.2 深度学习模型训练
```python
    def train_lstm(self, dataset, model_config: dict = None):
        """训练LSTM模型"""
        try:
            from qlib.contrib.model.pytorch_lstm import LSTMModel
            
            if model_config is None:
                model_config = {
                    "d_feat": 158,
                    "hidden_size": 64,
                    "num_layers": 2,
                    "dropout": 0.2,
                    "learning_rate": 0.001,
                    "batch_size": 800,
                    "epochs": 100
                }
            
            model = LSTMModel(**model_config)
            
            with R.start(experiment_name=f"lstm_training_{self.user_id}"):
                model.fit(dataset)
                R.save_objects(model=model)
                
                artifacts = R.list_artifacts()
                return {
                    "model": model,
                    "artifacts": artifacts,
                    "experiment_id": R.get_exp().experiment_id
                }
        except Exception as e:
            logger.error(f"训练LSTM模型失败: {e}")
            return None
    
    def train_transformer(self, dataset, model_config: dict = None):
        """训练Transformer模型"""
        try:
            from qlib.contrib.model.pytorch_transformer import TransformerModel
            
            if model_config is None:
                model_config = {
                    "d_feat": 158,
                    "d_model": 64,
                    "nhead": 8,
                    "num_layers": 2,
                    "dropout": 0.2,
                    "learning_rate": 0.001,
                    "batch_size": 800,
                    "epochs": 100
                }
            
            model = TransformerModel(**model_config)
            
            with R.start(experiment_name=f"transformer_training_{self.user_id}"):
                model.fit(dataset)
                R.save_objects(model=model)
                
                artifacts = R.list_artifacts()
                return {
                    "model": model,
                    "artifacts": artifacts,
                    "experiment_id": R.get_exp().experiment_id
                }
        except Exception as e:
            logger.error(f"训练Transformer模型失败: {e}")
            return None
```

## 6. 模型预测功能

### 6.1 基础预测
```python
class QlibPredictionService:
    def __init__(self, user_id: int):
        self.user_id = user_id
        init_qlib(user_id)
    
    def predict(self, model, instruments: list, start_time: str, end_time: str):
        """模型预测"""
        try:
            # 创建预测数据集
            dataset_service = QlibDatasetService(self.user_id)
            dataset = dataset_service.create_dataset(
                instruments=instruments,
                fields=["$close", "$volume", "$high", "$low", "$open"],
                start_time=start_time,
                end_time=end_time
            )
            
            # 执行预测
            predictions = model.predict(dataset)
            return predictions
        except Exception as e:
            logger.error(f"模型预测失败: {e}")
            return None
    
    def predict_with_features(self, model, instruments: list, features: list, 
                            start_time: str, end_time: str):
        """使用自定义特征进行预测"""
        try:
            # 获取特征数据
            data_service = QlibDataService(self.user_id)
            feature_data = data_service.get_features(instruments, features, start_time, end_time)
            
            # 创建预测数据集
            dataset_service = QlibDatasetService(self.user_id)
            dataset = dataset_service.create_custom_dataset(
                instruments=instruments,
                features=features,
                labels=["Ref($close, -1)/$close - 1"],
                start_time=start_time,
                end_time=end_time
            )
            
            # 执行预测
            predictions = model.predict(dataset)
            return predictions
        except Exception as e:
            logger.error(f"自定义特征预测失败: {e}")
            return None
```

## 7. 回测功能

### 7.1 基础回测
```python
class QlibBacktestService:
    def __init__(self, user_id: int):
        self.user_id = user_id
        init_qlib(user_id)
    
    def run_backtest(self, strategy_config: dict):
        """运行回测"""
        try:
            from qlib.contrib.evaluate import backtest_daily
            from qlib.contrib.strategy import TopkDropoutStrategy
            
            with R.start(experiment_name=f"backtest_{self.user_id}"):
                # 创建策略
                strategy = TopkDropoutStrategy(
                    topk=50,
                    n_drop=5,
                    model=model,
                    dataset=dataset
                )
                
                # 运行回测
                portfolio_config = {
                    "account": *********,
                    "benchmark": "SH000300",
                    "exchange_kwargs": {
                        "freq": "day",
                        "limit_threshold": 0.095,
                        "deal_price": "close",
                        "open_cost": 0.0005,
                        "close_cost": 0.0015,
                        "min_cost": 5
                    }
                }
                
                analysis = dict(
                    "enabled": True,
                    "class": "RiskAnalysis",
                    "kwargs": {
                        "config": {
                            "benchmark": "SH000300",
                            "report_analysis": True
                        }
                    }
                )
                
                backtest_result = backtest_daily(
                    model=model,
                    dataset=dataset,
                    strategy=strategy,
                    portfolio_config=portfolio_config,
                    analysis_config=analysis
                )
                
                # 保存回测结果
                R.save_objects(backtest_result=backtest_result)
                
                return {
                    "backtest_result": backtest_result,
                    "experiment_id": R.get_exp().experiment_id
                }
        except Exception as e:
            logger.error(f"回测失败: {e}")
            return None
```

### 7.2 自定义策略回测
```python
    def run_custom_strategy_backtest(self, strategy_code: str, config: dict):
        """运行自定义策略回测"""
        try:
            # 动态执行策略代码
            exec(strategy_code)
            
            # 获取策略函数
            strategy_func = locals().get('strategy')
            
            with R.start(experiment_name=f"custom_backtest_{self.user_id}"):
                # 运行回测
                backtest_result = backtest_daily(
                    model=None,  # 自定义策略不需要模型
                    dataset=None,  # 自定义策略不需要数据集
                    strategy=strategy_func,
                    portfolio_config=config.get("portfolio_config", {}),
                    analysis_config=config.get("analysis_config", {})
                )
                
                R.save_objects(backtest_result=backtest_result)
                
                return {
                    "backtest_result": backtest_result,
                    "experiment_id": R.get_exp().experiment_id
                }
        except Exception as e:
            logger.error(f"自定义策略回测失败: {e}")
            return None
```

## 8. 策略执行功能

### 8.1 基础策略执行
```python
class QlibStrategyService:
    def __init__(self, user_id: int):
        self.user_id = user_id
        init_qlib(user_id)
    
    def execute_strategy(self, strategy_code: str, instruments: list, 
                        start_time: str, end_time: str):
        """执行策略"""
        try:
            # 动态执行策略代码
            exec(strategy_code)
            
            # 获取策略函数
            strategy_func = locals().get('strategy')
            
            # 执行策略
            positions = strategy_func({
                "instruments": instruments,
                "start_time": start_time,
                "end_time": end_time
            })
            
            return positions
        except Exception as e:
            logger.error(f"策略执行失败: {e}")
            return None
```

### 8.2 实时策略执行
```python
    def execute_realtime_strategy(self, strategy_code: str, instruments: list):
        """实时策略执行"""
        try:
            # 动态执行策略代码
            exec(strategy_code)
            
            # 获取策略函数
            strategy_func = locals().get('strategy')
            
            # 获取最新数据
            data_service = QlibDataService(self.user_id)
            latest_data = data_service.get_features(
                instruments=instruments,
                fields=["$close", "$volume", "$high", "$low", "$open"],
                start_time="2024-01-01",
                end_time="2024-12-31"
            )
            
            # 执行策略
            positions = strategy_func({
                "data": latest_data,
                "instruments": instruments
            })
            
            return positions
        except Exception as e:
            logger.error(f"实时策略执行失败: {e}")
            return None
```

## 9. 模型评估功能

### 9.1 模型性能评估
```python
class QlibEvaluationService:
    def __init__(self, user_id: int):
        self.user_id = user_id
        init_qlib(user_id)
    
    def evaluate_model(self, model, dataset):
        """评估模型性能"""
        try:
            from qlib.contrib.evaluate import risk_analysis
            
            # 模型预测
            predictions = model.predict(dataset)
            
            # 风险分析
            analysis = risk_analysis(
                predictions=predictions,
                dataset=dataset,
                report_analysis=True
            )
            
            return {
                "predictions": predictions,
                "analysis": analysis
            }
        except Exception as e:
            logger.error(f"模型评估失败: {e}")
            return None
    
    def calculate_metrics(self, predictions, labels):
        """计算评估指标"""
        try:
            from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
            
            metrics = {
                "mse": mean_squared_error(labels, predictions),
                "mae": mean_absolute_error(labels, predictions),
                "r2": r2_score(labels, predictions)
            }
            
            return metrics
        except Exception as e:
            logger.error(f"计算评估指标失败: {e}")
            return None
```

## 10. 实验管理功能

### 10.1 实验记录
```python
class QlibExperimentService:
    def __init__(self, user_id: int):
        self.user_id = user_id
        init_qlib(user_id)
    
    def start_experiment(self, experiment_name: str):
        """开始实验"""
        try:
            experiment = R.start(experiment_name=experiment_name)
            return experiment
        except Exception as e:
            logger.error(f"开始实验失败: {e}")
            return None
    
    def log_parameters(self, **kwargs):
        """记录参数"""
        try:
            R.log_params(**kwargs)
        except Exception as e:
            logger.error(f"记录参数失败: {e}")
    
    def log_metrics(self, **kwargs):
        """记录指标"""
        try:
            R.log_metrics(**kwargs)
        except Exception as e:
            logger.error(f"记录指标失败: {e}")
    
    def save_objects(self, **kwargs):
        """保存对象"""
        try:
            R.save_objects(**kwargs)
        except Exception as e:
            logger.error(f"保存对象失败: {e}")
    
    def end_experiment(self):
        """结束实验"""
        try:
            R.end_exp()
        except Exception as e:
            logger.error(f"结束实验失败: {e}")
```

## 11. 完整功能调用示例

### 11.1 端到端工作流
```python
def complete_workflow(user_id: int, config: dict):
    """完整的量化投资工作流"""
    try:
        # 1. 初始化服务
        data_service = QlibDataService(user_id)
        dataset_service = QlibDatasetService(user_id)
        model_service = QlibModelService(user_id)
        backtest_service = QlibBacktestService(user_id)
        evaluation_service = QlibEvaluationService(user_id)
        
        # 2. 获取数据
        instruments = config.get("instruments", ["000001.SZ", "000002.SZ"])
        start_time = config.get("start_time", "2020-01-01")
        end_time = config.get("end_time", "2023-12-31")
        
        # 3. 创建数据集
        dataset = dataset_service.create_dataset(
            instruments=instruments,
            fields=["$close", "$volume", "$high", "$low", "$open"],
            start_time=start_time,
            end_time=end_time
        )
        
        # 4. 训练模型
        model_result = model_service.train_lightgbm(dataset)
        
        # 5. 模型预测
        predictions = model_result["model"].predict(dataset)
        
        # 6. 模型评估
        evaluation = evaluation_service.evaluate_model(model_result["model"], dataset)
        
        # 7. 回测
        backtest_result = backtest_service.run_backtest({
            "model": model_result["model"],
            "dataset": dataset
        })
        
        return {
            "model_result": model_result,
            "predictions": predictions,
            "evaluation": evaluation,
            "backtest_result": backtest_result
        }
    except Exception as e:
        logger.error(f"完整工作流执行失败: {e}")
        return None
```

### 11.2 策略开发工作流
```python
def strategy_development_workflow(user_id: int, strategy_code: str, config: dict):
    """策略开发工作流"""
    try:
        # 1. 初始化服务
        strategy_service = QlibStrategyService(user_id)
        backtest_service = QlibBacktestService(user_id)
        
        # 2. 策略验证
        positions = strategy_service.execute_strategy(
            strategy_code=strategy_code,
            instruments=config.get("instruments", []),
            start_time=config.get("start_time", "2020-01-01"),
            end_time=config.get("end_time", "2023-12-31")
        )
        
        # 3. 策略回测
        backtest_result = backtest_service.run_custom_strategy_backtest(
            strategy_code=strategy_code,
            config=config
        )
        
        return {
            "positions": positions,
            "backtest_result": backtest_result
        }
    except Exception as e:
        logger.error(f"策略开发工作流失败: {e}")
        return None
```

## 12. 错误处理和日志

### 12.1 错误处理
```python
import logging
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

def handle_qlib_error(func):
    """Qlib错误处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Qlib功能调用失败: {func.__name__}, 错误: {e}")
            return None
    return wrapper
```

### 12.2 性能监控
```python
import time
from functools import wraps

def monitor_performance(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        logger.info(f"{func.__name__} 执行时间: {end_time - start_time:.2f}秒")
        return result
    return wrapper
```

## 13. 配置管理

### 13.1 用户配置
```python
class QlibConfigManager:
    def __init__(self, user_id: int):
        self.user_id = user_id
        self.config = self.load_user_config()
    
    def load_user_config(self) -> Dict[str, Any]:
        """加载用户配置"""
        return {
            "data_path": f"~/.qlib/qlib_data/cn_data",
            "region": "cn",
            "auto_mount": False,
            "user_id": self.user_id,
            "max_workers": 4,
            "cache_size": 1000
        }
    
    def update_config(self, new_config: Dict[str, Any]):
        """更新配置"""
        self.config.update(new_config)
        self.save_user_config()
    
    def save_user_config(self):
        """保存用户配置"""
        # 保存到数据库或文件
        pass
```

## 14. 总结

本文档提供了完整的Qlib功能调用指南，包括：

1. **数据获取功能**：基础数据查询、高级数据查询、多层级数据获取
2. **数据集构建**：基础数据集、自定义数据集创建
3. **模型训练**：LightGBM、XGBoost、LSTM、Transformer等模型训练
4. **模型预测**：基础预测、自定义特征预测
5. **回测功能**：基础回测、自定义策略回测
6. **策略执行**：基础策略执行、实时策略执行
7. **模型评估**：性能评估、指标计算
8. **实验管理**：实验记录、参数记录、指标记录
9. **完整工作流**：端到端工作流、策略开发工作流
10. **错误处理和监控**：错误处理、性能监控、配置管理

这些功能可以无缝集成到多用户量化投资平台中，为用户提供完整的量化投资解决方案。 