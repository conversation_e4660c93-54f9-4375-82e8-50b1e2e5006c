#!/usr/bin/env python3
"""
测试超参数优化修复
"""

import qlib
import numpy as np
import pandas as pd
from qlib.contrib.model.gbdt import LGBModel
from qlib.contrib.data.handler import Alpha158
from qlib.data.dataset import DatasetH
from sklearn.metrics import mean_squared_error
import warnings
warnings.filterwarnings('ignore')

def calculate_ic(y_true, y_pred):
    """计算IC值"""
    valid_mask = ~(np.isnan(y_pred) | np.isnan(y_true))
    pred_clean = y_pred[valid_mask]
    true_clean = y_true[valid_mask]
    
    if len(pred_clean) <= 1:
        return 0
    
    correlation = np.corrcoef(pred_clean, true_clean)[0, 1]
    return correlation if not np.isnan(correlation) else 0

def test_hyperparameter_optimization():
    print("=== 测试超参数优化修复 ===")
    
    # 初始化Qlib
    print("初始化Qlib...")
    qlib.init(mount_path="~/.qlib/qlib_data/cn_data", region="cn")
    
    # 准备数据处理器（使用最小数据集）
    handler = Alpha158(
        instruments=['SH600000'],  # 只使用1只股票
        start_time='2020-01-01',
        end_time='2020-03-31',  # 很短的时间范围
        fit_start_time='2020-01-01',
        fit_end_time='2020-02-29',
        freq='day'
    )
    
    # 创建数据集
    dataset = DatasetH(
        handler=handler,
        segments={
            'train': ('2020-01-01', '2020-02-29'),
            'valid': ('2020-03-01', '2020-03-15'),
            'test': ('2020-03-16', '2020-03-31')
        }
    )
    
    print("准备数据...")
    train_data = dataset.prepare('train')
    valid_data = dataset.prepare('valid')
    test_data = dataset.prepare('test')
    
    print(f"原始数据形状:")
    print(f"- 训练集: {train_data.shape}")
    print(f"- 验证集: {valid_data.shape}")
    print(f"- 测试集: {test_data.shape}")
    
    # 使用修复后的数据访问方式
    label_cols = [col for col in train_data.columns if 'LABEL' in str(col)]
    print(f"找到标签列: {label_cols}")
    
    if not label_cols:
        print("❌ 没有找到标签列")
        return False
    
    feature_cols = [col for col in train_data.columns if 'LABEL' not in str(col)]
    
    X_train = train_data[feature_cols]
    y_train = train_data[label_cols[0]]
    X_test = test_data[feature_cols]
    y_test = test_data[label_cols[0]]
    
    print(f"数据分离后:")
    print(f"- 特征: {X_train.shape}")
    print(f"- 标签: {y_train.shape}")
    
    # 检查空数据问题
    if X_train.shape[0] == 0 or X_test.shape[0] == 0:
        print("❌ 训练集或测试集为空")
        return False
    
    print("✅ 数据分割成功，无空数据")
    
    # 处理NaN值
    print("处理NaN值...")
    X_train_clean = X_train.fillna(X_train.mean()).fillna(0)
    y_train_clean = y_train.fillna(y_train.median())
    X_test_clean = X_test.fillna(X_train.mean()).fillna(0)
    y_test_clean = y_test.fillna(y_train.median())
    
    print(f"NaN处理后数据形状:")
    print(f"- 训练特征: {X_train_clean.shape}")
    print(f"- 训练标签: {y_train_clean.shape}")
    print(f"- 测试特征: {X_test_clean.shape}")
    print(f"- 测试标签: {y_test_clean.shape}")
    
    # 验证没有NaN
    assert not X_train_clean.isna().any().any(), "训练特征仍有NaN"
    assert not y_train_clean.isna().any(), "训练标签仍有NaN"
    print("✅ NaN处理成功")
    
    # 测试LGBModel
    print("\n测试LGBModel...")
    try:
        model = LGBModel(
            loss='mse',
            n_estimators=10,  # 很小的值以加快测试
            max_depth=3,
            learning_rate=0.1,
            verbose=-1
        )
        
        model.fit(dataset)
        pred = model.predict(dataset, segment='test')
        
        print(f"预测结果类型: {type(pred)}")
        
        # 处理预测结果
        if hasattr(pred, 'values'):
            pred_values = pred.values.flatten() if hasattr(pred.values, 'flatten') else pred.values
        else:
            pred_values = pred.flatten() if hasattr(pred, 'flatten') else pred
        
        print(f"预测值形状: {pred_values.shape}")
        print(f"真实值形状: {y_test_clean.shape}")
        
        # 确保形状匹配
        if len(pred_values) != len(y_test_clean):
            print(f"警告: 预测值长度 {len(pred_values)} != 真实值长度 {len(y_test_clean)}")
            min_len = min(len(pred_values), len(y_test_clean))
            pred_values = pred_values[:min_len]
            y_test_clean = y_test_clean.iloc[:min_len] if hasattr(y_test_clean, 'iloc') else y_test_clean[:min_len]
        
        # 计算指标
        ic = calculate_ic(y_test_clean.values if hasattr(y_test_clean, 'values') else y_test_clean, pred_values)
        mse = mean_squared_error(y_test_clean, pred_values)
        
        print(f"✅ 模型训练成功!")
        print(f"IC值: {ic:.4f}")
        print(f"MSE: {mse:.6f}")
        
    except Exception as e:
        print(f"❌ 模型训练失败: {e}")
        return False
    
    print("\n=== 所有测试通过! ===")
    return True

if __name__ == "__main__":
    success = test_hyperparameter_optimization()
    if success:
        print("修复有效，可以应用到完整脚本")
    else:
        print("修复存在问题，需要进一步调试")