# 第6章：回测系统

## 学习目标

通过本章学习，您将能够：
- 理解Qlib回测框架的设计原理
- 掌握回测系统的核心组件和功能
- 学会进行回测分析和性能评估
- 掌握回测报告的生成和解读
- 理解回测系统的优化和扩展方法

## 6.1 回测框架设计

### 6.1.1 回测引擎架构

#### 回测引擎核心组件

Qlib的回测引擎采用事件驱动架构，包含以下核心组件：

```python
from qlib.backtest import backtest, executor
from qlib.contrib.evaluate import risk_analysis

class BacktestEngine:
    """回测引擎核心类"""
    
    def __init__(self, start_time, end_time, benchmark, universe, freq='day'):
        self.start_time = start_time
        self.end_time = end_time
        self.benchmark = benchmark
        self.universe = universe
        self.freq = freq
        
        # 核心组件
        self.data_handler = None
        self.strategy = None
        self.executor = None
        self.analyzer = None
    
    def set_data_handler(self, data_handler):
        """设置数据处理器"""
        self.data_handler = data_handler
    
    def set_strategy(self, strategy):
        """设置策略"""
        self.strategy = strategy
    
    def set_executor(self, executor):
        """设置执行器"""
        self.executor = executor
    
    def run_backtest(self):
        """运行回测"""
        # 初始化回测环境
        self._initialize_backtest()
        
        # 执行回测
        results = backtest(
            start_time=self.start_time,
            end_time=self.end_time,
            strategy=self.strategy,
            benchmark=self.benchmark,
            universe=self.universe,
            freq=self.freq
        )
        
        return results
```

#### 事件驱动机制

```python
class EventDrivenBacktest:
    """事件驱动回测系统"""
    
    def __init__(self):
        self.events = []
        self.current_time = None
        self.positions = {}
        self.cash = 1000000  # 初始资金
        
    def add_event(self, event):
        """添加事件"""
        self.events.append(event)
        self.events.sort(key=lambda x: x.timestamp)
    
    def process_events(self):
        """处理事件"""
        for event in self.events:
            self.current_time = event.timestamp
            
            if event.type == 'MARKET_DATA':
                self.handle_market_data(event)
            elif event.type == 'SIGNAL':
                self.handle_signal(event)
            elif event.type == 'ORDER':
                self.handle_order(event)
            elif event.type == 'FILL':
                self.handle_fill(event)
    
    def handle_market_data(self, event):
        """处理市场数据事件"""
        # 更新市场数据
        pass
    
    def handle_signal(self, event):
        """处理信号事件"""
        # 生成交易信号
        pass
    
    def handle_order(self, event):
        """处理订单事件"""
        # 执行订单
        pass
    
    def handle_fill(self, event):
        """处理成交事件"""
        # 更新持仓
        pass
```

### 6.1.2 事件驱动机制

#### 事件类型定义

```python
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, Any

@dataclass
class Event:
    """事件基类"""
    timestamp: datetime
    type: str
    data: Dict[str, Any]

class MarketDataEvent(Event):
    """市场数据事件"""
    def __init__(self, timestamp, symbol, price, volume):
        super().__init__(timestamp, 'MARKET_DATA', {
            'symbol': symbol,
            'price': price,
            'volume': volume
        })

class SignalEvent(Event):
    """信号事件"""
    def __init__(self, timestamp, signals):
        super().__init__(timestamp, 'SIGNAL', {
            'signals': signals
        })

class OrderEvent(Event):
    """订单事件"""
    def __init__(self, timestamp, symbol, order_type, quantity, price):
        super().__init__(timestamp, 'ORDER', {
            'symbol': symbol,
            'order_type': order_type,
            'quantity': quantity,
            'price': price
        })

class FillEvent(Event):
    """成交事件"""
    def __init__(self, timestamp, symbol, quantity, price, commission):
        super().__init__(timestamp, 'FILL', {
            'symbol': symbol,
            'quantity': quantity,
            'price': price,
            'commission': commission
        })
```

#### 事件处理器

```python
class EventHandler:
    """事件处理器"""
    
    def __init__(self):
        self.portfolio = Portfolio()
        self.order_manager = OrderManager()
        self.data_feed = DataFeed()
    
    def handle_market_data(self, event):
        """处理市场数据事件"""
        # 更新市场数据
        self.data_feed.update_price(event.data['symbol'], event.data['price'])
        
        # 检查是否有待执行的订单
        self.order_manager.check_orders(event.data)
    
    def handle_signal(self, event):
        """处理信号事件"""
        signals = event.data['signals']
        
        # 生成订单
        for symbol, signal in signals.items():
            if signal > 0:  # 买入信号
                order = OrderEvent(
                    event.timestamp,
                    symbol,
                    'BUY',
                    abs(signal),
                    self.data_feed.get_price(symbol)
                )
                self.order_manager.add_order(order)
            elif signal < 0:  # 卖出信号
                order = OrderEvent(
                    event.timestamp,
                    symbol,
                    'SELL',
                    abs(signal),
                    self.data_feed.get_price(symbol)
                )
                self.order_manager.add_order(order)
    
    def handle_order(self, event):
        """处理订单事件"""
        # 检查资金和持仓
        if self.portfolio.can_execute_order(event.data):
            # 执行订单
            fill = FillEvent(
                event.timestamp,
                event.data['symbol'],
                event.data['quantity'],
                event.data['price'],
                self.calculate_commission(event.data)
            )
            self.handle_fill(fill)
    
    def handle_fill(self, event):
        """处理成交事件"""
        # 更新持仓
        self.portfolio.update_position(event.data)
```

### 6.1.3 订单管理系统

#### 订单管理器

```python
class OrderManager:
    """订单管理器"""
    
    def __init__(self):
        self.pending_orders = []
        self.filled_orders = []
        self.cancelled_orders = []
    
    def add_order(self, order):
        """添加订单"""
        self.pending_orders.append(order)
    
    def execute_order(self, order, market_price):
        """执行订单"""
        # 检查订单是否可执行
        if self.can_execute(order, market_price):
            # 创建成交记录
            fill = FillEvent(
                order.timestamp,
                order.data['symbol'],
                order.data['quantity'],
                market_price,
                self.calculate_commission(order.data)
            )
            
            # 更新订单状态
            self.pending_orders.remove(order)
            self.filled_orders.append(fill)
            
            return fill
        
        return None
    
    def can_execute(self, order, market_price):
        """检查订单是否可执行"""
        if order.data['order_type'] == 'MARKET':
            return True
        elif order.data['order_type'] == 'LIMIT':
            if order.data['side'] == 'BUY':
                return market_price <= order.data['price']
            else:
                return market_price >= order.data['price']
        
        return False
    
    def calculate_commission(self, order_data):
        """计算手续费"""
        commission_rate = 0.0005  # 0.05%
        return order_data['quantity'] * order_data['price'] * commission_rate
```

#### 订单类型

```python
class Order:
    """订单基类"""
    
    def __init__(self, symbol, order_type, quantity, price=None):
        self.symbol = symbol
        self.order_type = order_type
        self.quantity = quantity
        self.price = price
        self.status = 'PENDING'
        self.timestamp = datetime.now()

class MarketOrder(Order):
    """市价单"""
    def __init__(self, symbol, quantity):
        super().__init__(symbol, 'MARKET', quantity)

class LimitOrder(Order):
    """限价单"""
    def __init__(self, symbol, quantity, price):
        super().__init__(symbol, 'LIMIT', quantity, price)

class StopOrder(Order):
    """止损单"""
    def __init__(self, symbol, quantity, stop_price):
        super().__init__(symbol, 'STOP', quantity, stop_price)
```

### 6.1.4 风险控制模块

#### 风险控制器

```python
class RiskController:
    """风险控制器"""
    
    def __init__(self, max_position_size=0.1, max_drawdown=0.2, max_leverage=2.0):
        self.max_position_size = max_position_size
        self.max_drawdown = max_drawdown
        self.max_leverage = max_leverage
        self.portfolio_value_history = []
    
    def check_order_risk(self, order, portfolio):
        """检查订单风险"""
        # 检查持仓集中度
        if not self.check_position_concentration(order, portfolio):
            return False, "持仓集中度过高"
        
        # 检查杠杆率
        if not self.check_leverage(order, portfolio):
            return False, "杠杆率过高"
        
        # 检查资金充足性
        if not self.check_sufficient_funds(order, portfolio):
            return False, "资金不足"
        
        return True, "风险检查通过"
    
    def check_position_concentration(self, order, portfolio):
        """检查持仓集中度"""
        current_position = portfolio.get_position(order.symbol)
        new_position = current_position + order.quantity
        
        portfolio_value = portfolio.get_total_value()
        position_value = abs(new_position * portfolio.get_price(order.symbol))
        
        concentration = position_value / portfolio_value
        
        return concentration <= self.max_position_size
    
    def check_leverage(self, order, portfolio):
        """检查杠杆率"""
        total_exposure = portfolio.get_total_exposure()
        portfolio_value = portfolio.get_total_value()
        
        leverage = total_exposure / portfolio_value
        
        return leverage <= self.max_leverage
    
    def check_sufficient_funds(self, order, portfolio):
        """检查资金充足性"""
        required_cash = order.quantity * portfolio.get_price(order.symbol)
        available_cash = portfolio.get_cash()
        
        return available_cash >= required_cash
    
    def update_portfolio_value(self, portfolio_value):
        """更新组合价值历史"""
        self.portfolio_value_history.append(portfolio_value)
    
    def calculate_drawdown(self):
        """计算回撤"""
        if len(self.portfolio_value_history) < 2:
            return 0
        
        peak = max(self.portfolio_value_history)
        current = self.portfolio_value_history[-1]
        
        drawdown = (peak - current) / peak
        
        return drawdown
    
    def check_drawdown_limit(self):
        """检查回撤限制"""
        current_drawdown = self.calculate_drawdown()
        return current_drawdown <= self.max_drawdown
```

## 6.2 回测分析

### 6.2.1 收益率分析

#### 收益率计算

```python
import pandas as pd
import numpy as np

class ReturnAnalyzer:
    """收益率分析器"""
    
    def __init__(self, portfolio_values, benchmark_values):
        self.portfolio_values = portfolio_values
        self.benchmark_values = benchmark_values
        self.portfolio_returns = self.calculate_returns(portfolio_values)
        self.benchmark_returns = self.calculate_returns(benchmark_values)
    
    def calculate_returns(self, values):
        """计算收益率"""
        returns = values.pct_change().dropna()
        return returns
    
    def calculate_cumulative_returns(self):
        """计算累积收益率"""
        cumulative_returns = (1 + self.portfolio_returns).cumprod() - 1
        return cumulative_returns
    
    def calculate_annualized_return(self):
        """计算年化收益率"""
        total_return = self.calculate_cumulative_returns().iloc[-1]
        years = len(self.portfolio_returns) / 252
        
        annualized_return = (1 + total_return) ** (1 / years) - 1
        return annualized_return
    
    def calculate_volatility(self):
        """计算波动率"""
        volatility = self.portfolio_returns.std() * np.sqrt(252)
        return volatility
    
    def calculate_sharpe_ratio(self, risk_free_rate=0.02):
        """计算夏普比率"""
        excess_returns = self.portfolio_returns - risk_free_rate / 252
        sharpe_ratio = excess_returns.mean() / self.portfolio_returns.std() * np.sqrt(252)
        return sharpe_ratio
    
    def calculate_sortino_ratio(self, risk_free_rate=0.02):
        """计算索提诺比率"""
        excess_returns = self.portfolio_returns - risk_free_rate / 252
        downside_returns = excess_returns[excess_returns < 0]
        downside_deviation = downside_returns.std()
        
        if downside_deviation == 0:
            return np.inf
        
        sortino_ratio = excess_returns.mean() / downside_deviation * np.sqrt(252)
        return sortino_ratio
```

#### 收益率分解

```python
class ReturnDecomposition:
    """收益率分解"""
    
    def __init__(self, portfolio_returns, factor_returns):
        self.portfolio_returns = portfolio_returns
        self.factor_returns = factor_returns
    
    def calculate_factor_exposure(self):
        """计算因子暴露"""
        # 使用线性回归计算因子暴露
        from sklearn.linear_model import LinearRegression
        
        model = LinearRegression()
        model.fit(self.factor_returns, self.portfolio_returns)
        
        factor_exposures = dict(zip(self.factor_returns.columns, model.coef_))
        return factor_exposures
    
    def decompose_returns(self):
        """分解收益率"""
        factor_exposures = self.calculate_factor_exposure()
        
        # 计算因子贡献
        factor_contributions = {}
        for factor, exposure in factor_exposures.items():
            factor_contributions[factor] = exposure * self.factor_returns[factor]
        
        # 计算残差
        predicted_returns = sum(factor_contributions.values())
        residual_returns = self.portfolio_returns - predicted_returns
        
        return {
            'factor_contributions': factor_contributions,
            'residual_returns': residual_returns,
            'factor_exposures': factor_exposures
        }
```

### 6.2.2 风险指标计算

#### 风险指标分析

```python
class RiskAnalyzer:
    """风险分析器"""
    
    def __init__(self, returns):
        self.returns = returns
    
    def calculate_var(self, confidence_level=0.05):
        """计算VaR"""
        var = np.percentile(self.returns, confidence_level * 100)
        return var
    
    def calculate_cvar(self, confidence_level=0.05):
        """计算CVaR"""
        var = self.calculate_var(confidence_level)
        cvar = self.returns[self.returns <= var].mean()
        return cvar
    
    def calculate_max_drawdown(self, values):
        """计算最大回撤"""
        peak = values.expanding().max()
        drawdown = (values - peak) / peak
        max_drawdown = drawdown.min()
        return max_drawdown
    
    def calculate_calmar_ratio(self, annualized_return, max_drawdown):
        """计算卡玛比率"""
        if max_drawdown == 0:
            return np.inf
        calmar_ratio = annualized_return / abs(max_drawdown)
        return calmar_ratio
    
    def calculate_information_ratio(self, benchmark_returns):
        """计算信息比率"""
        active_returns = self.returns - benchmark_returns
        information_ratio = active_returns.mean() / active_returns.std() * np.sqrt(252)
        return information_ratio
    
    def calculate_treynor_ratio(self, benchmark_returns, risk_free_rate=0.02):
        """计算特雷诺比率"""
        excess_returns = self.returns - risk_free_rate / 252
        beta = self.calculate_beta(benchmark_returns)
        
        if beta == 0:
            return np.inf
        
        treynor_ratio = excess_returns.mean() / beta * 252
        return treynor_ratio
    
    def calculate_beta(self, benchmark_returns):
        """计算贝塔系数"""
        covariance = np.cov(self.returns, benchmark_returns)[0, 1]
        benchmark_variance = np.var(benchmark_returns)
        beta = covariance / benchmark_variance
        return beta
```

#### 风险归因分析

```python
class RiskAttribution:
    """风险归因分析"""
    
    def __init__(self, portfolio_weights, covariance_matrix):
        self.portfolio_weights = portfolio_weights
        self.covariance_matrix = covariance_matrix
    
    def calculate_marginal_risk_contribution(self):
        """计算边际风险贡献"""
        portfolio_risk = np.sqrt(
            self.portfolio_weights.T @ self.covariance_matrix @ self.portfolio_weights
        )
        
        marginal_risk = self.covariance_matrix @ self.portfolio_weights / portfolio_risk
        
        return marginal_risk
    
    def calculate_risk_contribution(self):
        """计算风险贡献"""
        marginal_risk = self.calculate_marginal_risk_contribution()
        risk_contribution = self.portfolio_weights * marginal_risk
        
        return risk_contribution
    
    def calculate_percentage_risk_contribution(self):
        """计算百分比风险贡献"""
        risk_contribution = self.calculate_risk_contribution()
        portfolio_risk = np.sqrt(
            self.portfolio_weights.T @ self.covariance_matrix @ self.portfolio_weights
        )
        
        percentage_contribution = risk_contribution / portfolio_risk
        
        return percentage_contribution
```

### 6.2.3 交易成本分析

#### 交易成本计算

```python
class TransactionCostAnalyzer:
    """交易成本分析器"""
    
    def __init__(self, commission_rate=0.0005, slippage_rate=0.0001):
        self.commission_rate = commission_rate
        self.slippage_rate = slippage_rate
    
    def calculate_commission(self, trade_value):
        """计算手续费"""
        commission = trade_value * self.commission_rate
        return commission
    
    def calculate_slippage(self, trade_value):
        """计算滑点成本"""
        slippage = trade_value * self.slippage_rate
        return slippage
    
    def calculate_total_cost(self, trade_value):
        """计算总交易成本"""
        commission = self.calculate_commission(trade_value)
        slippage = self.calculate_slippage(trade_value)
        total_cost = commission + slippage
        
        return total_cost
    
    def calculate_cost_impact(self, portfolio_returns, cost_returns):
        """计算成本影响"""
        net_returns = portfolio_returns - cost_returns
        
        # 计算成本对收益率的影响
        cost_impact = portfolio_returns.mean() - net_returns.mean()
        
        return cost_impact
    
    def analyze_turnover_cost(self, portfolio_values, turnover_rates):
        """分析换手率成本"""
        # 计算换手成本
        turnover_costs = turnover_rates * self.commission_rate
        
        # 计算年化换手成本
        annual_turnover_cost = turnover_costs.mean() * 252
        
        return annual_turnover_cost
```

### 6.2.4 绩效归因分析

#### 绩效归因

```python
class PerformanceAttribution:
    """绩效归因分析"""
    
    def __init__(self, portfolio_returns, benchmark_returns, factor_returns):
        self.portfolio_returns = portfolio_returns
        self.benchmark_returns = benchmark_returns
        self.factor_returns = factor_returns
    
    def calculate_brinson_attribution(self, portfolio_weights, benchmark_weights, sector_returns):
        """计算Brinson归因"""
        # 资产配置效应
        allocation_effect = (portfolio_weights - benchmark_weights) * sector_returns
        
        # 个股选择效应
        selection_effect = benchmark_weights * (portfolio_returns - sector_returns)
        
        # 交互效应
        interaction_effect = (portfolio_weights - benchmark_weights) * (portfolio_returns - sector_returns)
        
        return {
            'allocation_effect': allocation_effect,
            'selection_effect': selection_effect,
            'interaction_effect': interaction_effect
        }
    
    def calculate_factor_attribution(self):
        """计算因子归因"""
        # 计算因子暴露
        factor_exposures = self.calculate_factor_exposure()
        
        # 计算因子贡献
        factor_contributions = {}
        for factor, exposure in factor_exposures.items():
            factor_contributions[factor] = exposure * self.factor_returns[factor].mean()
        
        # 计算残差贡献
        total_factor_contribution = sum(factor_contributions.values())
        residual_contribution = self.portfolio_returns.mean() - total_factor_contribution
        
        return {
            'factor_contributions': factor_contributions,
            'residual_contribution': residual_contribution
        }
    
    def calculate_factor_exposure(self):
        """计算因子暴露"""
        from sklearn.linear_model import LinearRegression
        
        model = LinearRegression()
        model.fit(self.factor_returns, self.portfolio_returns)
        
        factor_exposures = dict(zip(self.factor_returns.columns, model.coef_))
        return factor_exposures
```

## 6.3 回测报告生成

### 6.3.1 图形化报告

#### 报告生成器

```python
import matplotlib.pyplot as plt
import seaborn as sns
from qlib.contrib.evaluate import risk_analysis

class BacktestReportGenerator:
    """回测报告生成器"""
    
    def __init__(self, backtest_results):
        self.results = backtest_results
        self.portfolio_returns = backtest_results['returns']
        self.benchmark_returns = backtest_results['benchmark_returns']
        self.positions = backtest_results['positions']
    
    def generate_performance_chart(self):
        """生成绩效图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 累积收益率
        cumulative_returns = (1 + self.portfolio_returns).cumprod()
        benchmark_cumulative = (1 + self.benchmark_returns).cumprod()
        
        axes[0, 0].plot(cumulative_returns.index, cumulative_returns.values, label='Portfolio')
        axes[0, 0].plot(benchmark_cumulative.index, benchmark_cumulative.values, label='Benchmark')
        axes[0, 0].set_title('Cumulative Returns')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # 收益率分布
        axes[0, 1].hist(self.portfolio_returns, bins=50, alpha=0.7, label='Portfolio')
        axes[0, 1].hist(self.benchmark_returns, bins=50, alpha=0.7, label='Benchmark')
        axes[0, 1].set_title('Return Distribution')
        axes[0, 1].legend()
        
        # 回撤
        drawdown = self.calculate_drawdown()
        axes[1, 0].fill_between(drawdown.index, drawdown.values, 0, alpha=0.3, color='red')
        axes[1, 0].set_title('Drawdown')
        axes[1, 0].grid(True)
        
        # 滚动夏普比率
        rolling_sharpe = self.calculate_rolling_sharpe()
        axes[1, 1].plot(rolling_sharpe.index, rolling_sharpe.values)
        axes[1, 1].set_title('Rolling Sharpe Ratio')
        axes[1, 1].grid(True)
        
        plt.tight_layout()
        return fig
    
    def calculate_drawdown(self):
        """计算回撤"""
        cumulative_returns = (1 + self.portfolio_returns).cumprod()
        peak = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - peak) / peak
        return drawdown
    
    def calculate_rolling_sharpe(self, window=252):
        """计算滚动夏普比率"""
        rolling_mean = self.portfolio_returns.rolling(window).mean()
        rolling_std = self.portfolio_returns.rolling(window).std()
        rolling_sharpe = rolling_mean / rolling_std * np.sqrt(252)
        return rolling_sharpe
```

#### 风险分析图表

```python
class RiskAnalysisCharts:
    """风险分析图表"""
    
    def __init__(self, portfolio_returns, benchmark_returns):
        self.portfolio_returns = portfolio_returns
        self.benchmark_returns = benchmark_returns
    
    def generate_risk_charts(self):
        """生成风险分析图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 风险收益散点图
        portfolio_vol = self.portfolio_returns.std() * np.sqrt(252)
        portfolio_return = self.portfolio_returns.mean() * 252
        benchmark_vol = self.benchmark_returns.std() * np.sqrt(252)
        benchmark_return = self.benchmark_returns.mean() * 252
        
        axes[0, 0].scatter(portfolio_vol, portfolio_return, color='blue', s=100, label='Portfolio')
        axes[0, 0].scatter(benchmark_vol, benchmark_return, color='red', s=100, label='Benchmark')
        axes[0, 0].set_xlabel('Volatility')
        axes[0, 0].set_ylabel('Return')
        axes[0, 0].set_title('Risk-Return Scatter')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # 相关性热力图
        correlation_matrix = pd.DataFrame({
            'Portfolio': self.portfolio_returns,
            'Benchmark': self.benchmark_returns
        }).corr()
        
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', ax=axes[0, 1])
        axes[0, 1].set_title('Correlation Matrix')
        
        # 滚动贝塔
        rolling_beta = self.calculate_rolling_beta()
        axes[1, 0].plot(rolling_beta.index, rolling_beta.values)
        axes[1, 0].set_title('Rolling Beta')
        axes[1, 0].grid(True)
        
        # 信息比率
        information_ratio = self.calculate_information_ratio()
        axes[1, 1].bar(['Information Ratio'], [information_ratio])
        axes[1, 1].set_title('Information Ratio')
        axes[1, 1].grid(True)
        
        plt.tight_layout()
        return fig
    
    def calculate_rolling_beta(self, window=252):
        """计算滚动贝塔"""
        rolling_beta = []
        
        for i in range(window, len(self.portfolio_returns)):
            portfolio_window = self.portfolio_returns.iloc[i-window:i]
            benchmark_window = self.benchmark_returns.iloc[i-window:i]
            
            covariance = np.cov(portfolio_window, benchmark_window)[0, 1]
            benchmark_variance = np.var(benchmark_window)
            beta = covariance / benchmark_variance
            
            rolling_beta.append(beta)
        
        return pd.Series(rolling_beta, index=self.portfolio_returns.index[window:])
    
    def calculate_information_ratio(self):
        """计算信息比率"""
        active_returns = self.portfolio_returns - self.benchmark_returns
        information_ratio = active_returns.mean() / active_returns.std() * np.sqrt(252)
        return information_ratio
```

### 6.3.2 统计指标展示

#### 综合统计报告

```python
class StatisticalReport:
    """统计报告生成器"""
    
    def __init__(self, portfolio_returns, benchmark_returns):
        self.portfolio_returns = portfolio_returns
        self.benchmark_returns = benchmark_returns
    
    def generate_summary_statistics(self):
        """生成汇总统计"""
        stats = {}
        
        # 收益率统计
        stats['Total Return'] = (1 + self.portfolio_returns).prod() - 1
        stats['Annualized Return'] = self.portfolio_returns.mean() * 252
        stats['Volatility'] = self.portfolio_returns.std() * np.sqrt(252)
        stats['Sharpe Ratio'] = self.calculate_sharpe_ratio()
        stats['Sortino Ratio'] = self.calculate_sortino_ratio()
        
        # 风险统计
        stats['Max Drawdown'] = self.calculate_max_drawdown()
        stats['VaR (95%)'] = self.calculate_var(0.05)
        stats['CVaR (95%)'] = self.calculate_cvar(0.05)
        
        # 相对表现
        stats['Information Ratio'] = self.calculate_information_ratio()
        stats['Beta'] = self.calculate_beta()
        stats['Alpha'] = self.calculate_alpha()
        
        return pd.Series(stats)
    
    def calculate_sharpe_ratio(self, risk_free_rate=0.02):
        """计算夏普比率"""
        excess_returns = self.portfolio_returns - risk_free_rate / 252
        sharpe_ratio = excess_returns.mean() / self.portfolio_returns.std() * np.sqrt(252)
        return sharpe_ratio
    
    def calculate_sortino_ratio(self, risk_free_rate=0.02):
        """计算索提诺比率"""
        excess_returns = self.portfolio_returns - risk_free_rate / 252
        downside_returns = excess_returns[excess_returns < 0]
        downside_deviation = downside_returns.std()
        
        if downside_deviation == 0:
            return np.inf
        
        sortino_ratio = excess_returns.mean() / downside_deviation * np.sqrt(252)
        return sortino_ratio
    
    def calculate_max_drawdown(self):
        """计算最大回撤"""
        cumulative_returns = (1 + self.portfolio_returns).cumprod()
        peak = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - peak) / peak
        max_drawdown = drawdown.min()
        return max_drawdown
    
    def calculate_var(self, confidence_level):
        """计算VaR"""
        var = np.percentile(self.portfolio_returns, confidence_level * 100)
        return var
    
    def calculate_cvar(self, confidence_level):
        """计算CVaR"""
        var = self.calculate_var(confidence_level)
        cvar = self.portfolio_returns[self.portfolio_returns <= var].mean()
        return cvar
    
    def calculate_information_ratio(self):
        """计算信息比率"""
        active_returns = self.portfolio_returns - self.benchmark_returns
        information_ratio = active_returns.mean() / active_returns.std() * np.sqrt(252)
        return information_ratio
    
    def calculate_beta(self):
        """计算贝塔系数"""
        covariance = np.cov(self.portfolio_returns, self.benchmark_returns)[0, 1]
        benchmark_variance = np.var(self.benchmark_returns)
        beta = covariance / benchmark_variance
        return beta
    
    def calculate_alpha(self, risk_free_rate=0.02):
        """计算阿尔法"""
        beta = self.calculate_beta()
        portfolio_return = self.portfolio_returns.mean() * 252
        benchmark_return = self.benchmark_returns.mean() * 252
        
        alpha = portfolio_return - (risk_free_rate + beta * (benchmark_return - risk_free_rate))
        return alpha
```

### 6.3.3 策略对比分析

#### 多策略对比

```python
class StrategyComparison:
    """策略对比分析"""
    
    def __init__(self, strategy_results):
        self.strategy_results = strategy_results
    
    def generate_comparison_table(self):
        """生成对比表格"""
        comparison_data = []
        
        for strategy_name, results in self.strategy_results.items():
            returns = results['returns']
            
            # 计算关键指标
            annual_return = returns.mean() * 252
            volatility = returns.std() * np.sqrt(252)
            sharpe_ratio = self.calculate_sharpe_ratio(returns)
            max_drawdown = self.calculate_max_drawdown(returns)
            
            comparison_data.append({
                'Strategy': strategy_name,
                'Annual Return': annual_return,
                'Volatility': volatility,
                'Sharpe Ratio': sharpe_ratio,
                'Max Drawdown': max_drawdown
            })
        
        return pd.DataFrame(comparison_data)
    
    def generate_comparison_chart(self):
        """生成对比图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 累积收益率对比
        for strategy_name, results in self.strategy_results.items():
            cumulative_returns = (1 + results['returns']).cumprod()
            axes[0, 0].plot(cumulative_returns.index, cumulative_returns.values, label=strategy_name)
        
        axes[0, 0].set_title('Cumulative Returns Comparison')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # 风险收益散点图
        for strategy_name, results in self.strategy_results.items():
            returns = results['returns']
            vol = returns.std() * np.sqrt(252)
            ret = returns.mean() * 252
            axes[0, 1].scatter(vol, ret, label=strategy_name, s=100)
        
        axes[0, 1].set_xlabel('Volatility')
        axes[0, 1].set_ylabel('Return')
        axes[0, 1].set_title('Risk-Return Comparison')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
        
        # 回撤对比
        for strategy_name, results in self.strategy_results.items():
            drawdown = self.calculate_drawdown(results['returns'])
            axes[1, 0].plot(drawdown.index, drawdown.values, label=strategy_name)
        
        axes[1, 0].set_title('Drawdown Comparison')
        axes[1, 0].legend()
        axes[1, 0].grid(True)
        
        # 滚动夏普比率对比
        for strategy_name, results in self.strategy_results.items():
            rolling_sharpe = self.calculate_rolling_sharpe(results['returns'])
            axes[1, 1].plot(rolling_sharpe.index, rolling_sharpe.values, label=strategy_name)
        
        axes[1, 1].set_title('Rolling Sharpe Ratio Comparison')
        axes[1, 1].legend()
        axes[1, 1].grid(True)
        
        plt.tight_layout()
        return fig
    
    def calculate_sharpe_ratio(self, returns, risk_free_rate=0.02):
        """计算夏普比率"""
        excess_returns = returns - risk_free_rate / 252
        sharpe_ratio = excess_returns.mean() / returns.std() * np.sqrt(252)
        return sharpe_ratio
    
    def calculate_max_drawdown(self, returns):
        """计算最大回撤"""
        cumulative_returns = (1 + returns).cumprod()
        peak = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - peak) / peak
        max_drawdown = drawdown.min()
        return max_drawdown
    
    def calculate_drawdown(self, returns):
        """计算回撤"""
        cumulative_returns = (1 + returns).cumprod()
        peak = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - peak) / peak
        return drawdown
    
    def calculate_rolling_sharpe(self, returns, window=252):
        """计算滚动夏普比率"""
        rolling_mean = returns.rolling(window).mean()
        rolling_std = returns.rolling(window).std()
        rolling_sharpe = rolling_mean / rolling_std * np.sqrt(252)
        return rolling_sharpe
```

### 6.3.4 报告导出功能

#### 报告导出器

```python
import json
import yaml
from datetime import datetime

class ReportExporter:
    """报告导出器"""
    
    def __init__(self, backtest_results):
        self.results = backtest_results
    
    def export_to_json(self, filepath):
        """导出为JSON格式"""
        export_data = {
            'backtest_summary': self.generate_summary(),
            'performance_metrics': self.calculate_performance_metrics(),
            'risk_metrics': self.calculate_risk_metrics(),
            'export_time': datetime.now().isoformat()
        }
        
        with open(filepath, 'w') as f:
            json.dump(export_data, f, indent=2, default=str)
        
        print(f"报告已导出到: {filepath}")
    
    def export_to_yaml(self, filepath):
        """导出为YAML格式"""
        export_data = {
            'backtest_summary': self.generate_summary(),
            'performance_metrics': self.calculate_performance_metrics(),
            'risk_metrics': self.calculate_risk_metrics(),
            'export_time': datetime.now().isoformat()
        }
        
        with open(filepath, 'w') as f:
            yaml.dump(export_data, f, default_flow_style=False)
        
        print(f"报告已导出到: {filepath}")
    
    def export_to_html(self, filepath):
        """导出为HTML格式"""
        html_content = self.generate_html_report()
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"HTML报告已导出到: {filepath}")
    
    def generate_html_report(self):
        """生成HTML报告"""
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>回测报告</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .section { margin: 20px 0; }
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
            </style>
        </head>
        <body>
            <h1>量化投资回测报告</h1>
            <div class="section">
                <h2>回测摘要</h2>
                <p>生成时间: {export_time}</p>
                <p>回测期间: {start_time} 至 {end_time}</p>
            </div>
            <div class="section">
                <h2>绩效指标</h2>
                {performance_table}
            </div>
            <div class="section">
                <h2>风险指标</h2>
                {risk_table}
            </div>
        </body>
        </html>
        """
        
        # 生成表格内容
        performance_table = self.generate_performance_table_html()
        risk_table = self.generate_risk_table_html()
        
        return html_template.format(
            export_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            start_time=self.results.get('start_time', 'N/A'),
            end_time=self.results.get('end_time', 'N/A'),
            performance_table=performance_table,
            risk_table=risk_table
        )
    
    def generate_summary(self):
        """生成摘要"""
        return {
            'start_time': self.results.get('start_time'),
            'end_time': self.results.get('end_time'),
            'total_return': self.calculate_total_return(),
            'annualized_return': self.calculate_annualized_return(),
            'volatility': self.calculate_volatility(),
            'sharpe_ratio': self.calculate_sharpe_ratio()
        }
    
    def calculate_performance_metrics(self):
        """计算绩效指标"""
        returns = self.results['returns']
        
        return {
            'total_return': (1 + returns).prod() - 1,
            'annualized_return': returns.mean() * 252,
            'volatility': returns.std() * np.sqrt(252),
            'sharpe_ratio': self.calculate_sharpe_ratio(),
            'sortino_ratio': self.calculate_sortino_ratio(),
            'calmar_ratio': self.calculate_calmar_ratio()
        }
    
    def calculate_risk_metrics(self):
        """计算风险指标"""
        returns = self.results['returns']
        
        return {
            'max_drawdown': self.calculate_max_drawdown(),
            'var_95': self.calculate_var(0.05),
            'cvar_95': self.calculate_cvar(0.05),
            'var_99': self.calculate_var(0.01),
            'cvar_99': self.calculate_cvar(0.01)
        }
```

## 本章小结

本章详细介绍了Qlib回测系统的各个方面，包括：

1. **回测框架设计**：回测引擎架构、事件驱动机制、订单管理系统、风险控制模块
2. **回测分析**：收益率分析、风险指标计算、交易成本分析、绩效归因分析
3. **回测报告**：图形化报告、统计指标展示、策略对比分析、报告导出功能

## 课后练习

### 练习1：回测系统使用
1. 使用Qlib回测系统测试简单策略
2. 分析回测结果和性能指标
3. 生成回测报告

### 练习2：回测分析
1. 计算各种风险指标
2. 进行绩效归因分析
3. 分析交易成本影响

### 练习3：报告生成
1. 生成图形化回测报告
2. 进行多策略对比分析
3. 导出不同格式的报告

## 扩展阅读

1. **回测系统理论**
   - 《量化投资回测系统设计》
   - 《回测与绩效分析》

2. **风险分析**
   - 《风险管理与投资组合》
   - 《VaR模型与应用》

3. **绩效归因**
   - 《投资组合绩效归因》
   - 《因子分析与归因》 