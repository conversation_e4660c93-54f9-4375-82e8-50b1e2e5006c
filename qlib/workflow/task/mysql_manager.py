"""
基于MySQL的任务管理器
用于Qlib项目的任务调度、状态跟踪和结果管理
"""
import json
import uuid
import pymysql
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from sqlalchemy import create_engine, text, MetaData, Table, Column, String, Integer, DateTime, Text, JSON
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import SQLAlchemyError

from qlib.utils import get_module_logger

logger = get_module_logger("mysql_task_manager")


class MySQLTaskManager:
    """MySQL任务管理器"""
    
    def __init__(self, mysql_uri: str):
        """
        初始化MySQL任务管理器
        
        Args:
            mysql_uri: MySQL连接URI，格式: mysql+pymysql://user:password@host:port/database
        """
        self.mysql_uri = mysql_uri
        self.engine = create_engine(mysql_uri, echo=False)
        self.Session = sessionmaker(bind=self.engine)
        self.metadata = MetaData()
        
        # 定义表结构
        self._define_tables()
        # 初始化数据库表
        self._init_tables()
        
        logger.info(f"MySQL任务管理器初始化完成: {mysql_uri}")
    
    def _define_tables(self):
        """定义数据库表结构"""
        # 任务表
        self.tasks_table = Table(
            'tasks', self.metadata,
            Column('id', String(64), primary_key=True),
            Column('name', String(255), nullable=False),
            Column('status', String(20), default='PENDING'),  # PENDING, RUNNING, COMPLETED, FAILED
            Column('task_type', String(50), default='workflow'),  # workflow, model_training, backtest
            Column('config', JSON),
            Column('created_at', DateTime, default=datetime.utcnow),
            Column('updated_at', DateTime, default=datetime.utcnow, onupdate=datetime.utcnow),
            Column('started_at', DateTime),
            Column('completed_at', DateTime),
            Column('error_message', Text),
        )
        
        # 任务结果表
        self.task_results_table = Table(
            'task_results', self.metadata,
            Column('id', Integer, primary_key=True, autoincrement=True),
            Column('task_id', String(64), nullable=False),
            Column('result_type', String(50)),  # metrics, params, artifacts, logs
            Column('result_data', JSON),
            Column('created_at', DateTime, default=datetime.utcnow),
        )
        
        # 任务依赖表
        self.task_dependencies_table = Table(
            'task_dependencies', self.metadata,
            Column('id', Integer, primary_key=True, autoincrement=True),
            Column('task_id', String(64), nullable=False),
            Column('depends_on_task_id', String(64), nullable=False),
            Column('created_at', DateTime, default=datetime.utcnow),
        )
    
    def _init_tables(self):
        """初始化数据库表"""
        try:
            # 创建所有表
            self.metadata.create_all(self.engine)
            logger.info("MySQL任务管理表创建成功")
        except SQLAlchemyError as e:
            logger.error(f"创建MySQL任务管理表失败: {e}")
            raise
    
    def create_task(self, name: str, task_type: str = "workflow", 
                   config: Optional[Dict] = None, depends_on: Optional[List[str]] = None) -> str:
        """
        创建新任务
        
        Args:
            name: 任务名称
            task_type: 任务类型 (workflow, model_training, backtest)
            config: 任务配置
            depends_on: 依赖的任务ID列表
            
        Returns:
            str: 任务ID
        """
        task_id = str(uuid.uuid4())
        config = config or {}
        
        try:
            with self.engine.connect() as conn:
                # 插入任务记录
                conn.execute(
                    self.tasks_table.insert().values(
                        id=task_id,
                        name=name,
                        task_type=task_type,
                        status='PENDING',
                        config=config,
                        created_at=datetime.utcnow()
                    )
                )
                
                # 插入依赖关系
                if depends_on:
                    for dep_task_id in depends_on:
                        conn.execute(
                            self.task_dependencies_table.insert().values(
                                task_id=task_id,
                                depends_on_task_id=dep_task_id,
                                created_at=datetime.utcnow()
                            )
                        )
                
                conn.commit()
                logger.info(f"任务创建成功: {task_id} ({name})")
                return task_id
                
        except SQLAlchemyError as e:
            logger.error(f"创建任务失败: {e}")
            raise
    
    def update_task_status(self, task_id: str, status: str, 
                          error_message: Optional[str] = None) -> bool:
        """
        更新任务状态
        
        Args:
            task_id: 任务ID
            status: 新状态 (PENDING, RUNNING, COMPLETED, FAILED)
            error_message: 错误信息 (仅当状态为FAILED时)
            
        Returns:
            bool: 更新是否成功
        """
        try:
            with self.engine.connect() as conn:
                update_data = {
                    'status': status,
                    'updated_at': datetime.utcnow()
                }
                
                if status == 'RUNNING':
                    update_data['started_at'] = datetime.utcnow()
                elif status in ['COMPLETED', 'FAILED']:
                    update_data['completed_at'] = datetime.utcnow()
                    if error_message:
                        update_data['error_message'] = error_message
                
                result = conn.execute(
                    self.tasks_table.update()
                    .where(self.tasks_table.c.id == task_id)
                    .values(**update_data)
                )
                
                conn.commit()
                
                if result.rowcount > 0:
                    logger.info(f"任务状态更新成功: {task_id} -> {status}")
                    return True
                else:
                    logger.warning(f"任务不存在: {task_id}")
                    return False
                    
        except SQLAlchemyError as e:
            logger.error(f"更新任务状态失败: {e}")
            return False
    
    def get_task(self, task_id: str) -> Optional[Dict]:
        """
        获取任务信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 任务信息，如果不存在返回None
        """
        try:
            with self.engine.connect() as conn:
                result = conn.execute(
                    self.tasks_table.select().where(self.tasks_table.c.id == task_id)
                ).fetchone()
                
                if result:
                    return dict(result._mapping)
                return None
                
        except SQLAlchemyError as e:
            logger.error(f"获取任务信息失败: {e}")
            return None
    
    def list_tasks(self, status: Optional[str] = None, 
                  task_type: Optional[str] = None, limit: int = 100) -> List[Dict]:
        """
        列出任务
        
        Args:
            status: 过滤状态
            task_type: 过滤任务类型
            limit: 限制数量
            
        Returns:
            List[Dict]: 任务列表
        """
        try:
            with self.engine.connect() as conn:
                query = self.tasks_table.select()
                
                if status:
                    query = query.where(self.tasks_table.c.status == status)
                if task_type:
                    query = query.where(self.tasks_table.c.task_type == task_type)
                
                query = query.order_by(self.tasks_table.c.created_at.desc()).limit(limit)
                
                results = conn.execute(query).fetchall()
                return [dict(row._mapping) for row in results]
                
        except SQLAlchemyError as e:
            logger.error(f"列出任务失败: {e}")
            return []
    
    def add_task_result(self, task_id: str, result_type: str, result_data: Any) -> bool:
        """
        添加任务结果
        
        Args:
            task_id: 任务ID
            result_type: 结果类型 (metrics, params, artifacts, logs)
            result_data: 结果数据
            
        Returns:
            bool: 添加是否成功
        """
        try:
            with self.engine.connect() as conn:
                conn.execute(
                    self.task_results_table.insert().values(
                        task_id=task_id,
                        result_type=result_type,
                        result_data=result_data,
                        created_at=datetime.utcnow()
                    )
                )
                conn.commit()
                logger.info(f"任务结果添加成功: {task_id} ({result_type})")
                return True
                
        except SQLAlchemyError as e:
            logger.error(f"添加任务结果失败: {e}")
            return False
    
    def get_task_results(self, task_id: str, result_type: Optional[str] = None) -> List[Dict]:
        """
        获取任务结果
        
        Args:
            task_id: 任务ID
            result_type: 结果类型过滤
            
        Returns:
            List[Dict]: 任务结果列表
        """
        try:
            with self.engine.connect() as conn:
                query = self.task_results_table.select().where(
                    self.task_results_table.c.task_id == task_id
                )
                
                if result_type:
                    query = query.where(self.task_results_table.c.result_type == result_type)
                
                query = query.order_by(self.task_results_table.c.created_at.desc())
                
                results = conn.execute(query).fetchall()
                return [dict(row._mapping) for row in results]
                
        except SQLAlchemyError as e:
            logger.error(f"获取任务结果失败: {e}")
            return []
    
    def get_task_dependencies(self, task_id: str) -> List[str]:
        """
        获取任务依赖
        
        Args:
            task_id: 任务ID
            
        Returns:
            List[str]: 依赖的任务ID列表
        """
        try:
            with self.engine.connect() as conn:
                results = conn.execute(
                    self.task_dependencies_table.select().where(
                        self.task_dependencies_table.c.task_id == task_id
                    )
                ).fetchall()
                
                return [row.depends_on_task_id for row in results]
                
        except SQLAlchemyError as e:
            logger.error(f"获取任务依赖失败: {e}")
            return []
    
    def check_dependencies_completed(self, task_id: str) -> bool:
        """
        检查任务依赖是否都已完成
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 所有依赖是否都已完成
        """
        dependencies = self.get_task_dependencies(task_id)
        
        if not dependencies:
            return True
        
        try:
            with self.engine.connect() as conn:
                for dep_task_id in dependencies:
                    result = conn.execute(
                        self.tasks_table.select().where(self.tasks_table.c.id == dep_task_id)
                    ).fetchone()
                    
                    if not result or result.status != 'COMPLETED':
                        return False
                
                return True
                
        except SQLAlchemyError as e:
            logger.error(f"检查任务依赖失败: {e}")
            return False
    
    def get_ready_tasks(self, limit: int = 10) -> List[Dict]:
        """
        获取准备运行的任务（状态为PENDING且依赖已完成）
        
        Args:
            limit: 限制数量
            
        Returns:
            List[Dict]: 准备运行的任务列表
        """
        pending_tasks = self.list_tasks(status='PENDING', limit=limit)
        ready_tasks = []
        
        for task in pending_tasks:
            if self.check_dependencies_completed(task['id']):
                ready_tasks.append(task)
        
        return ready_tasks
    
    def cleanup_old_tasks(self, days: int = 30) -> int:
        """
        清理旧任务（删除指定天数前的已完成任务）
        
        Args:
            days: 保留天数
            
        Returns:
            int: 删除的任务数量
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            with self.engine.connect() as conn:
                # 先删除任务结果
                conn.execute(
                    self.task_results_table.delete().where(
                        self.task_results_table.c.task_id.in_(
                            self.tasks_table.select().where(
                                (self.tasks_table.c.status.in_(['COMPLETED', 'FAILED'])) &
                                (self.tasks_table.c.completed_at < cutoff_date)
                            ).with_only_columns([self.tasks_table.c.id])
                        )
                    )
                )
                
                # 删除任务依赖
                conn.execute(
                    self.task_dependencies_table.delete().where(
                        self.task_dependencies_table.c.task_id.in_(
                            self.tasks_table.select().where(
                                (self.tasks_table.c.status.in_(['COMPLETED', 'FAILED'])) &
                                (self.tasks_table.c.completed_at < cutoff_date)
                            ).with_only_columns([self.tasks_table.c.id])
                        )
                    )
                )
                
                # 删除任务
                result = conn.execute(
                    self.tasks_table.delete().where(
                        (self.tasks_table.c.status.in_(['COMPLETED', 'FAILED'])) &
                        (self.tasks_table.c.completed_at < cutoff_date)
                    )
                )
                
                conn.commit()
                deleted_count = result.rowcount
                logger.info(f"清理旧任务完成，删除 {deleted_count} 个任务")
                return deleted_count
                
        except SQLAlchemyError as e:
            logger.error(f"清理旧任务失败: {e}")
            return 0
    
    def get_task_statistics(self) -> Dict[str, Any]:
        """
        获取任务统计信息
        
        Returns:
            Dict: 统计信息
        """
        try:
            with self.engine.connect() as conn:
                # 按状态统计
                status_stats = {}
                for status in ['PENDING', 'RUNNING', 'COMPLETED', 'FAILED']:
                    count = conn.execute(
                        text("SELECT COUNT(*) FROM tasks WHERE status = :status"),
                        {"status": status}
                    ).scalar()
                    status_stats[status.lower()] = count
                
                # 按类型统计
                type_stats = {}
                type_results = conn.execute(
                    text("SELECT task_type, COUNT(*) as count FROM tasks GROUP BY task_type")
                ).fetchall()
                
                for row in type_results:
                    type_stats[row.task_type] = row.count
                
                # 总体统计
                total_tasks = conn.execute(text("SELECT COUNT(*) FROM tasks")).scalar()
                total_results = conn.execute(text("SELECT COUNT(*) FROM task_results")).scalar()
                
                return {
                    'total_tasks': total_tasks,
                    'total_results': total_results,
                    'status_distribution': status_stats,
                    'type_distribution': type_stats,
                    'timestamp': datetime.utcnow().isoformat()
                }
                
        except SQLAlchemyError as e:
            logger.error(f"获取任务统计失败: {e}")
            return {}


def get_mysql_task_manager(mysql_uri: str = None) -> MySQLTaskManager:
    """
    获取MySQL任务管理器实例
    
    Args:
        mysql_uri: MySQL连接URI，如果为None则从配置中获取
        
    Returns:
        MySQLTaskManager: 任务管理器实例
    """
    if mysql_uri is None:
        from qlib.config import C
        try:
            mysql_uri = C["mysql_task"]["task_url"]
        except KeyError:
            logger.error("请配置 `C['mysql_task']['task_url']` 后再使用MySQL任务管理器")
            raise
    
    return MySQLTaskManager(mysql_uri) 