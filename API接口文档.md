# 多用户量化投资平台 API 接口文档

## 1. 概述

### 1.1 基础信息
- **API版本**: v1
- **基础URL**: `https://api.qlib-platform.com/api/v1`
- **认证方式**: JWT Bearer <PERSON>
- **数据格式**: JSON
- **字符编码**: UTF-8

### 1.2 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 1.3 错误码说明
| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 429 | 请求频率限制 |
| 500 | 服务器内部错误 |

## 2. 认证接口

### 2.1 用户注册
**接口地址**: `POST /auth/register`

**请求参数**:
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "confirm_password": "string"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "user_id": 123,
    "username": "test_user",
    "email": "<EMAIL>",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 2.2 用户登录
**接口地址**: `POST /auth/login`

**请求参数**:
```json
{
  "username": "string",
  "password": "string"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 1800,
    "user": {
      "id": 123,
      "username": "test_user",
      "email": "<EMAIL>"
    }
  }
}
```

### 2.3 用户登出
**接口地址**: `POST /auth/logout`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "code": 200,
  "message": "登出成功",
  "data": null
}
```

### 2.4 刷新Token
**接口地址**: `POST /auth/refresh`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "code": 200,
  "message": "刷新成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 1800
  }
}
```

## 3. 用户管理接口

### 3.1 获取用户信息
**接口地址**: `GET /users/profile`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 123,
    "username": "test_user",
    "email": "<EMAIL>",
    "is_active": true,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3.2 更新用户信息
**接口地址**: `PUT /users/profile`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "email": "<EMAIL>",
  "nickname": "新昵称"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 123,
    "username": "test_user",
    "email": "<EMAIL>",
    "nickname": "新昵称",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3.3 获取用户配额
**接口地址**: `GET /users/quota`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "data_usage": {
      "current": 1024,
      "max": 10000,
      "unit": "MB"
    },
    "compute_time": {
      "current": 3600,
      "max": 86400,
      "unit": "seconds"
    },
    "storage": {
      "current": 512,
      "max": 5000,
      "unit": "MB"
    }
  }
}
```

## 4. 数据管理接口

### 4.1 获取股票列表
**接口地址**: `GET /data/instruments`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `market`: 市场类型 (cn/us)
- `type`: 证券类型 (stock/fund/bond)
- `page`: 页码 (默认1)
- `size`: 每页数量 (默认20)

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "instruments": [
      {
        "code": "000001.SZ",
        "name": "平安银行",
        "market": "cn",
        "type": "stock",
        "status": "active"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 1000,
      "pages": 50
    }
  }
}
```

### 4.2 获取数据字段
**接口地址**: `GET /data/features`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `category`: 字段类别 (price/volume/indicator)

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "features": [
      {
        "name": "$close",
        "description": "收盘价",
        "category": "price",
        "type": "float"
      },
      {
        "name": "$volume",
        "description": "成交量",
        "category": "volume",
        "type": "float"
      }
    ]
  }
}
```

### 4.3 查询历史数据
**接口地址**: `POST /data/query`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "instruments": ["000001.SZ", "000002.SZ"],
  "fields": ["$close", "$volume", "$high", "$low"],
  "start_time": "2024-01-01",
  "end_time": "2024-01-31",
  "freq": "1d"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "data": [
      {
        "instrument": "000001.SZ",
        "date": "2024-01-01",
        "$close": 10.5,
        "$volume": 1000000,
        "$high": 10.8,
        "$low": 10.2
      }
    ],
    "metadata": {
      "total_records": 62,
      "start_date": "2024-01-01",
      "end_date": "2024-01-31"
    }
  }
}
```

### 4.4 获取交易日历
**接口地址**: `GET /data/calendar`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `start_time`: 开始日期
- `end_time`: 结束日期
- `market`: 市场类型

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "calendar": [
      "2024-01-01",
      "2024-01-02",
      "2024-01-03"
    ]
  }
}
```

## 5. 策略管理接口

### 5.1 获取策略列表
**接口地址**: `GET /strategies`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `page`: 页码
- `size`: 每页数量
- `status`: 策略状态
- `type`: 策略类型

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "strategies": [
      {
        "id": 1,
        "name": "双均线策略",
        "description": "基于双均线的趋势跟踪策略",
        "type": "alpha",
        "status": "active",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 10,
      "pages": 1
    }
  }
}
```

### 5.2 创建策略
**接口地址**: `POST /strategies`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "name": "双均线策略",
  "description": "基于双均线的趋势跟踪策略",
  "type": "alpha",
  "code": "def strategy(context):\n    # 策略代码\n    pass",
  "config": {
    "universe": ["000001.SZ", "000002.SZ"],
    "benchmark": "000300.SH",
    "start_time": "2024-01-01",
    "end_time": "2024-12-31"
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "name": "双均线策略",
    "description": "基于双均线的趋势跟踪策略",
    "type": "alpha",
    "status": "draft",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 5.3 获取策略详情
**接口地址**: `GET /strategies/{strategy_id}`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "name": "双均线策略",
    "description": "基于双均线的趋势跟踪策略",
    "type": "alpha",
    "status": "active",
    "code": "def strategy(context):\n    # 策略代码\n    pass",
    "config": {
      "universe": ["000001.SZ", "000002.SZ"],
      "benchmark": "000300.SH",
      "start_time": "2024-01-01",
      "end_time": "2024-12-31"
    },
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 5.4 更新策略
**接口地址**: `PUT /strategies/{strategy_id}`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "name": "双均线策略V2",
  "description": "优化后的双均线策略",
  "code": "def strategy(context):\n    # 优化后的策略代码\n    pass",
  "config": {
    "universe": ["000001.SZ", "000002.SZ", "000003.SZ"],
    "benchmark": "000300.SH",
    "start_time": "2024-01-01",
    "end_time": "2024-12-31"
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 1,
    "name": "双均线策略V2",
    "description": "优化后的双均线策略",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 5.5 删除策略
**接口地址**: `DELETE /strategies/{strategy_id}`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

### 5.6 执行策略回测
**接口地址**: `POST /strategies/{strategy_id}/backtest`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "start_time": "2024-01-01",
  "end_time": "2024-12-31",
  "benchmark": "000300.SH",
  "initial_capital": 1000000,
  "commission": 0.0003,
  "slippage": 0.0001
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "回测任务已提交",
  "data": {
    "backtest_id": "bt_123456",
    "status": "running",
    "estimated_completion": "2024-01-01T01:00:00Z"
  }
}
```

### 5.7 获取回测结果
**接口地址**: `GET /strategies/{strategy_id}/backtest/{backtest_id}`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "backtest_id": "bt_123456",
    "status": "completed",
    "start_time": "2024-01-01",
    "end_time": "2024-12-31",
    "results": {
      "total_return": 0.15,
      "annualized_return": 0.12,
      "sharpe_ratio": 1.2,
      "max_drawdown": -0.08,
      "win_rate": 0.65
    },
    "returns": [
      {
        "date": "2024-01-01",
        "strategy_return": 0.001,
        "benchmark_return": 0.0008
      }
    ],
    "positions": [
      {
        "date": "2024-01-01",
        "instrument": "000001.SZ",
        "weight": 0.1,
        "quantity": 1000
      }
    ]
  }
}
```

## 6. 模型管理接口

### 6.1 获取模型列表
**接口地址**: `GET /models`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `page`: 页码
- `size`: 每页数量
- `status`: 模型状态

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "models": [
      {
        "id": 1,
        "name": "LightGBM模型",
        "type": "lightgbm",
        "status": "trained",
        "accuracy": 0.75,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 5,
      "pages": 1
    }
  }
}
```

### 6.2 训练模型
**接口地址**: `POST /models/train`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "name": "LightGBM模型",
  "type": "lightgbm",
  "config": {
    "dataset": {
      "instruments": ["000001.SZ", "000002.SZ"],
      "features": ["$close", "$volume", "$high", "$low"],
      "start_time": "2020-01-01",
      "end_time": "2023-12-31"
    },
    "model_params": {
      "learning_rate": 0.1,
      "num_leaves": 31,
      "max_depth": 6
    },
    "train_params": {
      "test_size": 0.2,
      "random_state": 42
    }
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "模型训练任务已提交",
  "data": {
    "model_id": "model_123456",
    "status": "training",
    "estimated_completion": "2024-01-01T02:00:00Z"
  }
}
```

### 6.3 获取模型详情
**接口地址**: `GET /models/{model_id}`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "model_123456",
    "name": "LightGBM模型",
    "type": "lightgbm",
    "status": "trained",
    "config": {
      "dataset": {
        "instruments": ["000001.SZ", "000002.SZ"],
        "features": ["$close", "$volume", "$high", "$low"],
        "start_time": "2020-01-01",
        "end_time": "2023-12-31"
      },
      "model_params": {
        "learning_rate": 0.1,
        "num_leaves": 31,
        "max_depth": 6
      }
    },
    "metrics": {
      "accuracy": 0.75,
      "precision": 0.72,
      "recall": 0.78,
      "f1_score": 0.75
    },
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T02:00:00Z"
  }
}
```

### 6.4 模型预测
**接口地址**: `POST /models/{model_id}/predict`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "instruments": ["000001.SZ", "000002.SZ"],
  "date": "2024-01-01"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "predictions": [
      {
        "instrument": "000001.SZ",
        "date": "2024-01-01",
        "prediction": 0.75,
        "confidence": 0.85
      },
      {
        "instrument": "000002.SZ",
        "date": "2024-01-01",
        "prediction": 0.65,
        "confidence": 0.78
      }
    ]
  }
}
```

## 7. 交易管理接口

### 7.1 获取订单列表
**接口地址**: `GET /trading/orders`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `page`: 页码
- `size`: 每页数量
- `status`: 订单状态
- `start_date`: 开始日期
- `end_date`: 结束日期

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "orders": [
      {
        "id": "order_123456",
        "instrument": "000001.SZ",
        "side": "buy",
        "quantity": 1000,
        "price": 10.5,
        "status": "filled",
        "filled_quantity": 1000,
        "filled_price": 10.52,
        "created_at": "2024-01-01T09:30:00Z",
        "filled_at": "2024-01-01T09:31:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 50,
      "pages": 3
    }
  }
}
```

### 7.2 创建订单
**接口地址**: `POST /trading/orders`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "instrument": "000001.SZ",
  "side": "buy",
  "quantity": 1000,
  "price": 10.5,
  "order_type": "limit",
  "strategy_id": 1
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "订单创建成功",
  "data": {
    "order_id": "order_123456",
    "instrument": "000001.SZ",
    "side": "buy",
    "quantity": 1000,
    "price": 10.5,
    "status": "pending",
    "created_at": "2024-01-01T09:30:00Z"
  }
}
```

### 7.3 获取持仓信息
**接口地址**: `GET /trading/positions`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "positions": [
      {
        "instrument": "000001.SZ",
        "quantity": 1000,
        "avg_price": 10.52,
        "current_price": 10.8,
        "market_value": 10800,
        "unrealized_pnl": 280,
        "unrealized_pnl_pct": 0.026
      }
    ],
    "summary": {
      "total_market_value": 10800,
      "total_unrealized_pnl": 280,
      "total_unrealized_pnl_pct": 0.026
    }
  }
}
```

### 7.4 获取投资组合
**接口地址**: `GET /trading/portfolio`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "portfolio_id": "portfolio_123",
    "name": "我的投资组合",
    "total_value": 100000,
    "cash": 20000,
    "positions_value": 80000,
    "daily_return": 0.001,
    "total_return": 0.05,
    "sharpe_ratio": 1.2,
    "max_drawdown": -0.08,
    "positions": [
      {
        "instrument": "000001.SZ",
        "weight": 0.1,
        "quantity": 1000,
        "market_value": 10000
      }
    ],
    "performance": [
      {
        "date": "2024-01-01",
        "value": 100000,
        "return": 0.001
      }
    ]
  }
}
```

## 8. 系统管理接口

### 8.1 获取系统状态
**接口地址**: `GET /system/status`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "system": {
      "status": "healthy",
      "uptime": "7 days",
      "version": "1.0.0"
    },
    "services": {
      "database": "healthy",
      "redis": "healthy",
      "celery": "healthy"
    },
    "resources": {
      "cpu_usage": 0.3,
      "memory_usage": 0.5,
      "disk_usage": 0.4
    }
  }
}
```

### 8.2 获取任务状态
**接口地址**: `GET /system/tasks`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `status`: 任务状态
- `type`: 任务类型

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "tasks": [
      {
        "id": "task_123456",
        "type": "backtest",
        "status": "running",
        "progress": 0.6,
        "created_at": "2024-01-01T09:00:00Z",
        "estimated_completion": "2024-01-01T10:00:00Z"
      }
    ]
  }
}
```

## 9. WebSocket接口

### 9.1 实时数据订阅
**连接地址**: `wss://api.qlib-platform.com/ws/realtime`

**认证**: 通过URL参数传递token
```
wss://api.qlib-platform.com/ws/realtime?token={jwt_token}
```

**订阅消息格式**:
```json
{
  "action": "subscribe",
  "channel": "price",
  "instruments": ["000001.SZ", "000002.SZ"]
}
```

**推送消息格式**:
```json
{
  "channel": "price",
  "data": {
    "instrument": "000001.SZ",
    "timestamp": "2024-01-01T09:30:00Z",
    "price": 10.5,
    "volume": 1000000,
    "change": 0.02,
    "change_pct": 0.0019
  }
}
```

### 9.2 任务状态推送
**订阅消息格式**:
```json
{
  "action": "subscribe",
  "channel": "task_status",
  "task_id": "task_123456"
}
```

**推送消息格式**:
```json
{
  "channel": "task_status",
  "data": {
    "task_id": "task_123456",
    "status": "running",
    "progress": 0.8,
    "message": "正在执行回测..."
  }
}
```

## 10. 文件上传接口

### 10.1 上传策略文件
**接口地址**: `POST /upload/strategy`

**请求头**: 
- `Authorization: Bearer {token}`
- `Content-Type: multipart/form-data`

**请求参数**:
- `file`: 策略文件 (Python文件)
- `name`: 策略名称
- `description`: 策略描述

**响应示例**:
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "file_id": "file_123456",
    "filename": "strategy.py",
    "size": 1024,
    "uploaded_at": "2024-01-01T00:00:00Z"
  }
}
```

### 10.2 上传数据文件
**接口地址**: `POST /upload/data`

**请求头**: 
- `Authorization: Bearer {token}`
- `Content-Type: multipart/form-data`

**请求参数**:
- `file`: 数据文件 (CSV/Excel)
- `type`: 数据类型 (price/volume/indicator)
- `market`: 市场类型

**响应示例**:
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "file_id": "file_123456",
    "filename": "data.csv",
    "size": 2048,
    "records": 1000,
    "uploaded_at": "2024-01-01T00:00:00Z"
  }
}
```

## 11. 接口限流说明

### 11.1 限流规则
- **认证接口**: 100次/小时
- **数据查询**: 1000次/小时
- **策略操作**: 100次/小时
- **模型训练**: 10次/小时
- **交易操作**: 1000次/小时

### 11.2 限流响应
当请求超过限流时，返回429状态码：
```json
{
  "code": 429,
  "message": "请求频率超限",
  "data": {
    "retry_after": 3600,
    "limit": 1000,
    "remaining": 0
  }
}
```

## 12. 错误处理

### 12.1 参数验证错误
```json
{
  "code": 400,
  "message": "参数验证失败",
  "data": {
    "errors": [
      {
        "field": "username",
        "message": "用户名不能为空"
      }
    ]
  }
}
```

### 12.2 权限不足错误
```json
{
  "code": 403,
  "message": "权限不足",
  "data": {
    "required_permission": "strategy:write",
    "current_permission": "strategy:read"
  }
}
```

### 12.3 资源不存在错误
```json
{
  "code": 404,
  "message": "资源不存在",
  "data": {
    "resource_type": "strategy",
    "resource_id": "123"
  }
}
```

## 13. 接口文档更新

本文档会随着API的更新而更新，请定期查看最新版本。如有疑问，请联系技术支持团队。 