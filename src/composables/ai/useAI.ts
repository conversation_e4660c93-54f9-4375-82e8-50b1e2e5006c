import { ref, reactive, computed, onMounted, onUnmounted, readonly } from 'vue'
import { 
  AIInferenceRequest,
  AIInferenceResponse,
  AIModelConfig,
  AIModelPerformance,
  StockAIAnalysis,
  AIRecommendation,
  MarketSentiment
} from '@/types/ai'
import { aiService } from '@/services/ai/AIService'
import { stockAnalyzer } from '@/services/ai/StockAnalyzer'

/**
 * AI服务通用Hook
 */
export function useAI() {
  const loading = ref(false)
  const error = ref<string | null>(null)
  const models = ref<AIModelConfig[]>([])
  const performance = reactive<Record<string, AIModelPerformance>>({})

  // 加载可用模型
  const loadModels = async () => {
    try {
      loading.value = true
      error.value = null
      models.value = await aiService.listModels()
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to load models'
      console.error('加载AI模型失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 执行推理
  const inference = async <T = any>(request: AIInferenceRequest): Promise<AIInferenceResponse<T> | null> => {
    try {
      loading.value = true
      error.value = null
      const response = await aiService.inference<T>(request)
      return response
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Inference failed'
      console.error('AI推理失败:', err)
      return null
    } finally {
      loading.value = false
    }
  }

  // 批量推理
  const batchInference = async (requests: AIInferenceRequest[]): Promise<AIInferenceResponse[]> => {
    try {
      loading.value = true
      error.value = null
      return await aiService.batchInference(requests)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Batch inference failed'
      console.error('批量推理失败:', err)
      return []
    } finally {
      loading.value = false
    }
  }

  // 获取模型性能
  const getModelPerformance = async (modelId: string) => {
    try {
      const perf = await aiService.getPerformance(modelId)
      performance[modelId] = perf
      return perf
    } catch (err) {
      console.error(`获取模型${modelId}性能失败:`, err)
      return null
    }
  }

  // 计算属性
  const isReady = computed(() => models.value.length > 0 && !loading.value)
  const hasError = computed(() => error.value !== null)

  // 初始化
  onMounted(() => {
    loadModels()
  })

  return {
    // 状态
    loading: readonly(loading),
    error: readonly(error),
    models: readonly(models),
    performance: readonly(performance),
    
    // 计算属性
    isReady,
    hasError,
    
    // 方法
    loadModels,
    inference,
    batchInference,
    getModelPerformance
  }
}

/**
 * 股票AI分析Hook
 */
export function useStockAI() {
  const loading = ref(false)
  const error = ref<string | null>(null)
  const analysisResults = reactive<Record<string, StockAIAnalysis>>({})
  const marketSentiment = ref<MarketSentiment | null>(null)

  // 分析单只股票
  const analyzeStock = async (symbol: string, options: any = {}): Promise<StockAIAnalysis | null> => {
    try {
      loading.value = true
      error.value = null
      
      const analysis = await stockAnalyzer.analyzeStock(symbol, options)
      analysisResults[symbol] = analysis
      
      return analysis
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Stock analysis failed'
      console.error(`股票分析失败 ${symbol}:`, err)
      return null
    } finally {
      loading.value = false
    }
  }

  // 批量分析股票
  const batchAnalyzeStocks = async (symbols: string[], options: any = {}): Promise<StockAIAnalysis[]> => {
    try {
      loading.value = true
      error.value = null
      
      const results = await stockAnalyzer.batchAnalyzeStocks(symbols, options)
      
      // 更新缓存
      results.forEach(result => {
        analysisResults[result.symbol] = result
      })
      
      return results
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Batch analysis failed'
      console.error('批量股票分析失败:', err)
      return []
    } finally {
      loading.value = false
    }
  }

  // 预测股价
  const predictPrice = async (symbol: string, timeframe: string): Promise<any> => {
    try {
      loading.value = true
      error.value = null
      return await stockAnalyzer.predictPrice(symbol, timeframe)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Price prediction failed'
      console.error(`价格预测失败 ${symbol}:`, err)
      return null
    } finally {
      loading.value = false
    }
  }

  // 风险评估
  const assessRisk = async (portfolio: any[], options: any = {}): Promise<any> => {
    try {
      loading.value = true
      error.value = null
      return await stockAnalyzer.assessRisk(portfolio, options)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Risk assessment failed'
      console.error('风险评估失败:', err)
      return null
    } finally {
      loading.value = false
    }
  }

  // 获取市场情绪
  const getMarketSentiment = async (options: any = {}): Promise<MarketSentiment | null> => {
    try {
      loading.value = true
      error.value = null
      
      const sentiment = await stockAnalyzer.getMarketSentiment(options)
      marketSentiment.value = sentiment
      
      return sentiment
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Market sentiment analysis failed'
      console.error('市场情绪分析失败:', err)
      return null
    } finally {
      loading.value = false
    }
  }

  // 异常检测
  const detectAnomalies = async (data: any[]): Promise<any[]> => {
    try {
      loading.value = true
      error.value = null
      return await stockAnalyzer.detectAnomalies(data)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Anomaly detection failed'
      console.error('异常检测失败:', err)
      return []
    } finally {
      loading.value = false
    }
  }

  // 获取股票分析结果
  const getAnalysis = (symbol: string): StockAIAnalysis | null => {
    return analysisResults[symbol] || null
  }

  // 获取所有分析结果
  const getAllAnalyses = (): StockAIAnalysis[] => {
    return Object.values(analysisResults)
  }

  // 计算属性
  const hasAnalyses = computed(() => Object.keys(analysisResults).length > 0)
  const topStocks = computed(() => {
    return getAllAnalyses()
      .sort((a, b) => b.score - a.score)
      .slice(0, 10)
  })

  return {
    // 状态
    loading: readonly(loading),
    error: readonly(error),
    analysisResults: readonly(analysisResults),
    marketSentiment: readonly(marketSentiment),
    
    // 计算属性
    hasAnalyses,
    topStocks,
    
    // 方法
    analyzeStock,
    batchAnalyzeStocks,
    predictPrice,
    assessRisk,
    getMarketSentiment,
    detectAnomalies,
    getAnalysis,
    getAllAnalyses
  }
}

/**
 * AI推荐系统Hook
 */
export function useAIRecommendation() {
  const loading = ref(false)
  const error = ref<string | null>(null)
  const recommendations = ref<AIRecommendation[]>([])
  const personalizedRecs = ref<AIRecommendation[]>([])

  // 获取股票推荐
  const getStockRecommendations = async (criteria: any = {}): Promise<AIRecommendation[]> => {
    try {
      loading.value = true
      error.value = null
      
      // 模拟AI推荐逻辑
      const mockRecommendations: AIRecommendation[] = [
        {
          id: `rec_${Date.now()}_1`,
          type: 'stock',
          title: '科技股推荐：AAPL',
          description: 'Apple公司基本面强劲，技术指标积极，建议买入',
          confidence: 0.85,
          priority: 'high',
          category: 'technology',
          target: {
            symbol: 'AAPL',
            action: 'buy'
          },
          reasoning: {
            factors: ['强劲财务表现', '创新产品线', '市场领导地位'],
            analysis: 'Apple公司在多个产品线上保持领先地位，财务状况健康，适合长期投资',
            risks: ['市场竞争加剧', '供应链风险'],
            opportunities: ['新兴市场扩张', '服务业务增长']
          },
          metrics: {
            expected_return: 0.15,
            risk_level: 0.3,
            time_horizon: '6-12个月',
            success_probability: 0.75
          },
          timestamp: Date.now()
        },
        {
          id: `rec_${Date.now()}_2`,
          type: 'portfolio',
          title: '平衡型投资组合',
          description: '科技股与价值股的平衡配置，适合稳健投资者',
          confidence: 0.78,
          priority: 'medium',
          category: 'portfolio',
          target: {
            symbols: ['AAPL', 'MSFT', 'JNJ', 'PG'],
            action: 'rebalance'
          },
          reasoning: {
            factors: ['行业分散', '风险控制', '稳定收益'],
            analysis: '通过科技股和消费股的组合，实现风险与收益的平衡',
            risks: ['市场系统性风险'],
            opportunities: ['长期稳定增长']
          },
          metrics: {
            expected_return: 0.12,
            risk_level: 0.25,
            time_horizon: '12个月以上',
            success_probability: 0.8
          },
          timestamp: Date.now()
        }
      ]
      
      recommendations.value = mockRecommendations
      return mockRecommendations
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to get recommendations'
      console.error('获取推荐失败:', err)
      return []
    } finally {
      loading.value = false
    }
  }

  // 获取个性化推荐
  const getPersonalizedRecommendations = async (userProfile: any): Promise<AIRecommendation[]> => {
    try {
      loading.value = true
      error.value = null
      
      // 基于用户画像生成个性化推荐
      const recs = await generatePersonalizedRecs(userProfile)
      personalizedRecs.value = recs
      
      return recs
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to get personalized recommendations'
      console.error('获取个性化推荐失败:', err)
      return []
    } finally {
      loading.value = false
    }
  }

  // 生成个性化推荐
  const generatePersonalizedRecs = async (userProfile: any): Promise<AIRecommendation[]> => {
    // 模拟个性化推荐算法
    const baseRecs = recommendations.value
    
    return baseRecs.map(rec => ({
      ...rec,
      id: `personalized_${rec.id}`,
      confidence: rec.confidence * (userProfile.risk_tolerance || 0.8),
      priority: userProfile.investment_style === 'aggressive' ? 'high' : rec.priority
    }))
  }

  // 评价推荐
  const rateRecommendation = async (recId: string, rating: number): Promise<void> => {
    try {
      // 提交用户评价，用于改进推荐算法
      console.log(`用户对推荐 ${recId} 评分: ${rating}`)
      
      // 更新推荐的用户反馈
      const rec = recommendations.value.find(r => r.id === recId)
      if (rec) {
        // 更新推荐质量指标
        rec.confidence = (rec.confidence + rating / 5) / 2
      }
    } catch (err) {
      console.error('提交评价失败:', err)
    }
  }

  // 计算属性
  const highPriorityRecs = computed(() => 
    recommendations.value.filter(rec => rec.priority === 'high')
  )
  
  const stockRecs = computed(() => 
    recommendations.value.filter(rec => rec.type === 'stock')
  )
  
  const portfolioRecs = computed(() => 
    recommendations.value.filter(rec => rec.type === 'portfolio')
  )

  return {
    // 状态
    loading: readonly(loading),
    error: readonly(error),
    recommendations: readonly(recommendations),
    personalizedRecs: readonly(personalizedRecs),
    
    // 计算属性
    highPriorityRecs,
    stockRecs,
    portfolioRecs,
    
    // 方法
    getStockRecommendations,
    getPersonalizedRecommendations,
    rateRecommendation
  }
}

/**
 * AI性能监控Hook
 */
export function useAIMonitoring() {
  const loading = ref(false)
  const error = ref<string | null>(null)
  const performance = reactive<Record<string, AIModelPerformance>>({})
  const alerts = ref<any[]>([])
  
  let monitoringInterval: number | null = null

  // 开始监控
  const startMonitoring = (interval: number = 60000) => {
    if (monitoringInterval) {
      clearInterval(monitoringInterval)
    }
    
    monitoringInterval = setInterval(async () => {
      await updatePerformanceMetrics()
    }, interval)
    
    // 立即执行一次
    updatePerformanceMetrics()
  }

  // 停止监控
  const stopMonitoring = () => {
    if (monitoringInterval) {
      clearInterval(monitoringInterval)
      monitoringInterval = null
    }
  }

  // 更新性能指标
  const updatePerformanceMetrics = async () => {
    try {
      const models = await aiService.listModels()
      
      for (const model of models) {
        try {
          const perf = await aiService.getPerformance(model.id)
          performance[model.id] = perf
          
          // 检查告警
          checkAlerts(model.id, perf)
        } catch (err) {
          console.warn(`获取模型 ${model.id} 性能失败:`, err)
        }
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update metrics'
      console.error('更新性能指标失败:', err)
    }
  }

  // 检查告警
  const checkAlerts = (modelId: string, perf: AIModelPerformance) => {
    const newAlerts = []
    
    // 错误率告警
    if (perf.metrics.error_rate > 0.1) {
      newAlerts.push({
        id: `alert_${Date.now()}_error_rate`,
        type: 'error_rate',
        modelId,
        message: `模型 ${modelId} 错误率过高: ${(perf.metrics.error_rate * 100).toFixed(1)}%`,
        severity: 'high',
        timestamp: Date.now()
      })
    }
    
    // 延迟告警
    if (perf.metrics.average_latency > 5000) {
      newAlerts.push({
        id: `alert_${Date.now()}_latency`,
        type: 'latency',
        modelId,
        message: `模型 ${modelId} 响应延迟过高: ${perf.metrics.average_latency}ms`,
        severity: 'medium',
        timestamp: Date.now()
      })
    }
    
    // 准确率告警
    if (perf.metrics.accuracy < 0.8) {
      newAlerts.push({
        id: `alert_${Date.now()}_accuracy`,
        type: 'accuracy',
        modelId,
        message: `模型 ${modelId} 准确率偏低: ${(perf.metrics.accuracy * 100).toFixed(1)}%`,
        severity: 'medium',
        timestamp: Date.now()
      })
    }
    
    alerts.value.push(...newAlerts)
    
    // 保持最近100条告警
    if (alerts.value.length > 100) {
      alerts.value = alerts.value.slice(-100)
    }
  }

  // 清除告警
  const clearAlert = (alertId: string) => {
    const index = alerts.value.findIndex(alert => alert.id === alertId)
    if (index !== -1) {
      alerts.value.splice(index, 1)
    }
  }

  // 清除所有告警
  const clearAllAlerts = () => {
    alerts.value.length = 0
  }

  // 计算属性
  const totalInferences = computed(() => {
    return Object.values(performance).reduce((sum, perf) => 
      sum + perf.metrics.inference_count, 0
    )
  })
  
  const averageLatency = computed(() => {
    const perfs = Object.values(performance)
    if (perfs.length === 0) return 0
    
    return perfs.reduce((sum, perf) => 
      sum + perf.metrics.average_latency, 0
    ) / perfs.length
  })
  
  const overallErrorRate = computed(() => {
    const perfs = Object.values(performance)
    if (perfs.length === 0) return 0
    
    return perfs.reduce((sum, perf) => 
      sum + perf.metrics.error_rate, 0
    ) / perfs.length
  })
  
  const activeAlerts = computed(() => 
    alerts.value.filter(alert => 
      Date.now() - alert.timestamp < 24 * 60 * 60 * 1000 // 24小时内
    )
  )

  // 生命周期
  onMounted(() => {
    startMonitoring()
  })
  
  onUnmounted(() => {
    stopMonitoring()
  })

  return {
    // 状态
    loading: readonly(loading),
    error: readonly(error),
    performance: readonly(performance),
    alerts: readonly(alerts),
    
    // 计算属性
    totalInferences,
    averageLatency,
    overallErrorRate,
    activeAlerts,
    
    // 方法
    startMonitoring,
    stopMonitoring,
    updatePerformanceMetrics,
    clearAlert,
    clearAllAlerts
  }
} 