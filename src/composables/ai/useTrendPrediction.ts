import { ref, computed } from 'vue'
import { TrendPredictionAI } from '@/services/ai/TrendPredictionAI'
import type { 
  TrendPrediction, 
  PredictionHorizon,
  TrendDirection,
  RiskLevel,
  MarketScenario
} from '@/types/ai'

// 全局状态
const trendPredictionAI = new TrendPredictionAI()
const prediction = ref<TrendPrediction | null>(null)
const loading = ref(false)
const error = ref<string | null>(null)

// 缓存管理
const predictionCache = new Map<string, TrendPrediction>()
const CACHE_DURATION = 5 * 60 * 1000 // 5分钟

/**
 * 趋势预测组合式API
 */
export function useTrendPrediction() {
  
  /**
   * 生成趋势预测
   */
  const generatePrediction = async (
    symbol: string,
    horizon: PredictionHorizon = 'medium',
    options?: {
      useCache?: boolean
      includeScenarios?: boolean
      includeTechnicals?: boolean
    }
  ) => {
    const cacheKey = `${symbol}-${horizon}`
    
    // 检查缓存
    if (options?.useCache !== false && predictionCache.has(cacheKey)) {
      const cached = predictionCache.get(cacheKey)!
      if (Date.now() - cached.timestamp < CACHE_DURATION) {
        prediction.value = cached
        return cached
      }
    }

    loading.value = true
    error.value = null

    try {
      // 准备预测参数
      const predictionParams = {
        symbol,
        horizon,
        includeScenarios: options?.includeScenarios ?? true,
        includeTechnicals: options?.includeTechnicals ?? true,
        confidence: 0.95
      }

      // 执行预测
      const result = await trendPredictionAI.predict(predictionParams)
      
      // 增强预测结果
      const enhancedPrediction: TrendPrediction = {
        ...result,
        timestamp: Date.now(),
        symbol,
        horizon,
        
        // 历史数据（模拟）
        historicalData: {
          dates: generateDateRange(30, -30),
          prices: generatePriceHistory(30, result.currentPrice)
        },
        
        // 预测数据
        forecastData: {
          dates: generateDateRange(getForecastDays(horizon)),
          prices: generateForecastPrices(getForecastDays(horizon), result.targetPrice),
          upperBound: generateForecastPrices(getForecastDays(horizon), result.priceRange.max),
          lowerBound: generateForecastPrices(getForecastDays(horizon), result.priceRange.min)
        },
        
        // 关键影响因子
        keyFactors: [
          {
            name: '市场情绪',
            impact: Math.random() * 0.4 - 0.2,
            weight: 0.25,
            description: '基于新闻和社交媒体情绪分析'
          },
          {
            name: '技术指标',
            impact: Math.random() * 0.3 - 0.15,
            weight: 0.20,
            description: '移动平均线、RSI、MACD等技术指标'
          },
          {
            name: '基本面',
            impact: Math.random() * 0.2 - 0.1,
            weight: 0.30,
            description: '财务指标和估值水平'
          },
          {
            name: '行业趋势',
            impact: Math.random() * 0.25 - 0.125,
            weight: 0.15,
            description: '所属行业的整体发展趋势'
          },
          {
            name: '宏观经济',
            impact: Math.random() * 0.15 - 0.075,
            weight: 0.10,
            description: '宏观经济环境和政策影响'
          }
        ],
        
        // 技术指标详情
        technicalIndicators: {
          movingAverages: [
            { period: 5, value: result.currentPrice * (1 + Math.random() * 0.02 - 0.01), signal: getRandomSignal() },
            { period: 10, value: result.currentPrice * (1 + Math.random() * 0.04 - 0.02), signal: getRandomSignal() },
            { period: 20, value: result.currentPrice * (1 + Math.random() * 0.06 - 0.03), signal: getRandomSignal() },
            { period: 50, value: result.currentPrice * (1 + Math.random() * 0.1 - 0.05), signal: getRandomSignal() }
          ],
          rsi: 30 + Math.random() * 40,
          macd: {
            value: Math.random() * 2 - 1,
            signal: getRandomSignal(),
            histogram: Math.random() * 1 - 0.5
          },
          bollingerBands: {
            upper: result.currentPrice * 1.05,
            middle: result.currentPrice,
            lower: result.currentPrice * 0.95,
            signal: getRandomSignal()
          }
        },
        
        // 风险评估
        riskAssessment: {
          overallRisk: getRandomRiskLevel(),
          riskScore: Math.random() * 10,
          riskFactors: [
            {
              type: 'market',
              name: '市场风险',
              level: getRandomRiskLevel(),
              description: '市场整体波动对股价的影响'
            },
            {
              type: 'industry',
              name: '行业风险',
              level: getRandomRiskLevel(),
              description: '行业特定风险因素'
            },
            {
              type: 'company',
              name: '公司风险',
              level: getRandomRiskLevel(),
              description: '公司特定的经营风险'
            }
          ]
        },
        
        // 情景分析
        scenarioAnalysis: [
          {
            scenario: 'bull' as MarketScenario,
            probability: 0.3,
            priceTarget: result.targetPrice * 1.15,
            expectedReturn: 0.15,
            description: '市场乐观情况下的预期表现'
          },
          {
            scenario: 'neutral' as MarketScenario,
            probability: 0.5,
            priceTarget: result.targetPrice,
            expectedReturn: (result.targetPrice - result.currentPrice) / result.currentPrice,
            description: '市场中性情况下的预期表现'
          },
          {
            scenario: 'bear' as MarketScenario,
            probability: 0.2,
            priceTarget: result.targetPrice * 0.85,
            expectedReturn: -0.15,
            description: '市场悲观情况下的预期表现'
          }
        ]
      }

      prediction.value = enhancedPrediction
      
      // 缓存结果
      predictionCache.set(cacheKey, enhancedPrediction)
      
      return enhancedPrediction

    } catch (err) {
      error.value = err instanceof Error ? err.message : '预测生成失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 批量预测
   */
  const batchPredict = async (
    symbols: string[],
    horizon: PredictionHorizon = 'medium'
  ) => {
    loading.value = true
    error.value = null

    try {
      const predictions = await Promise.all(
        symbols.map(symbol => generatePrediction(symbol, horizon, { useCache: false }))
      )
      
      return predictions
    } catch (err) {
      error.value = err instanceof Error ? err.message : '批量预测失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新预测
   */
  const updatePrediction = async (
    symbol: string,
    horizon: PredictionHorizon
  ) => {
    const cacheKey = `${symbol}-${horizon}`
    predictionCache.delete(cacheKey)
    return generatePrediction(symbol, horizon, { useCache: false })
  }

  /**
   * 清除预测结果
   */
  const clearPrediction = () => {
    prediction.value = null
    error.value = null
  }

  /**
   * 清除缓存
   */
  const clearCache = () => {
    predictionCache.clear()
  }

  /**
   * 获取预测准确率
   */
  const getPredictionAccuracy = async (symbol: string, days: number = 30) => {
    try {
      return await trendPredictionAI.getAccuracy(symbol, days)
    } catch (err) {
      console.error('获取预测准确率失败:', err)
      return 0
    }
  }

  // 计算属性
  const hasPrediction = computed(() => prediction.value !== null)
  const predictionAge = computed(() => {
    if (!prediction.value) return 0
    return Date.now() - prediction.value.timestamp
  })
  const isStale = computed(() => predictionAge.value > CACHE_DURATION)

  return {
    // 状态
    prediction: computed(() => prediction.value),
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    hasPrediction,
    predictionAge,
    isStale,
    
    // 方法
    generatePrediction,
    batchPredict,
    updatePrediction,
    clearPrediction,
    clearCache,
    getPredictionAccuracy
  }
}

// 工具函数
function generateDateRange(days: number, startOffset: number = 0): string[] {
  const dates = []
  const startDate = new Date()
  startDate.setDate(startDate.getDate() + startOffset)
  
  for (let i = 0; i < days; i++) {
    const date = new Date(startDate)
    date.setDate(date.getDate() + i)
    dates.push(date.toISOString().split('T')[0])
  }
  
  return dates
}

function generatePriceHistory(days: number, currentPrice: number): number[] {
  const prices = []
  let price = currentPrice * 0.9 // 起始价格
  
  for (let i = 0; i < days; i++) {
    price *= (1 + (Math.random() - 0.5) * 0.04) // 随机波动 ±2%
    prices.push(price)
  }
  
  return prices
}

function generateForecastPrices(days: number, targetPrice: number): number[] {
  const prices = []
  const startPrice = targetPrice * 0.95
  const priceStep = (targetPrice - startPrice) / days
  
  for (let i = 0; i < days; i++) {
    const basePrice = startPrice + priceStep * i
    const noise = (Math.random() - 0.5) * 0.02 * basePrice
    prices.push(basePrice + noise)
  }
  
  return prices
}

function getForecastDays(horizon: PredictionHorizon): number {
  switch (horizon) {
    case 'short': return 7
    case 'medium': return 30
    case 'long': return 90
    default: return 30
  }
}

function getRandomSignal(): 'buy' | 'sell' | 'hold' {
  const signals = ['buy', 'sell', 'hold'] as const
  return signals[Math.floor(Math.random() * signals.length)]
}

function getRandomRiskLevel(): RiskLevel {
  const levels = ['low', 'medium', 'high', 'very_high'] as const
  return levels[Math.floor(Math.random() * levels.length)]
}

// 导出单例实例
export const trendPredictionService = {
  async predict(symbol: string, horizon: PredictionHorizon = 'medium') {
    const { generatePrediction } = useTrendPrediction()
    return generatePrediction(symbol, horizon)
  },
  
  async batchPredict(symbols: string[], horizon: PredictionHorizon = 'medium') {
    const { batchPredict } = useTrendPrediction()
    return batchPredict(symbols, horizon)
  },
  
  async getAccuracy(symbol: string, days: number = 30) {
    const { getPredictionAccuracy } = useTrendPrediction()
    return getPredictionAccuracy(symbol, days)
  }
} 