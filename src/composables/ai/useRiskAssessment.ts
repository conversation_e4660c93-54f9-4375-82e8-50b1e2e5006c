import { ref, computed } from 'vue'
import { RiskAssessmentAI } from '@/services/ai/RiskAssessmentAI'
import type { 
  RiskAssessment, 
  RiskLevel,
  AssessmentType,
  RiskAlert,
  AlertSeverity
} from '@/types/ai'

// 全局状态
const riskAssessmentAI = new RiskAssessmentAI()
const assessment = ref<RiskAssessment | null>(null)
const loading = ref(false)
const error = ref<string | null>(null)

// 缓存管理
const assessmentCache = new Map<string, RiskAssessment>()
const CACHE_DURATION = 10 * 60 * 1000 // 10分钟

/**
 * 风险评估组合式API
 */
export function useRiskAssessment() {
  
  /**
   * 执行风险评估
   */
  const performAssessment = async (
    symbol: string,
    type: AssessmentType = 'comprehensive',
    options?: {
      useCache?: boolean
      includeStressTest?: boolean
      includeVaR?: boolean
    }
  ) => {
    const cacheKey = `${symbol}-${type}`
    
    // 检查缓存
    if (options?.useCache !== false && assessmentCache.has(cacheKey)) {
      const cached = assessmentCache.get(cacheKey)!
      if (Date.now() - cached.timestamp < CACHE_DURATION) {
        assessment.value = cached
        return cached
      }
    }

    loading.value = true
    error.value = null

    try {
      // 准备评估参数
      const assessmentParams = {
        symbol,
        type,
        includeStressTest: options?.includeStressTest ?? true,
        includeVaR: options?.includeVaR ?? true,
        confidence: 0.95
      }

      // 执行基础风险评估
      const result = await riskAssessmentAI.assess(assessmentParams)
      
      // 增强评估结果
      const enhancedAssessment: RiskAssessment = {
        ...result,
        timestamp: Date.now(),
        symbol,
        type,
        
        // 整体风险评估
        overallRisk: {
          score: Math.random() * 10,
          level: getRandomRiskLevel(),
          description: '基于多维度风险因素的综合评估'
        },
        
        // 市场风险
        marketRisk: {
          score: Math.random() * 10,
          beta: 0.8 + Math.random() * 0.8, // Beta系数 0.8-1.6
          volatility: Math.random() * 0.4, // 波动率 0-40%
          correlation: Math.random() * 2 - 1, // 相关性 -1到1
          description: '股票相对于市场的系统性风险'
        },
        
        // 信用风险
        creditRisk: {
          score: Math.random() * 10,
          rating: getRandomCreditRating(),
          probability: Math.random() * 0.1, // 违约概率 0-10%
          spread: Math.random() * 500, // 信用利差 0-500bp
          description: '公司财务健康状况和偿债能力评估'
        },
        
        // 流动性风险
        liquidityRisk: {
          score: Math.random() * 10,
          turnoverRate: Math.random() * 0.2, // 换手率 0-20%
          bidAskSpread: Math.random() * 0.05, // 买卖价差 0-5%
          marketDepth: Math.random() * 1000000, // 市场深度
          description: '股票交易流动性和变现能力评估'
        },
        
        // 操作风险
        operationalRisk: {
          score: Math.random() * 10,
          governance: Math.random() * 10,
          compliance: Math.random() * 10,
          management: Math.random() * 10,
          description: '公司运营管理和内控风险评估'
        },
        
        // 风险分解
        riskBreakdown: [
          {
            category: '市场风险',
            score: Math.random() * 10,
            weight: 0.4,
            description: '市场整体波动对股价的影响',
            expectedReturn: Math.random() * 0.2 - 0.1
          },
          {
            category: '信用风险',
            score: Math.random() * 10,
            weight: 0.25,
            description: '公司财务状况和偿债能力风险',
            expectedReturn: Math.random() * 0.15 - 0.075
          },
          {
            category: '流动性风险',
            score: Math.random() * 10,
            weight: 0.2,
            description: '股票交易活跃度和变现风险',
            expectedReturn: Math.random() * 0.1 - 0.05
          },
          {
            category: '操作风险',
            score: Math.random() * 10,
            weight: 0.15,
            description: '公司运营管理和合规风险',
            expectedReturn: Math.random() * 0.08 - 0.04
          }
        ],
        
        // VaR分析
        varAnalysis: {
          oneDay: 1000 + Math.random() * 2000,
          oneWeek: 2500 + Math.random() * 5000,
          oneMonth: 8000 + Math.random() * 15000,
          conditionalVar: 12000 + Math.random() * 20000,
          confidence: 0.95
        },
        
        // 压力测试
        stressTest: [
          {
            name: '市场崩盘',
            impact: -0.3 - Math.random() * 0.2,
            probability: 0.05,
            description: '类似2008年金融危机的极端市场情况'
          },
          {
            name: '行业危机',
            impact: -0.2 - Math.random() * 0.15,
            probability: 0.1,
            description: '行业特定的重大负面事件'
          },
          {
            name: '公司丑闻',
            impact: -0.4 - Math.random() * 0.3,
            probability: 0.02,
            description: '公司治理或财务造假等重大丑闻'
          },
          {
            name: '监管变化',
            impact: -0.15 - Math.random() * 0.1,
            probability: 0.15,
            description: '不利的监管政策变化'
          },
          {
            name: '利率上升',
            impact: -0.1 - Math.random() * 0.08,
            probability: 0.3,
            description: '央行加息对股市的负面影响'
          }
        ],
        
        // 风险因子
        riskFactors: [
          {
            name: '市场Beta',
            exposure: Math.random() * 2 - 1,
            contribution: Math.random() * 0.4,
            volatility: Math.random() * 0.3,
            description: '股票相对市场的敏感度'
          },
          {
            name: '行业因子',
            exposure: Math.random() * 1.5 - 0.75,
            contribution: Math.random() * 0.3,
            volatility: Math.random() * 0.25,
            description: '行业特定风险因子'
          },
          {
            name: '规模因子',
            exposure: Math.random() * 1 - 0.5,
            contribution: Math.random() * 0.2,
            volatility: Math.random() * 0.2,
            description: '公司规模相关风险'
          },
          {
            name: '价值因子',
            exposure: Math.random() * 1.2 - 0.6,
            contribution: Math.random() * 0.25,
            volatility: Math.random() * 0.22,
            description: '估值水平相关风险'
          },
          {
            name: '动量因子',
            exposure: Math.random() * 0.8 - 0.4,
            contribution: Math.random() * 0.15,
            volatility: Math.random() * 0.18,
            description: '价格动量相关风险'
          }
        ],
        
        // 当前预警
        currentAlerts: generateCurrentAlerts()
      }

      assessment.value = enhancedAssessment
      
      // 缓存结果
      assessmentCache.set(cacheKey, enhancedAssessment)
      
      return enhancedAssessment

    } catch (err) {
      error.value = err instanceof Error ? err.message : '风险评估失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 批量风险评估
   */
  const batchAssess = async (
    symbols: string[],
    type: AssessmentType = 'comprehensive'
  ) => {
    loading.value = true
    error.value = null

    try {
      const assessments = await Promise.all(
        symbols.map(symbol => performAssessment(symbol, type, { useCache: false }))
      )
      
      return assessments
    } catch (err) {
      error.value = err instanceof Error ? err.message : '批量评估失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新风险评估
   */
  const updateAssessment = async (
    symbol: string,
    type: AssessmentType
  ) => {
    const cacheKey = `${symbol}-${type}`
    assessmentCache.delete(cacheKey)
    return performAssessment(symbol, type, { useCache: false })
  }

  /**
   * 确认预警
   */
  const acknowledgeAlert = async (alertId: string) => {
    if (!assessment.value) return

    const alertIndex = assessment.value.currentAlerts.findIndex(
      alert => alert.id === alertId
    )
    
    if (alertIndex !== -1) {
      assessment.value.currentAlerts.splice(alertIndex, 1)
    }
  }

  /**
   * 设置风险阈值
   */
  const setRiskThresholds = async (thresholds: {
    overall?: number
    var?: number
    volatility?: number
  }) => {
    try {
      await riskAssessmentAI.setThresholds(thresholds)
    } catch (err) {
      console.error('设置风险阈值失败:', err)
      throw err
    }
  }

  /**
   * 获取风险历史
   */
  const getRiskHistory = async (symbol: string, days: number = 30) => {
    try {
      return await riskAssessmentAI.getHistory(symbol, days)
    } catch (err) {
      console.error('获取风险历史失败:', err)
      return []
    }
  }

  /**
   * 清除评估结果
   */
  const clearAssessment = () => {
    assessment.value = null
    error.value = null
  }

  /**
   * 清除缓存
   */
  const clearCache = () => {
    assessmentCache.clear()
  }

  // 计算属性
  const hasAssessment = computed(() => assessment.value !== null)
  const assessmentAge = computed(() => {
    if (!assessment.value) return 0
    return Date.now() - assessment.value.timestamp
  })
  const isStale = computed(() => assessmentAge.value > CACHE_DURATION)
  const alertCount = computed(() => assessment.value?.currentAlerts.length || 0)
  const highRiskAlerts = computed(() => 
    assessment.value?.currentAlerts.filter(alert => 
      alert.severity === 'high' || alert.severity === 'critical'
    ).length || 0
  )

  return {
    // 状态
    assessment: computed(() => assessment.value),
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    hasAssessment,
    assessmentAge,
    isStale,
    alertCount,
    highRiskAlerts,
    
    // 方法
    performAssessment,
    batchAssess,
    updateAssessment,
    acknowledgeAlert,
    setRiskThresholds,
    getRiskHistory,
    clearAssessment,
    clearCache
  }
}

// 工具函数
function getRandomRiskLevel(): RiskLevel {
  const levels = ['low', 'medium', 'high', 'very_high'] as const
  return levels[Math.floor(Math.random() * levels.length)]
}

function getRandomCreditRating(): string {
  const ratings = ['AAA', 'AA+', 'AA', 'AA-', 'A+', 'A', 'A-', 'BBB+', 'BBB', 'BBB-', 'BB+', 'BB', 'BB-']
  return ratings[Math.floor(Math.random() * ratings.length)]
}

function generateCurrentAlerts(): RiskAlert[] {
  const alerts: RiskAlert[] = []
  const alertTypes = [
    {
      severity: 'high' as AlertSeverity,
      title: 'VaR超出阈值',
      message: '1日VaR超出设定阈值，建议关注风险敞口'
    },
    {
      severity: 'medium' as AlertSeverity,
      title: '波动率上升',
      message: '股价波动率较前期显著上升，需要注意市场风险'
    },
    {
      severity: 'low' as AlertSeverity,
      title: '流动性下降',
      message: '交易量较平均水平下降，可能影响变现能力'
    },
    {
      severity: 'critical' as AlertSeverity,
      title: '信用评级下调',
      message: '公司信用评级被下调，信用风险显著上升'
    }
  ]

  // 随机生成0-3个预警
  const alertCount = Math.floor(Math.random() * 4)
  for (let i = 0; i < alertCount; i++) {
    const alertType = alertTypes[Math.floor(Math.random() * alertTypes.length)]
    alerts.push({
      id: `alert-${Date.now()}-${i}`,
      ...alertType,
      timestamp: Date.now() - Math.random() * 3600000 // 最近1小时内
    })
  }

  return alerts
}

// 导出单例实例
export const riskAssessmentService = {
  async assess(symbol: string, type: AssessmentType = 'comprehensive') {
    const { performAssessment } = useRiskAssessment()
    return performAssessment(symbol, type)
  },
  
  async batchAssess(symbols: string[], type: AssessmentType = 'comprehensive') {
    const { batchAssess } = useRiskAssessment()
    return batchAssess(symbols, type)
  },
  
  async getHistory(symbol: string, days: number = 30) {
    const { getRiskHistory } = useRiskAssessment()
    return getRiskHistory(symbol, days)
  }
} 