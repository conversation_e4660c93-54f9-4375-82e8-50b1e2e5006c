import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import type {
  StockData,
  DataPipelineConfig,
  PipelineMetrics,
  ProcessingResult,
  DataBatch,
  FeatureConfig,
  FeatureVector,
  FeatureImportance,
  FeatureSelectionMethod,
  NormalizationMethod,
  TrainingConfig,
  TrainingJob,
  TrainingProgress,
  TrainingMetrics,
  ValidationResult,
  ModelVersion,
  HyperParameters
} from '@/types/ai'

import { DataPipeline } from '@/services/ai/DataPipeline'
import { FeatureEngineering } from '@/services/ai/FeatureEngineering'
import { ModelTrainingManager } from '@/services/ai/ModelTrainingManager'

/**
 * 数据管道管理组合式API
 */
export function useDataPipeline() {
  // 状态管理
  const isInitialized = ref(false)
  const isRunning = ref(false)
  const error = ref<string | null>(null)

  // 服务实例
  let dataPipeline: DataPipeline | null = null
  let featureEngineering: FeatureEngineering | null = null
  let trainingManager: ModelTrainingManager | null = null

  // 管道配置
  const pipelineConfig = reactive<DataPipelineConfig>({
    qualityThreshold: 0.8,
    timelinessThreshold: 300000, // 5分钟
    defaultTransformations: ['normalize', 'enrich'],
    cacheConfig: {
      enabled: true,
      ttl: 3600,
      maxSize: 1000
    },
    streamingConfig: {
      enabled: true,
      bufferSize: 100,
      batchSize: 10,
      flushInterval: 5000
    }
  })

  // 管道指标
  const metrics = ref<PipelineMetrics>({
    processedRecords: 0,
    errorCount: 0,
    averageLatency: 0,
    throughput: 0,
    cacheHitRate: 0,
    qualityScore: 0
  })

  // 处理结果历史
  const processingHistory = ref<ProcessingResult[]>([])

  // 计算属性
  const isHealthy = computed(() => {
    return metrics.value.errorCount === 0 || 
           (metrics.value.errorCount / Math.max(metrics.value.processedRecords, 1)) < 0.1
  })

  const performanceScore = computed(() => {
    const latencyScore = Math.max(0, 100 - metrics.value.averageLatency / 10)
    const throughputScore = Math.min(100, metrics.value.throughput * 10)
    const qualityScore = metrics.value.qualityScore * 100
    const cacheScore = metrics.value.cacheHitRate * 100
    
    return (latencyScore + throughputScore + qualityScore + cacheScore) / 4
  })

  /**
   * 初始化数据管道
   */
  const initialize = async (config?: Partial<DataPipelineConfig>) => {
    try {
      if (config) {
        Object.assign(pipelineConfig, config)
      }

      // 初始化服务
      dataPipeline = new DataPipeline(pipelineConfig)
      featureEngineering = new FeatureEngineering()
      trainingManager = new ModelTrainingManager()

      isInitialized.value = true
      error.value = null

    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to initialize pipeline'
      throw err
    }
  }

  /**
   * 启动数据管道
   */
  const start = async () => {
    if (!dataPipeline) {
      throw new Error('Pipeline not initialized')
    }

    try {
      await dataPipeline.start()
      isRunning.value = true
      
      // 启动指标监控
      startMetricsMonitoring()

    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to start pipeline'
      throw err
    }
  }

  /**
   * 停止数据管道
   */
  const stop = async () => {
    if (!dataPipeline) return

    try {
      await dataPipeline.stop()
      isRunning.value = false
      
      // 停止指标监控
      stopMetricsMonitoring()

    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to stop pipeline'
      throw err
    }
  }

  /**
   * 处理数据批次
   */
  const processBatch = async (
    data: StockData[], 
    transformations?: string[], 
    config?: any
  ): Promise<ProcessingResult> => {
    if (!dataPipeline) {
      throw new Error('Pipeline not initialized')
    }

    try {
      const batchId = await dataPipeline.addToQueue(data, transformations, config)
      
      // 等待处理完成（简化实现）
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟处理结果
      const result: ProcessingResult = {
        batchId,
        processedCount: data.length,
        errorCount: 0,
        warnings: [],
        errors: [],
        metrics: {
          processingTime: Math.random() * 1000 + 100,
          throughput: data.length / (Math.random() * 2 + 1),
          qualityScore: 0.8 + Math.random() * 0.2
        },
        transformedData: data
      }

      processingHistory.value.unshift(result)
      
      // 保持历史记录在合理范围内
      if (processingHistory.value.length > 100) {
        processingHistory.value = processingHistory.value.slice(0, 100)
      }

      return result

    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Batch processing failed'
      throw err
    }
  }

  /**
   * 实时数据处理
   */
  const processRealtime = async (data: StockData): Promise<StockData> => {
    if (!dataPipeline) {
      throw new Error('Pipeline not initialized')
    }

    try {
      return await dataPipeline.processRealtime(data)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Realtime processing failed'
      throw err
    }
  }

  // 指标监控相关
  let metricsInterval: NodeJS.Timeout | null = null

  const startMetricsMonitoring = () => {
    if (metricsInterval) return

    metricsInterval = setInterval(() => {
      updateMetrics()
    }, 5000) // 每5秒更新一次指标
  }

  const stopMetricsMonitoring = () => {
    if (metricsInterval) {
      clearInterval(metricsInterval)
      metricsInterval = null
    }
  }

  const updateMetrics = () => {
    if (!dataPipeline) return

    const newMetrics = dataPipeline.getMetrics()
    metrics.value = { ...newMetrics }
  }

  // 生命周期管理
  onMounted(() => {
    // 自动初始化
    initialize().catch(console.error)
  })

  onUnmounted(() => {
    // 清理资源
    stopMetricsMonitoring()
    if (isRunning.value) {
      stop().catch(console.error)
    }
  })

  return {
    // 状态
    isInitialized,
    isRunning,
    error,
    metrics,
    processingHistory,
    
    // 计算属性
    isHealthy,
    performanceScore,
    
    // 配置
    pipelineConfig,
    
    // 方法
    initialize,
    start,
    stop,
    processBatch,
    processRealtime,
    updateMetrics
  }
}

/**
 * 特征工程组合式API
 */
export function useFeatureEngineering() {
  // 状态管理
  const isProcessing = ref(false)
  const error = ref<string | null>(null)
  
  // 特征工程服务
  let featureEngineering: FeatureEngineering | null = null

  // 特征配置
  const featureConfig = reactive<FeatureConfig>({
    includeBasicFeatures: true,
    includeTechnicalIndicators: true,
    includeStatisticalFeatures: true,
    includeFundamentalFeatures: false,
    includeMarketMicrostructure: false,
    includeSentimentFeatures: false,
    technicalConfig: {
      periods: {
        sma: [5, 10, 20, 50],
        ema: [12, 26],
        rsi: 14,
        macd: { fast: 12, slow: 26, signal: 9 },
        bollinger: { period: 20, stdDev: 2 }
      }
    }
  })

  // 特征向量缓存
  const featureVectors = ref<FeatureVector[]>([])
  const featureImportance = ref<FeatureImportance[]>([])

  /**
   * 初始化特征工程
   */
  const initializeFeatureEngineering = () => {
    if (!featureEngineering) {
      featureEngineering = new FeatureEngineering()
    }
  }

  /**
   * 提取特征
   */
  const extractFeatures = async (
    stockData: StockData[], 
    config?: Partial<FeatureConfig>
  ): Promise<FeatureVector[]> => {
    initializeFeatureEngineering()
    
    if (!featureEngineering) {
      throw new Error('Feature engineering not initialized')
    }

    isProcessing.value = true
    error.value = null

    try {
      const finalConfig = { ...featureConfig, ...config }
      const vectors = await featureEngineering.extractFeatures(stockData, finalConfig)
      
      featureVectors.value = vectors
      return vectors

    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Feature extraction failed'
      throw err
    } finally {
      isProcessing.value = false
    }
  }

  /**
   * 特征选择
   */
  const selectFeatures = async (
    features: FeatureVector[],
    method: FeatureSelectionMethod = 'correlation',
    targetVariable?: string,
    topK?: number
  ): Promise<{
    selectedFeatures: string[]
    importance: FeatureImportance[]
    reducedFeatures: FeatureVector[]
  }> => {
    initializeFeatureEngineering()
    
    if (!featureEngineering) {
      throw new Error('Feature engineering not initialized')
    }

    isProcessing.value = true

    try {
      const result = await featureEngineering.selectFeatures(
        features, 
        method, 
        targetVariable, 
        topK
      )
      
      featureImportance.value = result.importance
      return result

    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Feature selection failed'
      throw err
    } finally {
      isProcessing.value = false
    }
  }

  /**
   * 特征标准化
   */
  const normalizeFeatures = async (
    features: FeatureVector[],
    method: NormalizationMethod = 'standard'
  ): Promise<{
    normalizedFeatures: FeatureVector[]
    normalizationParams: any
  }> => {
    initializeFeatureEngineering()
    
    if (!featureEngineering) {
      throw new Error('Feature engineering not initialized')
    }

    isProcessing.value = true

    try {
      return await featureEngineering.normalizeFeatures(features, method)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Feature normalization failed'
      throw err
    } finally {
      isProcessing.value = false
    }
  }

  /**
   * 清理缓存
   */
  const clearCache = () => {
    if (featureEngineering) {
      featureEngineering.clearCache()
    }
    featureVectors.value = []
    featureImportance.value = []
  }

  return {
    // 状态
    isProcessing,
    error,
    featureVectors,
    featureImportance,
    
    // 配置
    featureConfig,
    
    // 方法
    extractFeatures,
    selectFeatures,
    normalizeFeatures,
    clearCache
  }
}

/**
 * 模型训练组合式API
 */
export function useModelTraining() {
  // 状态管理
  const isTraining = ref(false)
  const error = ref<string | null>(null)
  
  // 训练管理器
  let trainingManager: ModelTrainingManager | null = null

  // 训练作业
  const trainingJobs = ref<TrainingJob[]>([])
  const activeJob = ref<TrainingJob | null>(null)
  const trainingProgress = ref<TrainingProgress | null>(null)
  const trainingMetrics = ref<TrainingMetrics | null>(null)

  // 模型版本
  const modelVersions = ref<ModelVersion[]>([])

  /**
   * 初始化训练管理器
   */
  const initializeTrainingManager = () => {
    if (!trainingManager) {
      trainingManager = new ModelTrainingManager()
    }
  }

  /**
   * 创建训练作业
   */
  const createTrainingJob = async (config: TrainingConfig): Promise<string> => {
    initializeTrainingManager()
    
    if (!trainingManager) {
      throw new Error('Training manager not initialized')
    }

    try {
      const jobId = await trainingManager.createTrainingJob(config)
      
      // 刷新作业列表
      await refreshTrainingJobs()
      
      return jobId

    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to create training job'
      throw err
    }
  }

  /**
   * 启动训练
   */
  const startTraining = async (jobId: string): Promise<void> => {
    if (!trainingManager) {
      throw new Error('Training manager not initialized')
    }

    isTraining.value = true
    error.value = null

    try {
      await trainingManager.startTraining(jobId)
      
      // 设置活动作业
      const job = trainingJobs.value.find(j => j.id === jobId)
      if (job) {
        activeJob.value = job
        startProgressMonitoring(jobId)
      }

    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to start training'
      isTraining.value = false
      throw err
    }
  }

  /**
   * 停止训练
   */
  const stopTraining = async (jobId: string): Promise<void> => {
    if (!trainingManager) {
      throw new Error('Training manager not initialized')
    }

    try {
      await trainingManager.stopTraining(jobId)
      isTraining.value = false
      stopProgressMonitoring()

    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to stop training'
      throw err
    }
  }

  /**
   * 验证模型
   */
  const validateModel = async (jobId: string): Promise<ValidationResult> => {
    if (!trainingManager) {
      throw new Error('Training manager not initialized')
    }

    try {
      return await trainingManager.validateModel(jobId)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Model validation failed'
      throw err
    }
  }

  /**
   * 保存模型版本
   */
  const saveModelVersion = async (
    jobId: string, 
    version: string, 
    description?: string
  ): Promise<ModelVersion> => {
    if (!trainingManager) {
      throw new Error('Training manager not initialized')
    }

    try {
      const modelVersion = await trainingManager.saveModelVersion(jobId, version, description)
      
      // 刷新版本列表
      await refreshModelVersions(modelVersion.modelName)
      
      return modelVersion

    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to save model version'
      throw err
    }
  }

  /**
   * 超参数优化
   */
  const optimizeHyperParameters = async (
    modelName: string,
    parameterSpace: Record<string, any[]>,
    maxTrials: number = 50
  ): Promise<HyperParameters> => {
    if (!trainingManager) {
      throw new Error('Training manager not initialized')
    }

    try {
      return await trainingManager.optimizeHyperParameters(modelName, parameterSpace, maxTrials)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Hyperparameter optimization failed'
      throw err
    }
  }

  /**
   * 交叉验证
   */
  const crossValidate = async (
    config: TrainingConfig,
    folds: number = 5
  ): Promise<{
    meanAccuracy: number
    stdAccuracy: number
    foldResults: ValidationResult[]
  }> => {
    if (!trainingManager) {
      throw new Error('Training manager not initialized')
    }

    try {
      return await trainingManager.crossValidate(config, folds)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Cross validation failed'
      throw err
    }
  }

  // 进度监控
  let progressInterval: NodeJS.Timeout | null = null

  const startProgressMonitoring = (jobId: string) => {
    if (progressInterval) return

    progressInterval = setInterval(() => {
      updateTrainingProgress(jobId)
    }, 2000) // 每2秒更新一次进度
  }

  const stopProgressMonitoring = () => {
    if (progressInterval) {
      clearInterval(progressInterval)
      progressInterval = null
    }
  }

  const updateTrainingProgress = (jobId: string) => {
    if (!trainingManager) return

    const progress = trainingManager.getTrainingProgress(jobId)
    const metrics = trainingManager.getTrainingMetrics(jobId)
    
    trainingProgress.value = progress
    trainingMetrics.value = metrics

    // 检查训练是否完成
    if (progress && (progress.currentEpoch >= progress.totalEpochs)) {
      isTraining.value = false
      stopProgressMonitoring()
    }
  }

  /**
   * 刷新训练作业列表
   */
  const refreshTrainingJobs = async () => {
    // 在实际实现中，这里会从服务获取作业列表
    // 目前使用模拟数据
  }

  /**
   * 刷新模型版本列表
   */
  const refreshModelVersions = async (modelName: string) => {
    if (!trainingManager) return

    const versions = trainingManager.getModelVersions(modelName)
    modelVersions.value = versions
  }

  // 清理资源
  onUnmounted(() => {
    stopProgressMonitoring()
  })

  return {
    // 状态
    isTraining,
    error,
    trainingJobs,
    activeJob,
    trainingProgress,
    trainingMetrics,
    modelVersions,
    
    // 方法
    createTrainingJob,
    startTraining,
    stopTraining,
    validateModel,
    saveModelVersion,
    optimizeHyperParameters,
    crossValidate,
    refreshTrainingJobs,
    refreshModelVersions
  }
} 