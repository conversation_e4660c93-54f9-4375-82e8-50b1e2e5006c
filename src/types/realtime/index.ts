// 实时数据相关类型定义

// WebSocket连接状态
export enum WebSocketState {
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTING = 'disconnecting',
  DISCONNECTED = 'disconnected',
  ERROR = 'error'
}

// WebSocket消息类型
export enum MessageType {
  // 系统消息
  PING = 'ping',
  PONG = 'pong',
  SUBSCRIBE = 'subscribe',
  UNSUBSCRIBE = 'unsubscribe',
  ERROR = 'error',
  
  // 数据消息
  STOCK_PRICE = 'stock_price',
  STOCK_KLINE = 'stock_kline',
  MARKET_DATA = 'market_data',
  TRADE_SIGNAL = 'trade_signal',
  STRATEGY_STATUS = 'strategy_status',
  RISK_METRICS = 'risk_metrics',
  
  // 系统监控
  SYSTEM_STATUS = 'system_status',
  USER_ACTIVITY = 'user_activity',
  PERFORMANCE_METRICS = 'performance_metrics'
}

// WebSocket配置
export interface WebSocketConfig {
  url: string
  protocols?: string[]
  reconnect?: boolean
  reconnectInterval?: number
  reconnectAttempts?: number
  heartbeatInterval?: number
  heartbeatTimeout?: number
  messageQueueSize?: number
  autoConnect?: boolean
}

// WebSocket连接选项
export interface ConnectionOptions {
  timeout?: number
  retryDelay?: number
  maxRetries?: number
  backoffFactor?: number
  jitter?: boolean
}

// WebSocket消息结构
export interface WebSocketMessage<T = any> {
  id?: string
  type: MessageType
  channel?: string
  timestamp: number
  data: T
  metadata?: Record<string, any>
}

// 消息订阅配置
export interface SubscriptionConfig {
  channel: string
  type: MessageType
  params?: Record<string, any>
  callback?: (message: WebSocketMessage) => void
  onError?: (error: Error) => void
}

// 连接事件
export interface ConnectionEvent {
  type: 'open' | 'close' | 'error' | 'message'
  timestamp: number
  data?: any
  error?: Error
}

// 连接统计
export interface ConnectionStats {
  connectedAt?: number
  lastMessageAt?: number
  messagesReceived: number
  messagesSent: number
  reconnectCount: number
  errorCount: number
  averageLatency: number
  dataTransferred: number
}

// 心跳配置
export interface HeartbeatConfig {
  interval: number
  timeout: number
  maxMissed: number
  message?: any
}

// 消息队列项
export interface QueuedMessage {
  id: string
  message: WebSocketMessage
  timestamp: number
  retries: number
  priority: number
}

// 实时数据项
export interface RealtimeDataItem {
  id: string
  timestamp: number
  value: any
  source: string
  quality: 'good' | 'uncertain' | 'bad'
  metadata?: Record<string, any>
}

// 股票实时数据
export interface StockRealtimeData {
  symbol: string
  name: string
  price: number
  change: number
  changePercent: number
  volume: number
  turnover: number
  high: number
  low: number
  open: number
  previousClose: number
  timestamp: number
  marketStatus: 'open' | 'closed' | 'pre_market' | 'after_hours'
}

// K线实时数据
export interface KlineRealtimeData {
  symbol: string
  interval: string
  timestamp: number
  open: number
  high: number
  low: number
  close: number
  volume: number
  turnover: number
  isComplete: boolean
}

// 交易信号
export interface TradeSignal {
  id: string
  symbol: string
  type: 'buy' | 'sell' | 'hold'
  strength: number
  price: number
  volume?: number
  reason: string
  timestamp: number
  source: string
  confidence: number
}

// 策略状态
export interface StrategyStatus {
  id: string
  name: string
  status: 'running' | 'stopped' | 'paused' | 'error'
  performance: {
    totalReturn: number
    dailyReturn: number
    maxDrawdown: number
    sharpeRatio: number
    winRate: number
  }
  positions: Array<{
    symbol: string
    quantity: number
    avgPrice: number
    currentPrice: number
    pnl: number
    pnlPercent: number
  }>
  lastUpdate: number
}

// 风险指标
export interface RiskMetrics {
  portfolioValue: number
  totalPnL: number
  dailyPnL: number
  maxDrawdown: number
  var95: number
  var99: number
  beta: number
  alpha: number
  sharpeRatio: number
  volatility: number
  timestamp: number
}

// 系统状态
export interface SystemStatus {
  cpu: number
  memory: number
  disk: number
  network: {
    inbound: number
    outbound: number
  }
  connections: number
  activeUsers: number
  dataQuality: number
  timestamp: number
}

// 监控告警
export interface Alert {
  id: string
  type: 'info' | 'warning' | 'error' | 'critical'
  title: string
  message: string
  source: string
  timestamp: number
  acknowledged: boolean
  data?: any
}

// 数据流配置
export interface DataStreamConfig {
  bufferSize: number
  updateInterval: number
  compression: boolean
  batchSize: number
  priority: number
}

// 实时组件配置
export interface RealtimeComponentConfig {
  autoUpdate: boolean
  updateInterval: number
  maxDataPoints: number
  enableAnimation: boolean
  showTimestamp: boolean
  dataRetention: number
}

// WebSocket客户端接口
export interface IWebSocketClient {
  connect(options?: ConnectionOptions): Promise<void>
  disconnect(): Promise<void>
  send(message: WebSocketMessage): Promise<void>
  subscribe(config: SubscriptionConfig): string
  unsubscribe(subscriptionId: string): void
  getState(): WebSocketState
  getStats(): ConnectionStats
  on(event: string, callback: Function): void
  off(event: string, callback: Function): void
}

// 消息路由器接口
export interface IMessageRouter {
  addRoute(type: MessageType, handler: (message: WebSocketMessage) => void): void
  removeRoute(type: MessageType): void
  route(message: WebSocketMessage): void
  getRoutes(): Map<MessageType, Function>
}

// 连接管理器接口
export interface IConnectionManager {
  createConnection(config: WebSocketConfig): IWebSocketClient
  getConnection(id: string): IWebSocketClient | undefined
  closeConnection(id: string): Promise<void>
  closeAllConnections(): Promise<void>
  getConnectionStats(): Record<string, ConnectionStats>
}

// 数据缓存接口
export interface IDataCache {
  set(key: string, value: any, ttl?: number): void
  get(key: string): any
  has(key: string): boolean
  delete(key: string): boolean
  clear(): void
  size(): number
  getStats(): {
    hits: number
    misses: number
    size: number
    memoryUsage: number
  }
}

// 事件总线接口
export interface IEventBus {
  emit(event: string, ...args: any[]): void
  on(event: string, callback: Function): void
  off(event: string, callback: Function): void
  once(event: string, callback: Function): void
  removeAllListeners(event?: string): void
}

// 实时数据存储接口
export interface IRealtimeStore {
  addData(key: string, data: RealtimeDataItem): void
  getData(key: string, limit?: number): RealtimeDataItem[]
  getLatestData(key: string): RealtimeDataItem | undefined
  removeData(key: string): void
  clearAll(): void
  subscribe(key: string, callback: (data: RealtimeDataItem) => void): string
  unsubscribe(subscriptionId: string): void
}

// 导出所有类型
export * from './websocket'
export * from './data'
export * from './monitoring' 