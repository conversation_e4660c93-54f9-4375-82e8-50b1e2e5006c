// AI相关类型定义

// AI模型类型
export enum AIModelType {
  CLASSIFICATION = 'classification',
  REGRESSION = 'regression',
  CLUSTERING = 'clustering',
  NLP = 'nlp',
  COMPUTER_VISION = 'computer_vision',
  TIME_SERIES = 'time_series',
  RECOMMENDATION = 'recommendation'
}

// AI任务类型
export enum AITaskType {
  STOCK_ANALYSIS = 'stock_analysis',
  PRICE_PREDICTION = 'price_prediction',
  RISK_ASSESSMENT = 'risk_assessment',
  SENTIMENT_ANALYSIS = 'sentiment_analysis',
  PATTERN_RECOGNITION = 'pattern_recognition',
  PORTFOLIO_OPTIMIZATION = 'portfolio_optimization',
  ANOMALY_DETECTION = 'anomaly_detection',
  TEXT_GENERATION = 'text_generation',
  RECOMMENDATION = 'recommendation'
}

// AI推理状态
export enum AIInferenceStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// AI模型配置
export interface AIModelConfig {
  id: string
  name: string
  type: AIModelType
  version: string
  endpoint: string
  inputShape?: number[]
  outputShape?: number[]
  preprocessing?: PreprocessingConfig
  postprocessing?: PostprocessingConfig
  metadata?: Record<string, any>
}

// 预处理配置
export interface PreprocessingConfig {
  normalization?: {
    type: 'standard' | 'minmax' | 'robust'
    params?: Record<string, any>
  }
  featureSelection?: {
    method: 'variance' | 'correlation' | 'mutual_info'
    threshold?: number
  }
  encoding?: {
    categorical?: 'onehot' | 'label' | 'target'
    numerical?: 'standard' | 'robust' | 'quantile'
  }
}

// 后处理配置
export interface PostprocessingConfig {
  activation?: 'softmax' | 'sigmoid' | 'relu' | 'tanh'
  threshold?: number
  scaling?: {
    type: 'linear' | 'log' | 'sqrt'
    factor?: number
  }
  interpretation?: {
    explainable?: boolean
    confidence?: boolean
    probability?: boolean
  }
}

// AI推理请求
export interface AIInferenceRequest {
  id?: string
  modelId: string
  taskType: AITaskType
  input: any
  parameters?: Record<string, any>
  options?: {
    timeout?: number
    priority?: number
    cached?: boolean
    explain?: boolean
  }
  metadata?: Record<string, any>
}

// AI推理响应
export interface AIInferenceResponse<T = any> {
  id: string
  status: AIInferenceStatus
  result?: T
  confidence?: number
  probability?: number[]
  explanation?: AIExplanation
  metrics?: AIMetrics
  error?: string
  timestamp: number
  duration: number
}

// AI解释
export interface AIExplanation {
  type: 'feature_importance' | 'attention' | 'gradient' | 'lime' | 'shap'
  features: Array<{
    name: string
    importance: number
    value: any
    contribution: number
  }>
  reasoning?: string
  confidence_intervals?: Array<{
    feature: string
    lower: number
    upper: number
  }>
}

// AI指标
export interface AIMetrics {
  accuracy?: number
  precision?: number
  recall?: number
  f1_score?: number
  auc_roc?: number
  mse?: number
  mae?: number
  r2_score?: number
  inference_time: number
  memory_usage?: number
}

// 股票AI分析结果
export interface StockAIAnalysis {
  symbol: string
  timestamp: number
  score: number
  grade: 'A+' | 'A' | 'B+' | 'B' | 'C+' | 'C' | 'D'
  recommendation: 'strong_buy' | 'buy' | 'hold' | 'sell' | 'strong_sell'
  confidence: number
  factors: Array<{
    name: string
    value: number
    weight: number
    impact: 'positive' | 'negative' | 'neutral'
    explanation: string
  }>
  technicalAnalysis: {
    trend: 'bullish' | 'bearish' | 'sideways'
    momentum: number
    volatility: number
    support: number
    resistance: number
    patterns: string[]
  }
  fundamentalAnalysis: {
    valuation: 'undervalued' | 'fairly_valued' | 'overvalued'
    growth: number
    profitability: number
    financial_health: number
    competitive_position: number
  }
  sentimentAnalysis: {
    overall: number
    news: number
    social: number
    analyst: number
    insider: number
  }
  riskAssessment: {
    overall_risk: 'low' | 'medium' | 'high'
    market_risk: number
    sector_risk: number
    company_risk: number
    liquidity_risk: number
  }
  predictions: {
    price_target: {
      short_term: number  // 1个月
      medium_term: number // 3个月
      long_term: number   // 12个月
    }
    probability_ranges: Array<{
      timeframe: string
      ranges: Array<{
        min: number
        max: number
        probability: number
      }>
    }>
  }
}

// 投资组合AI优化
export interface PortfolioAIOptimization {
  id: string
  timestamp: number
  objective: 'max_return' | 'min_risk' | 'max_sharpe' | 'custom'
  constraints: {
    max_weight_per_stock?: number
    min_weight_per_stock?: number
    max_sector_weight?: number
    min_cash_ratio?: number
    max_turnover?: number
  }
  current_portfolio: Array<{
    symbol: string
    weight: number
    value: number
  }>
  optimized_portfolio: Array<{
    symbol: string
    current_weight: number
    target_weight: number
    action: 'buy' | 'sell' | 'hold'
    quantity: number
    value: number
  }>
  metrics: {
    expected_return: number
    expected_risk: number
    sharpe_ratio: number
    max_drawdown: number
    var_95: number
    var_99: number
  }
  rebalancing: {
    total_trades: number
    total_cost: number
    turnover_rate: number
    tax_impact: number
  }
}

// AI推荐结果
export interface AIRecommendation {
  id: string
  type: 'stock' | 'portfolio' | 'strategy' | 'action'
  title: string
  description: string
  confidence: number
  priority: 'high' | 'medium' | 'low'
  category: string
  target: {
    symbol?: string
    symbols?: string[]
    action?: string
    parameters?: Record<string, any>
  }
  reasoning: {
    factors: string[]
    analysis: string
    risks: string[]
    opportunities: string[]
  }
  metrics: {
    expected_return?: number
    risk_level?: number
    time_horizon?: string
    success_probability?: number
  }
  timestamp: number
  expires_at?: number
}

// 自然语言查询
export interface NLPQuery {
  id: string
  text: string
  intent: string
  entities: Array<{
    type: string
    value: string
    confidence: number
    start: number
    end: number
  }>
  parameters: Record<string, any>
  context?: Record<string, any>
}

// NLP响应
export interface NLPResponse {
  id: string
  query: NLPQuery
  response: {
    text: string
    data?: any
    charts?: any[]
    actions?: Array<{
      type: string
      label: string
      action: string
      parameters?: Record<string, any>
    }>
  }
  confidence: number
  alternatives?: Array<{
    text: string
    confidence: number
  }>
  timestamp: number
}

// 市场情绪分析
export interface MarketSentiment {
  symbol?: string
  timestamp: number
  overall_sentiment: number // -1 to 1
  sentiment_trend: 'improving' | 'declining' | 'stable'
  sources: {
    news: {
      sentiment: number
      volume: number
      key_topics: string[]
      articles_count: number
    }
    social: {
      sentiment: number
      volume: number
      mentions_count: number
      trending_hashtags: string[]
    }
    analyst: {
      sentiment: number
      ratings_count: number
      upgrades: number
      downgrades: number
    }
    insider: {
      sentiment: number
      transactions_count: number
      buy_sell_ratio: number
    }
  }
  indicators: {
    fear_greed_index: number
    volatility_sentiment: number
    momentum_sentiment: number
    contrarian_indicator: number
  }
}

// AI模型性能监控
export interface AIModelPerformance {
  modelId: string
  timestamp: number
  metrics: {
    inference_count: number
    average_latency: number
    error_rate: number
    accuracy: number
    throughput: number
    memory_usage: number
    cpu_usage: number
  }
  quality_metrics: {
    prediction_accuracy: number
    confidence_calibration: number
    feature_drift: number
    data_drift: number
  }
  business_metrics: {
    user_satisfaction: number
    conversion_rate: number
    engagement_rate: number
    revenue_impact: number
  }
}

// AI服务配置
export interface AIServiceConfig {
  endpoint: string
  apiKey?: string
  timeout: number
  retries: number
  cache: {
    enabled: boolean
    ttl: number
    maxSize: number
  }
  rateLimit: {
    requests: number
    window: number
  }
  monitoring: {
    enabled: boolean
    metricsInterval: number
    alertThresholds: Record<string, number>
  }
}

// AI批处理任务
export interface AIBatchTask {
  id: string
  name: string
  type: AITaskType
  status: 'queued' | 'running' | 'completed' | 'failed'
  inputs: any[]
  outputs?: any[]
  progress: number
  created_at: number
  started_at?: number
  completed_at?: number
  error?: string
  metadata?: Record<string, any>
}

// AI实验配置
export interface AIExperiment {
  id: string
  name: string
  description: string
  models: string[]
  datasets: string[]
  metrics: string[]
  parameters: Record<string, any>
  status: 'draft' | 'running' | 'completed' | 'failed'
  results?: Array<{
    modelId: string
    metrics: Record<string, number>
    predictions?: any[]
  }>
  created_at: number
  updated_at: number
}

// AI接口定义
export interface IAIService {
  inference<T = any>(request: AIInferenceRequest): Promise<AIInferenceResponse<T>>
  batchInference(requests: AIInferenceRequest[]): Promise<AIInferenceResponse[]>
  getModelInfo(modelId: string): Promise<AIModelConfig>
  listModels(): Promise<AIModelConfig[]>
  getPerformance(modelId: string): Promise<AIModelPerformance>
}

export interface IAIAnalyzer {
  analyzeStock(symbol: string, options?: any): Promise<StockAIAnalysis>
  predictPrice(symbol: string, timeframe: string): Promise<number[]>
  assessRisk(portfolio: any[]): Promise<any>
  detectAnomalies(data: any[]): Promise<any[]>
}

export interface IAIRecommender {
  recommendStocks(criteria: any): Promise<AIRecommendation[]>
  optimizePortfolio(portfolio: any[], objective: string): Promise<PortfolioAIOptimization>
  suggestActions(context: any): Promise<AIRecommendation[]>
}

export interface INLPProcessor {
  parseQuery(text: string): Promise<NLPQuery>
  generateResponse(query: NLPQuery): Promise<NLPResponse>
  analyzeSentiment(text: string): Promise<number>
  extractEntities(text: string): Promise<any[]>
}

// 技术分析结果
export interface TechnicalAnalysisResult {
  symbol: string
  timestamp: number
  indicators: TechnicalIndicator[]
  patterns: TechnicalPattern[]
  signals: TechnicalSignal[]
  trendAnalysis: TrendAnalysis
  volumeAnalysis: VolumeAnalysis
  momentumAnalysis: MomentumAnalysis
  volatilityAnalysis: VolatilityAnalysis
  supportResistance: SupportResistanceLevel[]
  overallScore: number
  recommendation: 'STRONG_BUY' | 'BUY' | 'HOLD' | 'SELL' | 'STRONG_SELL'
  confidence: number
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'EXTREME'
}

// 技术指标
export interface TechnicalIndicator {
  name: string
  value: number
  signal: 'BUY' | 'SELL' | 'HOLD'
  strength: number
  description: string
}

// 技术形态
export interface TechnicalPattern {
  name: string
  type: 'BULLISH' | 'BEARISH' | 'NEUTRAL'
  confidence: number
  description: string
  timeframe: string
}

// 技术信号
export interface TechnicalSignal {
  type: string
  direction: 'UP' | 'DOWN'
  strength: number
  confidence: number
  timeframe: string
  description: string
}

// K线形态
export interface CandlestickPattern {
  name: string
  type: 'BULLISH' | 'BEARISH' | 'NEUTRAL'
  reliability: number
  description: string
}

// 支撑阻力位
export interface SupportResistanceLevel {
  level: number
  type: 'SUPPORT' | 'RESISTANCE'
  strength: number
  touches: number
  lastTested: number
}

// 趋势分析
export interface TrendAnalysis {
  shortTerm: {
    direction: 'UP' | 'DOWN' | 'SIDEWAYS'
    strength: number
    angle: number
  }
  mediumTerm: {
    direction: 'UP' | 'DOWN' | 'SIDEWAYS'
    strength: number
    angle: number
  }
  longTerm: {
    direction: 'UP' | 'DOWN' | 'SIDEWAYS'
    strength: number
    angle: number
  }
  overall: {
    direction: 'UP' | 'DOWN' | 'SIDEWAYS'
    strength: number
    reliability: number
  }
}

// 成交量分析
export interface VolumeAnalysis {
  trend: 'INCREASING' | 'DECREASING' | 'STABLE'
  priceVolumeRelation: 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL'
  indicators: any[]
  anomalies: any[]
  averageVolume: number
  relativeVolume: number
}

// 动量分析
export interface MomentumAnalysis {
  priceMomentum: number
  volumeMomentum: number
  indicators: any[]
  divergence: boolean
  strength: number
  direction: 'UP' | 'DOWN' | 'NEUTRAL'
}

// 波动率分析
export interface VolatilityAnalysis {
  current: number
  trend: 'INCREASING' | 'DECREASING' | 'STABLE'
  indicators: any[]
  state: 'LOW' | 'NORMAL' | 'HIGH' | 'EXTREME'
  percentile: number
  forecast: number
}

// 趋势预测
export interface TrendPrediction {
  symbol: string
  timestamp: number
  horizon: number
  prediction: number[]
  confidence: PredictionConfidence
  marketRegime: MarketRegime
  signals: TrendSignal[]
  features: { [key: string]: number }
  models: { [key: string]: number }
  scenarios: any[]
}

// 预测置信度
export interface PredictionConfidence {
  overall: number
  modelAgreement: number
  historicalAccuracy: number
  dataQuality: number
  marketCondition: number
}

// 趋势信号
export interface TrendSignal {
  type: string
  direction: 'UP' | 'DOWN'
  strength: number
  confidence: number
  timeframe: string
  description: string
}

// 市场状态
export interface MarketRegime {
  type: 'BULL' | 'BEAR' | 'SIDEWAYS' | 'VOLATILE'
  strength: number
  duration: number
  characteristics: string[]
  probability: number
}

// 市场趋势分析
export interface MarketTrendAnalysis {
  timestamp: number
  indexPredictions: { [symbol: string]: TrendPrediction }
  marketBreadth: any
  sectorRotation: any
  marketSentiment: any
  volatilityAnalysis: any
  liquidityAnalysis: any
  overallTrend: string
  riskLevel: string
  recommendations: string[]
}

// 风险评估
export interface RiskAssessment {
  symbol: string
  timestamp: number
  overallRiskScore: number
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'EXTREME'
  marketRisk: MarketRisk
  companyRisk: CompanyRisk
  liquidityRisk: LiquidityRisk
  volatilityRisk: VolatilityRisk
  portfolioRisk?: PortfolioRisk
  riskMetrics: RiskMetrics
  scenarios: RiskScenario[]
  alerts: RiskAlert[]
  recommendations: string[]
  nextReviewDate: number
}

// 风险指标
export interface RiskMetrics {
  valueAtRisk: {
    var95: number
    var99: number
  }
  expectedShortfall: {
    es95: number
    es99: number
  }
  maxDrawdown: number
  sharpeRatio: number
  sortinoRatio: number
  calmarRatio: number
  informationRatio: number
  riskAdjustedReturn: number
  downside: {
    downsideDeviation: number
    downsideFrequency: number
  }
}

// 风险情景
export interface RiskScenario {
  name: string
  description: string
  probability: number
  priceImpact: number
  timeframe: string
  triggers: string[]
  potentialLoss: number
  recoveryTime: string
}

// 风险告警
export interface RiskAlert {
  type: string
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  message: string
  timestamp: number
  threshold: number
  currentValue: number
  recommendation: string
}

// 投资组合风险
export interface PortfolioRisk {
  portfolioVolatility: number
  valueAtRisk: number
  expectedShortfall: number
  maxDrawdown: number
  sharpeRatio: number
  beta: number
  riskContribution: { [symbol: string]: number }
  diversificationRatio: number
  correlationMatrix: { [key: string]: { [key: string]: number } }
  concentrationRisk: number
  stressTestResults: any[]
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'EXTREME'
  recommendations: string[]
}

// 市场风险
export interface MarketRisk {
  volatility: number
  beta: number
  correlation: number
  systematicRisk: number
  riskFactors: string[]
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'EXTREME'
  explanation: string
}

// 公司风险
export interface CompanyRisk {
  idiosyncraticRisk: number
  fundamentalRisk: number
  sectorRisk: number
  financialRisk: number
  managementRisk: number
  regulatoryRisk: number
  overallRisk: number
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'EXTREME'
  keyRiskFactors: string[]
}

// 流动性风险
export interface LiquidityRisk {
  avgVolume: number
  volumeVolatility: number
  bidAskSpread: number
  marketDepth: number
  liquidityRatio: number
  impactCost: number
  liquidityScore: number
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'EXTREME'
  explanation: string
}

// 波动率风险
export interface VolatilityRisk {
  historical: number
  implied: number
  clustering: boolean
  forecast: number
  riskPremium: number
  regime: 'LOW' | 'MEDIUM' | 'HIGH'
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'EXTREME'
  explanation: string
}

// 技术数据点
export interface TechnicalDataPoint {
  timestamp: number
  open: number
  high: number
  low: number
  close: number
  volume: number
}

// 特征工程相关类型
export interface FeatureConfig {
  includeBasicFeatures: boolean
  includeTechnicalIndicators: boolean
  includeStatisticalFeatures: boolean
  includeFundamentalFeatures: boolean
  includeMarketMicrostructure: boolean
  includeSentimentFeatures: boolean
  technicalConfig?: TechnicalIndicatorConfig
}

export interface TechnicalIndicatorConfig {
  periods: {
    sma: number[]
    ema: number[]
    rsi: number
    macd: { fast: number, slow: number, signal: number }
    bollinger: { period: number, stdDev: number }
  }
  customIndicators?: string[]
}

export interface FeatureVector {
  symbol: string
  timestamp: number | Date
  features: Record<string, number>
  metadata: {
    featureCount: number
    extractionTime: Date
    config: FeatureConfig
  }
}

export interface FeatureImportance {
  feature: string
  importance: number
  rank: number
  method: FeatureSelectionMethod
}

export interface FeatureEngineeringOptions {
  normalization?: NormalizationMethod
  featureSelection?: FeatureSelectionMethod
  maxFeatures?: number
  cacheResults?: boolean
}

export type FeatureSelectionMethod = 'correlation' | 'mutual_information' | 'chi_square' | 'recursive_elimination' | 'lasso' | 'random_forest'
export type NormalizationMethod = 'standard' | 'minmax' | 'robust'

// 数据管道相关类型
export interface DataPipelineConfig {
  qualityThreshold: number
  timelinessThreshold?: number
  defaultTransformations?: string[]
  cacheConfig?: CacheConfig
  streamingConfig?: StreamingConfig
}

export interface CacheConfig {
  enabled: boolean
  ttl?: number
  maxSize?: number
}

export interface StreamingConfig {
  enabled: boolean
  bufferSize?: number
  batchSize?: number
  flushInterval?: number
}

export interface DataBatch {
  id: string
  data: StockData[]
  transformations?: string[]
  config?: any
  timestamp: Date
  cacheKey?: string
}

export interface ProcessingResult {
  batchId: string
  processedCount: number
  errorCount: number
  warnings: Array<{ message: string, timestamp: Date, context?: string }>
  errors: Array<{ message: string, timestamp: Date, context?: string }>
  metrics: {
    processingTime: number
    throughput: number
    qualityScore: number
  }
  transformedData: StockData[]
}

export interface PipelineMetrics {
  processedRecords: number
  errorCount: number
  averageLatency: number
  throughput: number
  cacheHitRate: number
  qualityScore: number
}

export type DataTransformation = (data: StockData[], config?: any) => Promise<StockData[]>
export type DataQualityCheck = (data: StockData[]) => Promise<any>

// 模型训练相关类型
export interface TrainingConfig {
  modelName: string
  modelType: AIModelType
  epochs?: number
  batchSize?: number
  learningRate?: number
  hyperParameters?: HyperParameters
  validationInterval?: number
  earlyStopping?: {
    patience: number
    minDelta: number
  }
  learningRateScheduler?: {
    type: 'step' | 'exponential' | 'cosine'
    stepSize?: number
    gamma?: number
    minLr?: number
  }
}

export interface TrainingData {
  modelName: string
  features?: string[]
  samples?: Array<{
    id: number | string
    features: number[]
    target: number | string
  }>
  metadata: {
    sampleCount: number
    featureCount: number
    createdAt: Date
  }
}

export interface TrainingJob {
  id: string
  modelName: string
  config: TrainingConfig
  status: 'pending' | 'running' | 'completed' | 'failed' | 'stopped'
  progress: TrainingProgress
  metrics: TrainingMetrics
  validationResult?: ValidationResult
  artifacts: ModelArtifact[]
  logs: string[]
  error?: string
  createdAt: Date
  updatedAt: Date
}

export interface TrainingProgress {
  currentEpoch: number
  totalEpochs: number
  currentLoss: number
  bestLoss: number
  accuracy: number
  validationLoss: number
  validationAccuracy: number
  learningRate: number
  startTime: Date
  endTime?: Date
  estimatedEndTime?: Date
}

export interface TrainingMetrics {
  trainLoss: number[]
  validationLoss: number[]
  accuracy: number[]
  validationAccuracy: number[]
  learningRate: number[]
  epochTimes: number[]
}

export interface ValidationResult {
  jobId: string
  accuracy: number
  precision: number
  recall: number
  f1Score: number
  loss: number
  confusionMatrix: number[][]
  rocAuc: number
  validationTime: number
  timestamp: Date
}

export interface ModelVersion {
  id: string
  modelName: string
  version: string
  description: string
  config: TrainingConfig
  metrics: TrainingMetrics
  validationResult?: ValidationResult
  artifacts: ModelArtifact[]
  status: 'active' | 'inactive' | 'deployed'
  createdAt: Date
  createdBy: string
  deployedAt?: Date
}

export interface ModelArtifact {
  id: string
  jobId: string
  type: 'model' | 'checkpoint' | 'config' | 'metrics'
  name: string
  path: string
  size: number
  metrics?: any
  createdAt: Date
}

export interface HyperParameters {
  [key: string]: any
}

export interface ExperimentConfig {
  id: string
  modelName: string
  type: 'hyperparameter_optimization' | 'feature_selection' | 'architecture_search'
  parameterSpace: Record<string, any[]>
  maxTrials: number
  trials: Array<{
    id: number
    parameters: HyperParameters
    score: number
    timestamp: Date
  }>
  bestTrial?: {
    id: number
    parameters: HyperParameters
    score: number
    timestamp: Date
  }
  status: 'pending' | 'running' | 'completed' | 'failed'
  error?: string
  createdAt: Date
  completedAt?: Date
}

// 导出所有类型
export * from './models'
export * from './analysis'
export * from './nlp' 