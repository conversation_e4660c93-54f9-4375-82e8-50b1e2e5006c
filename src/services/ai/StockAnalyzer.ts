import {
  StockAIAnalysis,
  AIInferenceRequest,
  AITaskType,
  MarketSentiment,
  IAIAnalyzer
} from '@/types/ai'
import { aiService } from './AIService'

/**
 * 股票智能分析器
 * 提供AI驱动的股票分析、评分、预测等功能
 */
export class StockAnalyzer implements IAIAnalyzer {
  private readonly MODELS = {
    STOCK_SCORING: 'stock_scoring_v2',
    TECHNICAL_ANALYSIS: 'technical_analysis_v1',
    FUNDAMENTAL_ANALYSIS: 'fundamental_analysis_v1',
    SENTIMENT_ANALYSIS: 'sentiment_analysis_v1',
    PRICE_PREDICTION: 'price_prediction_lstm_v2',
    RISK_ASSESSMENT: 'risk_assessment_v1',
    PATTERN_RECOGNITION: 'pattern_recognition_v1'
  }

  /**
   * 综合股票分析
   */
  async analyzeStock(symbol: string, options: any = {}): Promise<StockAIAnalysis> {
    try {
      console.log(`开始分析股票: ${symbol}`)
      
      // 并行执行多个分析任务
      const [
        technicalResult,
        fundamentalResult,
        sentimentResult,
        riskResult,
        predictionResult
      ] = await Promise.all([
        this.performTechnicalAnalysis(symbol, options),
        this.performFundamentalAnalysis(symbol, options),
        this.performSentimentAnalysis(symbol, options),
        this.assessRisk(symbol, options),
        this.predictPrice(symbol, options.timeframe || '1M')
      ])

      // 综合评分计算
      const overallScore = this.calculateOverallScore({
        technical: technicalResult,
        fundamental: fundamentalResult,
        sentiment: sentimentResult,
        risk: riskResult
      })

      // 生成投资建议
      const recommendation = this.generateRecommendation(overallScore, riskResult)

      // 构建完整分析结果
      const analysis: StockAIAnalysis = {
        symbol,
        timestamp: Date.now(),
        score: overallScore.score,
        grade: overallScore.grade,
        recommendation: recommendation.action,
        confidence: overallScore.confidence,
        factors: overallScore.factors,
        technicalAnalysis: technicalResult,
        fundamentalAnalysis: fundamentalResult,
        sentimentAnalysis: sentimentResult,
        riskAssessment: riskResult,
        predictions: {
          price_target: predictionResult.targets,
          probability_ranges: predictionResult.ranges
        }
      }

      console.log(`股票分析完成: ${symbol}, 评分: ${overallScore.score}`)
      return analysis

    } catch (error) {
      console.error(`股票分析失败 ${symbol}:`, error)
      throw new Error(`股票分析失败: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * 技术分析
   */
  private async performTechnicalAnalysis(symbol: string, options: any): Promise<any> {
    const request: AIInferenceRequest = {
      modelId: this.MODELS.TECHNICAL_ANALYSIS,
      taskType: AITaskType.PATTERN_RECOGNITION,
      input: {
        symbol,
        timeframe: options.timeframe || '1D',
        indicators: options.indicators || ['MA', 'RSI', 'MACD', 'BB', 'KDJ'],
        period: options.period || 60
      },
      options: {
        cached: true,
        explain: true
      }
    }

    const response = await aiService.inference(request)
    
    if (response.status !== 'completed' || !response.result) {
      throw new Error('技术分析失败')
    }

    return {
      trend: response.result.trend || 'sideways',
      momentum: response.result.momentum || 0,
      volatility: response.result.volatility || 0,
      support: response.result.support || 0,
      resistance: response.result.resistance || 0,
      patterns: response.result.patterns || [],
      indicators: response.result.indicators || {},
      signals: response.result.signals || []
    }
  }

  /**
   * 基本面分析
   */
  private async performFundamentalAnalysis(symbol: string, options: any): Promise<any> {
    const request: AIInferenceRequest = {
      modelId: this.MODELS.FUNDAMENTAL_ANALYSIS,
      taskType: AITaskType.STOCK_ANALYSIS,
      input: {
        symbol,
        metrics: options.metrics || ['PE', 'PB', 'ROE', 'ROA', 'DEBT_RATIO', 'GROWTH_RATE'],
        period: options.period || 'TTM'
      },
      options: {
        cached: true,
        explain: true
      }
    }

    const response = await aiService.inference(request)
    
    if (response.status !== 'completed' || !response.result) {
      throw new Error('基本面分析失败')
    }

    return {
      valuation: response.result.valuation || 'fairly_valued',
      growth: response.result.growth_score || 0,
      profitability: response.result.profitability_score || 0,
      financial_health: response.result.financial_health_score || 0,
      competitive_position: response.result.competitive_score || 0,
      metrics: response.result.metrics || {},
      ratios: response.result.ratios || {}
    }
  }

  /**
   * 情绪分析
   */
  private async performSentimentAnalysis(symbol: string, options: any): Promise<any> {
    const request: AIInferenceRequest = {
      modelId: this.MODELS.SENTIMENT_ANALYSIS,
      taskType: AITaskType.SENTIMENT_ANALYSIS,
      input: {
        symbol,
        sources: options.sources || ['news', 'social', 'analyst', 'insider'],
        timeframe: options.timeframe || '7D'
      },
      options: {
        cached: true
      }
    }

    const response = await aiService.inference(request)
    
    if (response.status !== 'completed' || !response.result) {
      throw new Error('情绪分析失败')
    }

    return {
      overall: response.result.overall_sentiment || 0,
      news: response.result.news_sentiment || 0,
      social: response.result.social_sentiment || 0,
      analyst: response.result.analyst_sentiment || 0,
      insider: response.result.insider_sentiment || 0,
      trend: response.result.sentiment_trend || 'stable',
      key_factors: response.result.key_factors || []
    }
  }

  /**
   * 风险评估
   */
  async assessRisk(portfolio: any[], options: any = {}): Promise<any> {
    const request: AIInferenceRequest = {
      modelId: this.MODELS.RISK_ASSESSMENT,
      taskType: AITaskType.RISK_ASSESSMENT,
      input: {
        portfolio: Array.isArray(portfolio) ? portfolio : [{ symbol: portfolio, weight: 1 }],
        timeframe: options.timeframe || '1Y',
        confidence_level: options.confidence || 0.95
      },
      options: {
        cached: true,
        explain: true
      }
    }

    const response = await aiService.inference(request)
    
    if (response.status !== 'completed' || !response.result) {
      throw new Error('风险评估失败')
    }

    return {
      overall_risk: response.result.risk_level || 'medium',
      market_risk: response.result.market_risk || 0,
      sector_risk: response.result.sector_risk || 0,
      company_risk: response.result.company_risk || 0,
      liquidity_risk: response.result.liquidity_risk || 0,
      var_95: response.result.var_95 || 0,
      var_99: response.result.var_99 || 0,
      max_drawdown: response.result.max_drawdown || 0,
      beta: response.result.beta || 1,
      volatility: response.result.volatility || 0
    }
  }

  /**
   * 价格预测
   */
  async predictPrice(symbol: string, timeframe: string): Promise<any> {
    const request: AIInferenceRequest = {
      modelId: this.MODELS.PRICE_PREDICTION,
      taskType: AITaskType.PRICE_PREDICTION,
      input: {
        symbol,
        timeframe,
        features: ['price', 'volume', 'technical_indicators', 'market_data'],
        prediction_horizons: ['1M', '3M', '12M']
      },
      options: {
        cached: true,
        explain: true
      }
    }

    const response = await aiService.inference(request)
    
    if (response.status !== 'completed' || !response.result) {
      throw new Error('价格预测失败')
    }

    return {
      targets: {
        short_term: response.result.predictions['1M'] || 0,
        medium_term: response.result.predictions['3M'] || 0,
        long_term: response.result.predictions['12M'] || 0
      },
      ranges: response.result.probability_ranges || [],
      confidence: response.confidence || 0,
      factors: response.result.important_features || []
    }
  }

  /**
   * 异常检测
   */
  async detectAnomalies(data: any[]): Promise<any[]> {
    const request: AIInferenceRequest = {
      modelId: this.MODELS.PATTERN_RECOGNITION,
      taskType: AITaskType.ANOMALY_DETECTION,
      input: {
        data,
        sensitivity: 0.95,
        method: 'isolation_forest'
      },
      options: {
        cached: false
      }
    }

    const response = await aiService.inference(request)
    
    if (response.status !== 'completed' || !response.result) {
      throw new Error('异常检测失败')
    }

    return response.result.anomalies || []
  }

  /**
   * 计算综合评分
   */
  private calculateOverallScore(analyses: any): any {
    const weights = {
      technical: 0.3,
      fundamental: 0.4,
      sentiment: 0.2,
      risk: 0.1
    }

    // 技术分析评分
    const technicalScore = this.calculateTechnicalScore(analyses.technical)
    
    // 基本面评分
    const fundamentalScore = this.calculateFundamentalScore(analyses.fundamental)
    
    // 情绪评分
    const sentimentScore = this.calculateSentimentScore(analyses.sentiment)
    
    // 风险调整评分
    const riskAdjustment = this.calculateRiskAdjustment(analyses.risk)

    // 加权综合评分
    const overallScore = 
      technicalScore * weights.technical +
      fundamentalScore * weights.fundamental +
      sentimentScore * weights.sentiment -
      riskAdjustment * weights.risk

    // 标准化到0-100分
    const normalizedScore = Math.max(0, Math.min(100, overallScore))

    // 计算等级
    const grade = this.calculateGrade(normalizedScore)

    // 计算置信度
    const confidence = this.calculateConfidence(analyses)

    // 生成因子分析
    const factors = this.generateFactors(analyses, weights)

    return {
      score: normalizedScore,
      grade,
      confidence,
      factors,
      breakdown: {
        technical: technicalScore,
        fundamental: fundamentalScore,
        sentiment: sentimentScore,
        risk: riskAdjustment
      }
    }
  }

  /**
   * 计算技术分析评分
   */
  private calculateTechnicalScore(technical: any): number {
    let score = 50 // 基础分数

    // 趋势评分
    if (technical.trend === 'bullish') score += 20
    else if (technical.trend === 'bearish') score -= 20

    // 动量评分
    score += technical.momentum * 10

    // 模式识别评分
    if (technical.patterns) {
      const bullishPatterns = technical.patterns.filter((p: string) => 
        p.includes('bullish') || p.includes('ascending') || p.includes('cup')
      ).length
      const bearishPatterns = technical.patterns.filter((p: string) => 
        p.includes('bearish') || p.includes('descending') || p.includes('head')
      ).length
      
      score += (bullishPatterns - bearishPatterns) * 5
    }

    return Math.max(0, Math.min(100, score))
  }

  /**
   * 计算基本面评分
   */
  private calculateFundamentalScore(fundamental: any): number {
    let score = 50 // 基础分数

    // 估值评分
    if (fundamental.valuation === 'undervalued') score += 15
    else if (fundamental.valuation === 'overvalued') score -= 15

    // 成长性评分
    score += fundamental.growth * 20

    // 盈利能力评分
    score += fundamental.profitability * 15

    // 财务健康度评分
    score += fundamental.financial_health * 10

    // 竞争地位评分
    score += fundamental.competitive_position * 10

    return Math.max(0, Math.min(100, score))
  }

  /**
   * 计算情绪评分
   */
  private calculateSentimentScore(sentiment: any): number {
    let score = 50 // 基础分数

    // 综合情绪评分
    score += sentiment.overall * 30

    // 新闻情绪评分
    score += sentiment.news * 10

    // 社交媒体情绪评分
    score += sentiment.social * 5

    // 分析师情绪评分
    score += sentiment.analyst * 10

    // 内部人士情绪评分
    score += sentiment.insider * 5

    return Math.max(0, Math.min(100, score))
  }

  /**
   * 计算风险调整
   */
  private calculateRiskAdjustment(risk: any): number {
    let adjustment = 0

    // 总体风险调整
    if (risk.overall_risk === 'high') adjustment += 20
    else if (risk.overall_risk === 'medium') adjustment += 10
    else adjustment += 5

    // 波动率调整
    adjustment += risk.volatility * 10

    // Beta调整
    if (risk.beta > 1.5) adjustment += 10
    else if (risk.beta < 0.5) adjustment += 5

    return Math.max(0, adjustment)
  }

  /**
   * 计算等级
   */
  private calculateGrade(score: number): StockAIAnalysis['grade'] {
    if (score >= 90) return 'A+'
    if (score >= 80) return 'A'
    if (score >= 70) return 'B+'
    if (score >= 60) return 'B'
    if (score >= 50) return 'C+'
    if (score >= 40) return 'C'
    return 'D'
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(analyses: any): number {
    // 基于各分析结果的一致性计算置信度
    let confidence = 0.5

    // 技术分析置信度
    if (analyses.technical.momentum > 0.5) confidence += 0.1
    if (analyses.technical.patterns && analyses.technical.patterns.length > 0) confidence += 0.1

    // 基本面分析置信度
    if (analyses.fundamental.financial_health > 0.7) confidence += 0.1
    if (analyses.fundamental.growth > 0.6) confidence += 0.1

    // 情绪分析置信度
    if (Math.abs(analyses.sentiment.overall) > 0.3) confidence += 0.1

    return Math.max(0, Math.min(1, confidence))
  }

  /**
   * 生成因子分析
   */
  private generateFactors(analyses: any, weights: any): any[] {
    const factors = []

    // 技术因子
    factors.push({
      name: '技术趋势',
      value: this.calculateTechnicalScore(analyses.technical),
      weight: weights.technical,
      impact: analyses.technical.trend === 'bullish' ? 'positive' : 
              analyses.technical.trend === 'bearish' ? 'negative' : 'neutral',
      explanation: `技术分析显示${analyses.technical.trend}趋势，动量指标为${analyses.technical.momentum.toFixed(2)}`
    })

    // 基本面因子
    factors.push({
      name: '基本面',
      value: this.calculateFundamentalScore(analyses.fundamental),
      weight: weights.fundamental,
      impact: analyses.fundamental.valuation === 'undervalued' ? 'positive' : 
              analyses.fundamental.valuation === 'overvalued' ? 'negative' : 'neutral',
      explanation: `估值${analyses.fundamental.valuation}，成长性评分${(analyses.fundamental.growth * 100).toFixed(0)}分`
    })

    // 情绪因子
    factors.push({
      name: '市场情绪',
      value: this.calculateSentimentScore(analyses.sentiment),
      weight: weights.sentiment,
      impact: analyses.sentiment.overall > 0.1 ? 'positive' : 
              analyses.sentiment.overall < -0.1 ? 'negative' : 'neutral',
      explanation: `市场情绪${analyses.sentiment.overall > 0 ? '积极' : analyses.sentiment.overall < 0 ? '消极' : '中性'}`
    })

    // 风险因子
    factors.push({
      name: '风险水平',
      value: 100 - this.calculateRiskAdjustment(analyses.risk),
      weight: weights.risk,
      impact: analyses.risk.overall_risk === 'low' ? 'positive' : 
              analyses.risk.overall_risk === 'high' ? 'negative' : 'neutral',
      explanation: `风险水平${analyses.risk.overall_risk}，波动率${(analyses.risk.volatility * 100).toFixed(1)}%`
    })

    return factors
  }

  /**
   * 生成投资建议
   */
  private generateRecommendation(overallScore: any, risk: any): any {
    const score = overallScore.score
    const riskLevel = risk.overall_risk

    if (score >= 80 && riskLevel !== 'high') {
      return { action: 'strong_buy', reason: '综合评分优秀，风险可控' }
    } else if (score >= 70 && riskLevel === 'low') {
      return { action: 'buy', reason: '评分良好，风险较低' }
    } else if (score >= 60 && riskLevel !== 'high') {
      return { action: 'buy', reason: '评分中等，适度买入' }
    } else if (score >= 40 && score < 60) {
      return { action: 'hold', reason: '评分中等，建议持有观望' }
    } else if (score >= 30 && riskLevel === 'high') {
      return { action: 'sell', reason: '评分偏低，风险较高' }
    } else {
      return { action: 'strong_sell', reason: '评分较低，建议减仓' }
    }
  }

  /**
   * 批量股票分析
   */
  async batchAnalyzeStocks(symbols: string[], options: any = {}): Promise<StockAIAnalysis[]> {
    const batchSize = options.batchSize || 10
    const results: StockAIAnalysis[] = []

    for (let i = 0; i < symbols.length; i += batchSize) {
      const batch = symbols.slice(i, i + batchSize)
      const batchResults = await Promise.all(
        batch.map(symbol => this.analyzeStock(symbol, options))
      )
      results.push(...batchResults)
    }

    return results
  }

  /**
   * 获取市场整体情绪
   */
  async getMarketSentiment(options: any = {}): Promise<MarketSentiment> {
    const request: AIInferenceRequest = {
      modelId: this.MODELS.SENTIMENT_ANALYSIS,
      taskType: AITaskType.SENTIMENT_ANALYSIS,
      input: {
        scope: 'market',
        indices: options.indices || ['SPY', 'QQQ', 'IWM'],
        timeframe: options.timeframe || '1D'
      },
      options: {
        cached: true
      }
    }

    const response = await aiService.inference(request)
    
    if (response.status !== 'completed' || !response.result) {
      throw new Error('市场情绪分析失败')
    }

    return {
      timestamp: Date.now(),
      overall_sentiment: response.result.overall_sentiment || 0,
      sentiment_trend: response.result.sentiment_trend || 'stable',
      sources: response.result.sources || {},
      indicators: response.result.indicators || {}
    }
  }
}

// 创建默认股票分析器实例
export const stockAnalyzer = new StockAnalyzer() 