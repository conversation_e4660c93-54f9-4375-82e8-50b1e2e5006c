import type {
  StockData,
  DataPipelineConfig,
  DataTransformation,
  DataQualityCheck,
  CacheConfig,
  StreamingConfig,
  PipelineMetrics,
  DataBatch,
  ProcessingResult
} from '@/types/ai'

/**
 * 数据处理管道服务
 * 负责实时数据流处理、转换和缓存
 */
export class DataPipeline {
  private transformations: Map<string, DataTransformation> = new Map()
  private qualityChecks: Map<string, DataQualityCheck> = new Map()
  private cache: Map<string, any> = new Map()
  private streamingHandlers: Map<string, Function> = new Map()
  private metrics: PipelineMetrics = {
    processedRecords: 0,
    errorCount: 0,
    averageLatency: 0,
    throughput: 0,
    cacheHitRate: 0,
    qualityScore: 0
  }
  private isRunning: boolean = false
  private processingQueue: DataBatch[] = []

  constructor(private config: DataPipelineConfig) {
    this.initializePipeline()
  }

  /**
   * 初始化数据管道
   */
  private initializePipeline(): void {
    // 注册默认转换器
    this.registerTransformation('normalize', this.normalizeData.bind(this))
    this.registerTransformation('aggregate', this.aggregateData.bind(this))
    this.registerTransformation('filter', this.filterData.bind(this))
    this.registerTransformation('enrich', this.enrichData.bind(this))

    // 注册默认质量检查
    this.registerQualityCheck('completeness', this.checkCompleteness.bind(this))
    this.registerQualityCheck('accuracy', this.checkAccuracy.bind(this))
    this.registerQualityCheck('consistency', this.checkConsistency.bind(this))
    this.registerQualityCheck('timeliness', this.checkTimeliness.bind(this))

    // 初始化缓存
    if (this.config.cacheConfig?.enabled) {
      this.initializeCache()
    }

    // 初始化流处理
    if (this.config.streamingConfig?.enabled) {
      this.initializeStreaming()
    }
  }

  /**
   * 启动数据管道
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      throw new Error('Data pipeline is already running')
    }

    console.log('Starting data pipeline...')
    this.isRunning = true

    // 启动处理循环
    this.startProcessingLoop()

    // 启动流处理
    if (this.config.streamingConfig?.enabled) {
      await this.startStreaming()
    }

    console.log('Data pipeline started successfully')
  }

  /**
   * 停止数据管道
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return
    }

    console.log('Stopping data pipeline...')
    this.isRunning = false

    // 处理剩余队列
    await this.processRemainingQueue()

    // 停止流处理
    if (this.config.streamingConfig?.enabled) {
      await this.stopStreaming()
    }

    console.log('Data pipeline stopped')
  }

  /**
   * 处理数据批次
   */
  async processBatch(batch: DataBatch): Promise<ProcessingResult> {
    const startTime = Date.now()
    const result: ProcessingResult = {
      batchId: batch.id,
      processedCount: 0,
      errorCount: 0,
      warnings: [],
      errors: [],
      metrics: {
        processingTime: 0,
        throughput: 0,
        qualityScore: 0
      },
      transformedData: []
    }

    try {
      // 数据质量检查
      const qualityResults = await this.runQualityChecks(batch.data)
      result.metrics.qualityScore = this.calculateQualityScore(qualityResults)

      // 应用数据转换
      let transformedData = batch.data
      for (const transformationName of batch.transformations || []) {
        const transformation = this.transformations.get(transformationName)
        if (transformation) {
          transformedData = await transformation(transformedData, batch.config)
        }
      }

      result.transformedData = transformedData
      result.processedCount = transformedData.length

      // 缓存结果
      if (this.config.cacheConfig?.enabled && batch.cacheKey) {
        await this.cacheResult(batch.cacheKey, transformedData)
      }

      // 更新指标
      const processingTime = Date.now() - startTime
      result.metrics.processingTime = processingTime
      result.metrics.throughput = transformedData.length / (processingTime / 1000)

      this.updateMetrics(result)

    } catch (error) {
      result.errorCount++
      result.errors.push({
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
        context: batch.id
      })
      
      this.metrics.errorCount++
    }

    return result
  }

  /**
   * 添加数据到处理队列
   */
  async addToQueue(data: StockData[], transformations?: string[], config?: any): Promise<string> {
    const batchId = this.generateBatchId()
    const batch: DataBatch = {
      id: batchId,
      data,
      transformations,
      config,
      timestamp: new Date(),
      cacheKey: this.generateCacheKey(data, transformations)
    }

    this.processingQueue.push(batch)
    return batchId
  }

  /**
   * 实时数据处理
   */
  async processRealtime(data: StockData): Promise<StockData> {
    const startTime = Date.now()

    try {
      // 检查缓存
      const cacheKey = this.generateCacheKey([data])
      if (this.config.cacheConfig?.enabled) {
        const cachedResult = await this.getCachedResult(cacheKey)
        if (cachedResult) {
          this.metrics.cacheHitRate = (this.metrics.cacheHitRate * 0.9) + (1 * 0.1)
          return cachedResult
        }
      }

      // 质量检查
      const qualityResults = await this.runQualityChecks([data])
      if (this.calculateQualityScore(qualityResults) < this.config.qualityThreshold) {
        throw new Error('Data quality below threshold')
      }

      // 应用默认转换
      let processedData = data
      for (const transformationName of this.config.defaultTransformations || []) {
        const transformation = this.transformations.get(transformationName)
        if (transformation) {
          const result = await transformation([processedData])
          processedData = result[0]
        }
      }

      // 缓存结果
      if (this.config.cacheConfig?.enabled) {
        await this.cacheResult(cacheKey, processedData)
      }

      // 更新指标
      const processingTime = Date.now() - startTime
      this.metrics.averageLatency = (this.metrics.averageLatency * 0.9) + (processingTime * 0.1)
      this.metrics.processedRecords++

      return processedData

    } catch (error) {
      this.metrics.errorCount++
      throw error
    }
  }

  /**
   * 注册数据转换器
   */
  registerTransformation(name: string, transformation: DataTransformation): void {
    this.transformations.set(name, transformation)
  }

  /**
   * 注册质量检查
   */
  registerQualityCheck(name: string, check: DataQualityCheck): void {
    this.qualityChecks.set(name, check)
  }

  /**
   * 获取管道指标
   */
  getMetrics(): PipelineMetrics {
    return { ...this.metrics }
  }

  // 私有方法实现

  /**
   * 启动处理循环
   */
  private startProcessingLoop(): void {
    const processLoop = async () => {
      while (this.isRunning) {
        if (this.processingQueue.length > 0) {
          const batch = this.processingQueue.shift()
          if (batch) {
            try {
              await this.processBatch(batch)
            } catch (error) {
              console.error('Error processing batch:', error)
            }
          }
        } else {
          // 等待新数据
          await new Promise(resolve => setTimeout(resolve, 100))
        }
      }
    }

    processLoop()
  }

  /**
   * 处理剩余队列
   */
  private async processRemainingQueue(): Promise<void> {
    while (this.processingQueue.length > 0) {
      const batch = this.processingQueue.shift()
      if (batch) {
        await this.processBatch(batch)
      }
    }
  }

  /**
   * 初始化缓存
   */
  private initializeCache(): void {
    const cacheConfig = this.config.cacheConfig!
    
    // 设置缓存清理定时器
    if (cacheConfig.ttl) {
      setInterval(() => {
        this.cleanupCache()
      }, cacheConfig.ttl * 1000)
    }

    // 设置最大缓存大小检查
    if (cacheConfig.maxSize) {
      setInterval(() => {
        this.enforceMaxCacheSize()
      }, 60000) // 每分钟检查一次
    }
  }

  /**
   * 初始化流处理
   */
  private initializeStreaming(): void {
    const streamConfig = this.config.streamingConfig!
    
    // 注册流处理器
    this.streamingHandlers.set('stock_data', this.handleStockDataStream.bind(this))
    this.streamingHandlers.set('market_data', this.handleMarketDataStream.bind(this))
    this.streamingHandlers.set('news_data', this.handleNewsDataStream.bind(this))
  }

  /**
   * 启动流处理
   */
  private async startStreaming(): Promise<void> {
    // 这里应该连接到实际的数据流（如WebSocket、Kafka等）
    console.log('Starting streaming processors...')
    
    // 模拟流处理启动
    for (const [streamName, handler] of this.streamingHandlers) {
      console.log(`Starting stream handler: ${streamName}`)
      // 在实际实现中，这里会启动相应的流连接
    }
  }

  /**
   * 停止流处理
   */
  private async stopStreaming(): Promise<void> {
    console.log('Stopping streaming processors...')
    
    // 关闭流连接
    for (const streamName of this.streamingHandlers.keys()) {
      console.log(`Stopping stream handler: ${streamName}`)
      // 在实际实现中，这里会关闭相应的流连接
    }
  }

  /**
   * 运行质量检查
   */
  private async runQualityChecks(data: StockData[]): Promise<Map<string, any>> {
    const results = new Map<string, any>()

    for (const [checkName, check] of this.qualityChecks) {
      try {
        const result = await check(data)
        results.set(checkName, result)
      } catch (error) {
        console.error(`Quality check ${checkName} failed:`, error)
        results.set(checkName, { passed: false, error: error.message })
      }
    }

    return results
  }

  /**
   * 计算质量分数
   */
  private calculateQualityScore(qualityResults: Map<string, any>): number {
    let totalScore = 0
    let checkCount = 0

    for (const [checkName, result] of qualityResults) {
      if (result.score !== undefined) {
        totalScore += result.score
        checkCount++
      } else if (result.passed !== undefined) {
        totalScore += result.passed ? 1 : 0
        checkCount++
      }
    }

    return checkCount > 0 ? totalScore / checkCount : 0
  }

  /**
   * 缓存结果
   */
  private async cacheResult(key: string, data: any): Promise<void> {
    const cacheEntry = {
      data,
      timestamp: Date.now(),
      ttl: this.config.cacheConfig?.ttl || 3600
    }

    this.cache.set(key, cacheEntry)
  }

  /**
   * 获取缓存结果
   */
  private async getCachedResult(key: string): Promise<any> {
    const entry = this.cache.get(key)
    if (!entry) {
      return null
    }

    // 检查TTL
    const now = Date.now()
    if (entry.ttl && (now - entry.timestamp) > (entry.ttl * 1000)) {
      this.cache.delete(key)
      return null
    }

    return entry.data
  }

  /**
   * 清理过期缓存
   */
  private cleanupCache(): void {
    const now = Date.now()
    const keysToDelete: string[] = []

    for (const [key, entry] of this.cache) {
      if (entry.ttl && (now - entry.timestamp) > (entry.ttl * 1000)) {
        keysToDelete.push(key)
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key))
  }

  /**
   * 强制执行最大缓存大小
   */
  private enforceMaxCacheSize(): void {
    const maxSize = this.config.cacheConfig?.maxSize
    if (!maxSize || this.cache.size <= maxSize) {
      return
    }

    // 删除最旧的条目
    const entries = Array.from(this.cache.entries())
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp)

    const deleteCount = this.cache.size - maxSize
    for (let i = 0; i < deleteCount; i++) {
      this.cache.delete(entries[i][0])
    }
  }

  /**
   * 更新指标
   */
  private updateMetrics(result: ProcessingResult): void {
    this.metrics.processedRecords += result.processedCount
    this.metrics.errorCount += result.errorCount
    
    // 更新平均延迟
    if (result.metrics.processingTime) {
      this.metrics.averageLatency = (this.metrics.averageLatency * 0.9) + (result.metrics.processingTime * 0.1)
    }
    
    // 更新吞吐量
    if (result.metrics.throughput) {
      this.metrics.throughput = (this.metrics.throughput * 0.9) + (result.metrics.throughput * 0.1)
    }
    
    // 更新质量分数
    if (result.metrics.qualityScore) {
      this.metrics.qualityScore = (this.metrics.qualityScore * 0.9) + (result.metrics.qualityScore * 0.1)
    }
  }

  /**
   * 生成批次ID
   */
  private generateBatchId(): string {
    return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(data: StockData[], transformations?: string[]): string {
    const dataHash = data.map(d => `${d.symbol}_${d.timestamp}`).join('|')
    const transformHash = transformations?.join('|') || ''
    return `${dataHash}_${transformHash}`
  }

  // 默认数据转换器实现

  /**
   * 数据标准化转换
   */
  private async normalizeData(data: StockData[], config?: any): Promise<StockData[]> {
    return data.map(item => ({
      ...item,
      // 价格标准化
      open: this.normalizeValue(item.open, config?.priceRange),
      high: this.normalizeValue(item.high, config?.priceRange),
      low: this.normalizeValue(item.low, config?.priceRange),
      close: this.normalizeValue(item.close, config?.priceRange),
      // 成交量标准化
      volume: this.normalizeValue(item.volume, config?.volumeRange)
    }))
  }

  /**
   * 数据聚合转换
   */
  private async aggregateData(data: StockData[], config?: any): Promise<StockData[]> {
    const interval = config?.interval || '1h'
    const aggregatedData: StockData[] = []

    // 简化的聚合逻辑
    const groups = this.groupDataByInterval(data, interval)
    
    for (const group of groups) {
      const aggregated: StockData = {
        symbol: group[0].symbol,
        timestamp: group[0].timestamp,
        open: group[0].open,
        high: Math.max(...group.map(d => d.high)),
        low: Math.min(...group.map(d => d.low)),
        close: group[group.length - 1].close,
        volume: group.reduce((sum, d) => sum + d.volume, 0)
      }
      
      aggregatedData.push(aggregated)
    }

    return aggregatedData
  }

  /**
   * 数据过滤转换
   */
  private async filterData(data: StockData[], config?: any): Promise<StockData[]> {
    let filtered = data

    // 按价格范围过滤
    if (config?.priceRange) {
      const { min, max } = config.priceRange
      filtered = filtered.filter(d => d.close >= min && d.close <= max)
    }

    // 按成交量过滤
    if (config?.volumeThreshold) {
      filtered = filtered.filter(d => d.volume >= config.volumeThreshold)
    }

    // 按时间范围过滤
    if (config?.timeRange) {
      const { start, end } = config.timeRange
      filtered = filtered.filter(d => d.timestamp >= start && d.timestamp <= end)
    }

    return filtered
  }

  /**
   * 数据丰富化转换
   */
  private async enrichData(data: StockData[], config?: any): Promise<StockData[]> {
    return data.map(item => ({
      ...item,
      // 添加计算字段
      priceChange: item.close - item.open,
      priceChangePercent: ((item.close - item.open) / item.open) * 100,
      highLowRange: item.high - item.low,
      volumeWeightedPrice: (item.high + item.low + item.close) / 3,
      // 添加时间戳相关字段
      hour: new Date(item.timestamp).getHours(),
      dayOfWeek: new Date(item.timestamp).getDay(),
      // 添加技术指标（简化版）
      sma5: this.calculateSimpleSMA(data, item.symbol, 5),
      rsi: this.calculateSimpleRSI(data, item.symbol, 14)
    }))
  }

  // 默认质量检查实现

  /**
   * 完整性检查
   */
  private async checkCompleteness(data: StockData[]): Promise<any> {
    const requiredFields = ['symbol', 'timestamp', 'open', 'high', 'low', 'close', 'volume']
    let completeRecords = 0

    for (const record of data) {
      const isComplete = requiredFields.every(field => 
        record[field as keyof StockData] !== undefined && 
        record[field as keyof StockData] !== null
      )
      if (isComplete) completeRecords++
    }

    const completeness = data.length > 0 ? completeRecords / data.length : 0

    return {
      passed: completeness >= 0.95,
      score: completeness,
      details: {
        totalRecords: data.length,
        completeRecords,
        completeness
      }
    }
  }

  /**
   * 准确性检查
   */
  private async checkAccuracy(data: StockData[]): Promise<any> {
    let accurateRecords = 0

    for (const record of data) {
      // 价格逻辑检查
      const priceLogical = record.high >= record.low && 
                          record.high >= record.open && 
                          record.high >= record.close &&
                          record.low <= record.open && 
                          record.low <= record.close

      // 成交量检查
      const volumeValid = record.volume >= 0

      // 时间戳检查
      const timestampValid = record.timestamp && !isNaN(new Date(record.timestamp).getTime())

      if (priceLogical && volumeValid && timestampValid) {
        accurateRecords++
      }
    }

    const accuracy = data.length > 0 ? accurateRecords / data.length : 0

    return {
      passed: accuracy >= 0.98,
      score: accuracy,
      details: {
        totalRecords: data.length,
        accurateRecords,
        accuracy
      }
    }
  }

  /**
   * 一致性检查
   */
  private async checkConsistency(data: StockData[]): Promise<any> {
    // 检查数据格式一致性
    const symbols = new Set(data.map(d => typeof d.symbol))
    const timestamps = new Set(data.map(d => typeof d.timestamp))
    const prices = new Set(data.map(d => typeof d.close))

    const consistency = (symbols.size === 1 && timestamps.size === 1 && prices.size === 1) ? 1 : 0

    return {
      passed: consistency === 1,
      score: consistency,
      details: {
        symbolTypes: Array.from(symbols),
        timestampTypes: Array.from(timestamps),
        priceTypes: Array.from(prices)
      }
    }
  }

  /**
   * 及时性检查
   */
  private async checkTimeliness(data: StockData[]): Promise<any> {
    const now = Date.now()
    const timeThreshold = this.config.timelinessThreshold || 300000 // 5分钟
    let timelyRecords = 0

    for (const record of data) {
      const recordTime = new Date(record.timestamp).getTime()
      if ((now - recordTime) <= timeThreshold) {
        timelyRecords++
      }
    }

    const timeliness = data.length > 0 ? timelyRecords / data.length : 0

    return {
      passed: timeliness >= 0.8,
      score: timeliness,
      details: {
        totalRecords: data.length,
        timelyRecords,
        timeliness,
        threshold: timeThreshold
      }
    }
  }

  // 流处理器实现

  /**
   * 股票数据流处理器
   */
  private async handleStockDataStream(data: any): Promise<void> {
    try {
      const stockData = this.parseStockData(data)
      const processedData = await this.processRealtime(stockData)
      
      // 触发下游处理
      this.emitProcessedData('stock_data', processedData)
    } catch (error) {
      console.error('Error handling stock data stream:', error)
    }
  }

  /**
   * 市场数据流处理器
   */
  private async handleMarketDataStream(data: any): Promise<void> {
    try {
      // 处理市场数据
      console.log('Processing market data stream:', data)
    } catch (error) {
      console.error('Error handling market data stream:', error)
    }
  }

  /**
   * 新闻数据流处理器
   */
  private async handleNewsDataStream(data: any): Promise<void> {
    try {
      // 处理新闻数据
      console.log('Processing news data stream:', data)
    } catch (error) {
      console.error('Error handling news data stream:', error)
    }
  }

  // 辅助方法

  /**
   * 标准化数值
   */
  private normalizeValue(value: number, range?: { min: number, max: number }): number {
    if (!range) return value
    return (value - range.min) / (range.max - range.min)
  }

  /**
   * 按时间间隔分组数据
   */
  private groupDataByInterval(data: StockData[], interval: string): StockData[][] {
    // 简化的分组逻辑
    const groups: StockData[][] = []
    let currentGroup: StockData[] = []

    for (const item of data) {
      if (currentGroup.length === 0) {
        currentGroup.push(item)
      } else {
        // 简化：每10条记录一组
        if (currentGroup.length < 10) {
          currentGroup.push(item)
        } else {
          groups.push(currentGroup)
          currentGroup = [item]
        }
      }
    }

    if (currentGroup.length > 0) {
      groups.push(currentGroup)
    }

    return groups
  }

  /**
   * 计算简单移动平均
   */
  private calculateSimpleSMA(data: StockData[], symbol: string, period: number): number {
    const symbolData = data.filter(d => d.symbol === symbol).slice(-period)
    if (symbolData.length < period) return NaN
    
    const sum = symbolData.reduce((acc, d) => acc + d.close, 0)
    return sum / period
  }

  /**
   * 计算简单RSI
   */
  private calculateSimpleRSI(data: StockData[], symbol: string, period: number): number {
    const symbolData = data.filter(d => d.symbol === symbol)
    if (symbolData.length < period + 1) return NaN

    let gains = 0
    let losses = 0

    for (let i = 1; i <= period; i++) {
      const change = symbolData[i].close - symbolData[i - 1].close
      if (change > 0) gains += change
      else losses -= change
    }

    const avgGain = gains / period
    const avgLoss = losses / period
    const rs = avgGain / (avgLoss || 1)

    return 100 - (100 / (1 + rs))
  }

  /**
   * 解析股票数据
   */
  private parseStockData(data: any): StockData {
    // 简化的数据解析
    return {
      symbol: data.symbol || 'UNKNOWN',
      timestamp: data.timestamp || Date.now(),
      open: parseFloat(data.open) || 0,
      high: parseFloat(data.high) || 0,
      low: parseFloat(data.low) || 0,
      close: parseFloat(data.close) || 0,
      volume: parseInt(data.volume) || 0
    }
  }

  /**
   * 发送处理后的数据
   */
  private emitProcessedData(streamName: string, data: any): void {
    // 在实际实现中，这里会发送数据到下游系统
    console.log(`Emitting processed data from ${streamName}:`, data)
  }
} 