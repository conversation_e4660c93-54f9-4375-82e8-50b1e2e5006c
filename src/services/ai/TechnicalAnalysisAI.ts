import { 
  TechnicalIndicator, 
  TechnicalPattern, 
  TechnicalSignal,
  TechnicalAnalysisResult,
  CandlestickPattern,
  SupportResistanceLevel,
  TrendAnalysis,
  VolumeAnalysis,
  MomentumAnalysis,
  VolatilityAnalysis
} from '../../types/ai'

export interface TechnicalDataPoint {
  timestamp: number
  open: number
  high: number
  low: number
  close: number
  volume: number
}

export interface TechnicalAnalysisConfig {
  indicators: string[]
  patterns: string[]
  timeframes: string[]
  sensitivity: number
  lookbackPeriods: number
}

export class TechnicalAnalysisAI {
  private config: TechnicalAnalysisConfig
  private cache: Map<string, any> = new Map()
  private cacheTimeout: number = 5 * 60 * 1000 // 5分钟缓存

  constructor(config: TechnicalAnalysisConfig) {
    this.config = config
  }

  /**
   * 执行完整的技术分析
   */
  async analyzeTechnical(
    symbol: string, 
    data: TechnicalDataPoint[]
  ): Promise<TechnicalAnalysisResult> {
    const cacheKey = `technical_${symbol}_${Date.now()}`
    
    try {
      // 并行执行各类技术分析
      const [
        indicators,
        patterns,
        signals,
        trendAnalysis,
        volumeAnalysis,
        momentumAnalysis,
        volatilityAnalysis,
        supportResistance
      ] = await Promise.all([
        this.calculateIndicators(data),
        this.detectPatterns(data),
        this.generateSignals(data),
        this.analyzeTrend(data),
        this.analyzeVolume(data),
        this.analyzeMomentum(data),
        this.analyzeVolatility(data),
        this.findSupportResistance(data)
      ])

      // 综合评分计算
      const overallScore = this.calculateOverallScore({
        indicators,
        patterns,
        signals,
        trendAnalysis,
        volumeAnalysis,
        momentumAnalysis,
        volatilityAnalysis
      })

      const result: TechnicalAnalysisResult = {
        symbol,
        timestamp: Date.now(),
        indicators,
        patterns,
        signals,
        trendAnalysis,
        volumeAnalysis,
        momentumAnalysis,
        volatilityAnalysis,
        supportResistance,
        overallScore,
        recommendation: this.generateRecommendation(overallScore),
        confidence: this.calculateConfidence(signals),
        riskLevel: this.assessRiskLevel(volatilityAnalysis, trendAnalysis)
      }

      // 缓存结果
      this.cache.set(cacheKey, result)
      setTimeout(() => this.cache.delete(cacheKey), this.cacheTimeout)

      return result
    } catch (error) {
      console.error('技术分析失败:', error)
      throw new Error(`技术分析失败: ${error.message}`)
    }
  }

  /**
   * 计算技术指标
   */
  private async calculateIndicators(data: TechnicalDataPoint[]): Promise<TechnicalIndicator[]> {
    const indicators: TechnicalIndicator[] = []

    // 移动平均线
    const sma20 = this.calculateSMA(data, 20)
    const sma50 = this.calculateSMA(data, 50)
    const ema12 = this.calculateEMA(data, 12)
    const ema26 = this.calculateEMA(data, 26)

    indicators.push({
      name: 'SMA_20',
      value: sma20[sma20.length - 1],
      signal: this.interpretSMASignal(data, sma20),
      strength: this.calculateIndicatorStrength(data, sma20),
      description: '20日简单移动平均线'
    })

    indicators.push({
      name: 'SMA_50',
      value: sma50[sma50.length - 1],
      signal: this.interpretSMASignal(data, sma50),
      strength: this.calculateIndicatorStrength(data, sma50),
      description: '50日简单移动平均线'
    })

    // MACD指标
    const macd = this.calculateMACD(ema12, ema26)
    indicators.push({
      name: 'MACD',
      value: macd.macd[macd.macd.length - 1],
      signal: this.interpretMACDSignal(macd),
      strength: this.calculateMACDStrength(macd),
      description: 'MACD指标'
    })

    // RSI指标
    const rsi = this.calculateRSI(data, 14)
    indicators.push({
      name: 'RSI',
      value: rsi[rsi.length - 1],
      signal: this.interpretRSISignal(rsi),
      strength: this.calculateRSIStrength(rsi),
      description: '相对强弱指标'
    })

    // 布林带
    const bollinger = this.calculateBollingerBands(data, 20, 2)
    indicators.push({
      name: 'BOLLINGER',
      value: bollinger.percent[bollinger.percent.length - 1],
      signal: this.interpretBollingerSignal(bollinger, data),
      strength: this.calculateBollingerStrength(bollinger),
      description: '布林带指标'
    })

    // KDJ指标
    const kdj = this.calculateKDJ(data, 9, 3, 3)
    indicators.push({
      name: 'KDJ',
      value: kdj.k[kdj.k.length - 1],
      signal: this.interpretKDJSignal(kdj),
      strength: this.calculateKDJStrength(kdj),
      description: 'KDJ随机指标'
    })

    return indicators
  }

  /**
   * 检测技术形态
   */
  private async detectPatterns(data: TechnicalDataPoint[]): Promise<TechnicalPattern[]> {
    const patterns: TechnicalPattern[] = []

    // 检测K线形态
    const candlestickPatterns = this.detectCandlestickPatterns(data)
    patterns.push(...candlestickPatterns)

    // 检测图表形态
    const chartPatterns = this.detectChartPatterns(data)
    patterns.push(...chartPatterns)

    // 检测波浪形态
    const wavePatterns = this.detectWavePatterns(data)
    patterns.push(...wavePatterns)

    return patterns
  }

  /**
   * 生成交易信号
   */
  private async generateSignals(data: TechnicalDataPoint[]): Promise<TechnicalSignal[]> {
    const signals: TechnicalSignal[] = []

    // 趋势信号
    const trendSignal = this.generateTrendSignal(data)
    if (trendSignal) signals.push(trendSignal)

    // 动量信号
    const momentumSignal = this.generateMomentumSignal(data)
    if (momentumSignal) signals.push(momentumSignal)

    // 超买超卖信号
    const overboughtSignal = this.generateOverboughtSignal(data)
    if (overboughtSignal) signals.push(overboughtSignal)

    // 突破信号
    const breakoutSignal = this.generateBreakoutSignal(data)
    if (breakoutSignal) signals.push(breakoutSignal)

    return signals
  }

  /**
   * 趋势分析
   */
  private async analyzeTrend(data: TechnicalDataPoint[]): Promise<TrendAnalysis> {
    const prices = data.map(d => d.close)
    
    // 短期趋势 (5日)
    const shortTrend = this.calculateTrendDirection(prices.slice(-5))
    
    // 中期趋势 (20日)
    const mediumTrend = this.calculateTrendDirection(prices.slice(-20))
    
    // 长期趋势 (60日)
    const longTrend = this.calculateTrendDirection(prices.slice(-60))

    // 趋势强度
    const trendStrength = this.calculateTrendStrength(data)

    // 趋势可靠性
    const reliability = this.calculateTrendReliability(data)

    return {
      shortTerm: {
        direction: shortTrend.direction,
        strength: shortTrend.strength,
        angle: shortTrend.angle
      },
      mediumTerm: {
        direction: mediumTrend.direction,
        strength: mediumTrend.strength,
        angle: mediumTrend.angle
      },
      longTerm: {
        direction: longTrend.direction,
        strength: longTrend.strength,
        angle: longTrend.angle
      },
      overall: {
        direction: this.determineOverallTrend(shortTrend, mediumTrend, longTrend),
        strength: trendStrength,
        reliability: reliability
      }
    }
  }

  /**
   * 成交量分析
   */
  private async analyzeVolume(data: TechnicalDataPoint[]): Promise<VolumeAnalysis> {
    const volumes = data.map(d => d.volume)
    const prices = data.map(d => d.close)

    // 成交量趋势
    const volumeTrend = this.calculateVolumeTrend(volumes)

    // 价量关系
    const priceVolumeRelation = this.analyzePriceVolumeRelation(prices, volumes)

    // 成交量指标
    const volumeIndicators = this.calculateVolumeIndicators(data)

    // 异常成交量
    const volumeAnomalies = this.detectVolumeAnomalies(volumes)

    return {
      trend: volumeTrend,
      priceVolumeRelation,
      indicators: volumeIndicators,
      anomalies: volumeAnomalies,
      averageVolume: volumes.reduce((a, b) => a + b, 0) / volumes.length,
      relativeVolume: volumes[volumes.length - 1] / (volumes.reduce((a, b) => a + b, 0) / volumes.length)
    }
  }

  /**
   * 动量分析
   */
  private async analyzeMomentum(data: TechnicalDataPoint[]): Promise<MomentumAnalysis> {
    const prices = data.map(d => d.close)

    // 价格动量
    const priceMomentum = this.calculatePriceMomentum(prices)

    // 成交量动量
    const volumeMomentum = this.calculateVolumeMomentum(data.map(d => d.volume))

    // 动量指标
    const momentumIndicators = this.calculateMomentumIndicators(data)

    // 动量发散
    const divergence = this.detectMomentumDivergence(data)

    return {
      priceMomentum,
      volumeMomentum,
      indicators: momentumIndicators,
      divergence,
      strength: this.calculateMomentumStrength(priceMomentum, volumeMomentum),
      direction: this.determineMomentumDirection(priceMomentum)
    }
  }

  /**
   * 波动率分析
   */
  private async analyzeVolatility(data: TechnicalDataPoint[]): Promise<VolatilityAnalysis> {
    const prices = data.map(d => d.close)
    const returns = this.calculateReturns(prices)

    // 历史波动率
    const historicalVolatility = this.calculateHistoricalVolatility(returns)

    // 波动率趋势
    const volatilityTrend = this.calculateVolatilityTrend(data)

    // 波动率指标
    const volatilityIndicators = this.calculateVolatilityIndicators(data)

    // 波动率状态
    const volatilityState = this.assessVolatilityState(historicalVolatility, volatilityTrend)

    return {
      current: historicalVolatility,
      trend: volatilityTrend,
      indicators: volatilityIndicators,
      state: volatilityState,
      percentile: this.calculateVolatilityPercentile(data),
      forecast: this.forecastVolatility(data)
    }
  }

  /**
   * 支撑阻力位分析
   */
  private async findSupportResistance(data: TechnicalDataPoint[]): Promise<SupportResistanceLevel[]> {
    const levels: SupportResistanceLevel[] = []

    // 基于价格的支撑阻力
    const priceLevels = this.findPriceSupportResistance(data)
    levels.push(...priceLevels)

    // 基于成交量的支撑阻力
    const volumeLevels = this.findVolumeSupportResistance(data)
    levels.push(...volumeLevels)

    // 基于技术指标的支撑阻力
    const indicatorLevels = this.findIndicatorSupportResistance(data)
    levels.push(...indicatorLevels)

    // 按强度排序
    return levels.sort((a, b) => b.strength - a.strength)
  }

  // 辅助方法实现
  private calculateSMA(data: TechnicalDataPoint[], period: number): number[] {
    const sma: number[] = []
    for (let i = period - 1; i < data.length; i++) {
      const sum = data.slice(i - period + 1, i + 1).reduce((acc, point) => acc + point.close, 0)
      sma.push(sum / period)
    }
    return sma
  }

  private calculateEMA(data: TechnicalDataPoint[], period: number): number[] {
    const ema: number[] = []
    const multiplier = 2 / (period + 1)
    
    // 第一个EMA值使用SMA
    let sum = 0
    for (let i = 0; i < period; i++) {
      sum += data[i].close
    }
    ema.push(sum / period)

    // 后续EMA值
    for (let i = period; i < data.length; i++) {
      const currentEMA = (data[i].close - ema[ema.length - 1]) * multiplier + ema[ema.length - 1]
      ema.push(currentEMA)
    }

    return ema
  }

  private calculateMACD(ema12: number[], ema26: number[]): any {
    const macd: number[] = []
    const signal: number[] = []
    const histogram: number[] = []

    // 计算MACD线
    const startIndex = Math.max(0, ema26.length - ema12.length)
    for (let i = startIndex; i < ema12.length; i++) {
      macd.push(ema12[i] - ema26[i - startIndex])
    }

    // 计算信号线 (MACD的9日EMA)
    const signalMultiplier = 2 / (9 + 1)
    if (macd.length > 0) {
      signal.push(macd[0])
      for (let i = 1; i < macd.length; i++) {
        signal.push((macd[i] - signal[i - 1]) * signalMultiplier + signal[i - 1])
      }
    }

    // 计算柱状图
    for (let i = 0; i < macd.length && i < signal.length; i++) {
      histogram.push(macd[i] - signal[i])
    }

    return { macd, signal, histogram }
  }

  private calculateRSI(data: TechnicalDataPoint[], period: number): number[] {
    const rsi: number[] = []
    const gains: number[] = []
    const losses: number[] = []

    // 计算价格变化
    for (let i = 1; i < data.length; i++) {
      const change = data[i].close - data[i - 1].close
      gains.push(change > 0 ? change : 0)
      losses.push(change < 0 ? Math.abs(change) : 0)
    }

    // 计算RSI
    for (let i = period - 1; i < gains.length; i++) {
      const avgGain = gains.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period
      const avgLoss = losses.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period
      
      if (avgLoss === 0) {
        rsi.push(100)
      } else {
        const rs = avgGain / avgLoss
        rsi.push(100 - (100 / (1 + rs)))
      }
    }

    return rsi
  }

  private calculateBollingerBands(data: TechnicalDataPoint[], period: number, stdDev: number): any {
    const sma = this.calculateSMA(data, period)
    const upperBand: number[] = []
    const lowerBand: number[] = []
    const percent: number[] = []

    for (let i = 0; i < sma.length; i++) {
      const dataIndex = i + period - 1
      const slice = data.slice(dataIndex - period + 1, dataIndex + 1)
      const mean = sma[i]
      const variance = slice.reduce((acc, point) => acc + Math.pow(point.close - mean, 2), 0) / period
      const standardDeviation = Math.sqrt(variance)

      upperBand.push(mean + stdDev * standardDeviation)
      lowerBand.push(mean - stdDev * standardDeviation)
      
      // 计算%B
      const currentPrice = data[dataIndex].close
      const percentB = (currentPrice - lowerBand[i]) / (upperBand[i] - lowerBand[i])
      percent.push(percentB)
    }

    return { sma, upperBand, lowerBand, percent }
  }

  private calculateKDJ(data: TechnicalDataPoint[], kPeriod: number, kSmooth: number, dSmooth: number): any {
    const k: number[] = []
    const d: number[] = []
    const j: number[] = []

    for (let i = kPeriod - 1; i < data.length; i++) {
      const slice = data.slice(i - kPeriod + 1, i + 1)
      const highest = Math.max(...slice.map(point => point.high))
      const lowest = Math.min(...slice.map(point => point.low))
      const currentClose = data[i].close

      const rsv = ((currentClose - lowest) / (highest - lowest)) * 100

      if (k.length === 0) {
        k.push(rsv)
        d.push(rsv)
      } else {
        const kValue = (rsv + (kSmooth - 1) * k[k.length - 1]) / kSmooth
        const dValue = (kValue + (dSmooth - 1) * d[d.length - 1]) / dSmooth
        
        k.push(kValue)
        d.push(dValue)
      }

      const jValue = 3 * k[k.length - 1] - 2 * d[d.length - 1]
      j.push(jValue)
    }

    return { k, d, j }
  }

  // 信号解释方法
  private interpretSMASignal(data: TechnicalDataPoint[], sma: number[]): 'BUY' | 'SELL' | 'HOLD' {
    const currentPrice = data[data.length - 1].close
    const currentSMA = sma[sma.length - 1]
    const previousSMA = sma[sma.length - 2]

    if (currentPrice > currentSMA && currentSMA > previousSMA) {
      return 'BUY'
    } else if (currentPrice < currentSMA && currentSMA < previousSMA) {
      return 'SELL'
    } else {
      return 'HOLD'
    }
  }

  private interpretMACDSignal(macd: any): 'BUY' | 'SELL' | 'HOLD' {
    const currentMACD = macd.macd[macd.macd.length - 1]
    const currentSignal = macd.signal[macd.signal.length - 1]
    const previousMACD = macd.macd[macd.macd.length - 2]
    const previousSignal = macd.signal[macd.signal.length - 2]

    if (currentMACD > currentSignal && previousMACD <= previousSignal) {
      return 'BUY'
    } else if (currentMACD < currentSignal && previousMACD >= previousSignal) {
      return 'SELL'
    } else {
      return 'HOLD'
    }
  }

  private interpretRSISignal(rsi: number[]): 'BUY' | 'SELL' | 'HOLD' {
    const currentRSI = rsi[rsi.length - 1]
    
    if (currentRSI < 30) {
      return 'BUY'  // 超卖
    } else if (currentRSI > 70) {
      return 'SELL' // 超买
    } else {
      return 'HOLD'
    }
  }

  private interpretBollingerSignal(bollinger: any, data: TechnicalDataPoint[]): 'BUY' | 'SELL' | 'HOLD' {
    const currentPrice = data[data.length - 1].close
    const currentUpper = bollinger.upperBand[bollinger.upperBand.length - 1]
    const currentLower = bollinger.lowerBand[bollinger.lowerBand.length - 1]
    const currentPercent = bollinger.percent[bollinger.percent.length - 1]

    if (currentPercent < 0.2) {
      return 'BUY'  // 接近下轨
    } else if (currentPercent > 0.8) {
      return 'SELL' // 接近上轨
    } else {
      return 'HOLD'
    }
  }

  private interpretKDJSignal(kdj: any): 'BUY' | 'SELL' | 'HOLD' {
    const currentK = kdj.k[kdj.k.length - 1]
    const currentD = kdj.d[kdj.d.length - 1]
    const currentJ = kdj.j[kdj.j.length - 1]

    if (currentK < 20 && currentD < 20 && currentK > currentD) {
      return 'BUY'  // 低位金叉
    } else if (currentK > 80 && currentD > 80 && currentK < currentD) {
      return 'SELL' // 高位死叉
    } else {
      return 'HOLD'
    }
  }

  // 强度计算方法
  private calculateIndicatorStrength(data: TechnicalDataPoint[], indicator: number[]): number {
    // 基于指标与价格的相关性和趋势一致性计算强度
    const prices = data.slice(-indicator.length).map(d => d.close)
    const correlation = this.calculateCorrelation(prices, indicator)
    return Math.abs(correlation) * 100
  }

  private calculateMACDStrength(macd: any): number {
    const histogram = macd.histogram
    const recentHistogram = histogram.slice(-5)
    const avgHistogram = recentHistogram.reduce((a, b) => a + Math.abs(b), 0) / recentHistogram.length
    return Math.min(avgHistogram * 10, 100)
  }

  private calculateRSIStrength(rsi: number[]): number {
    const currentRSI = rsi[rsi.length - 1]
    if (currentRSI < 30 || currentRSI > 70) {
      return Math.abs(50 - currentRSI) * 2
    }
    return 50 - Math.abs(50 - currentRSI)
  }

  private calculateBollingerStrength(bollinger: any): number {
    const currentPercent = bollinger.percent[bollinger.percent.length - 1]
    return Math.abs(0.5 - currentPercent) * 200
  }

  private calculateKDJStrength(kdj: any): number {
    const currentK = kdj.k[kdj.k.length - 1]
    const currentD = kdj.d[kdj.d.length - 1]
    const divergence = Math.abs(currentK - currentD)
    return Math.min(divergence * 2, 100)
  }

  private calculateCorrelation(x: number[], y: number[]): number {
    const n = Math.min(x.length, y.length)
    const sumX = x.slice(0, n).reduce((a, b) => a + b, 0)
    const sumY = y.slice(0, n).reduce((a, b) => a + b, 0)
    const sumXY = x.slice(0, n).reduce((acc, xi, i) => acc + xi * y[i], 0)
    const sumX2 = x.slice(0, n).reduce((acc, xi) => acc + xi * xi, 0)
    const sumY2 = y.slice(0, n).reduce((acc, yi) => acc + yi * yi, 0)

    const numerator = n * sumXY - sumX * sumY
    const denominator = Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY))

    return denominator === 0 ? 0 : numerator / denominator
  }

  // 其他辅助方法的简化实现
  private detectCandlestickPatterns(data: TechnicalDataPoint[]): TechnicalPattern[] {
    // 简化的K线形态检测
    return []
  }

  private detectChartPatterns(data: TechnicalDataPoint[]): TechnicalPattern[] {
    // 简化的图表形态检测
    return []
  }

  private detectWavePatterns(data: TechnicalDataPoint[]): TechnicalPattern[] {
    // 简化的波浪形态检测
    return []
  }

  private generateTrendSignal(data: TechnicalDataPoint[]): TechnicalSignal | null {
    // 简化的趋势信号生成
    return null
  }

  private generateMomentumSignal(data: TechnicalDataPoint[]): TechnicalSignal | null {
    // 简化的动量信号生成
    return null
  }

  private generateOverboughtSignal(data: TechnicalDataPoint[]): TechnicalSignal | null {
    // 简化的超买超卖信号生成
    return null
  }

  private generateBreakoutSignal(data: TechnicalDataPoint[]): TechnicalSignal | null {
    // 简化的突破信号生成
    return null
  }

  private calculateTrendDirection(prices: number[]): any {
    const firstPrice = prices[0]
    const lastPrice = prices[prices.length - 1]
    const change = lastPrice - firstPrice
    const changePercent = (change / firstPrice) * 100

    return {
      direction: change > 0 ? 'UP' : change < 0 ? 'DOWN' : 'SIDEWAYS',
      strength: Math.abs(changePercent),
      angle: Math.atan(change / prices.length) * (180 / Math.PI)
    }
  }

  private calculateTrendStrength(data: TechnicalDataPoint[]): number {
    // 简化的趋势强度计算
    return 50
  }

  private calculateTrendReliability(data: TechnicalDataPoint[]): number {
    // 简化的趋势可靠性计算
    return 0.75
  }

  private determineOverallTrend(short: any, medium: any, long: any): 'UP' | 'DOWN' | 'SIDEWAYS' {
    const directions = [short.direction, medium.direction, long.direction]
    const upCount = directions.filter(d => d === 'UP').length
    const downCount = directions.filter(d => d === 'DOWN').length

    if (upCount >= 2) return 'UP'
    if (downCount >= 2) return 'DOWN'
    return 'SIDEWAYS'
  }

  private calculateVolumeTrend(volumes: number[]): 'INCREASING' | 'DECREASING' | 'STABLE' {
    const recent = volumes.slice(-5)
    const earlier = volumes.slice(-10, -5)
    const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length
    const earlierAvg = earlier.reduce((a, b) => a + b, 0) / earlier.length

    const change = (recentAvg - earlierAvg) / earlierAvg
    if (change > 0.1) return 'INCREASING'
    if (change < -0.1) return 'DECREASING'
    return 'STABLE'
  }

  private analyzePriceVolumeRelation(prices: number[], volumes: number[]): 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL' {
    const correlation = this.calculateCorrelation(prices, volumes)
    if (correlation > 0.3) return 'POSITIVE'
    if (correlation < -0.3) return 'NEGATIVE'
    return 'NEUTRAL'
  }

  private calculateVolumeIndicators(data: TechnicalDataPoint[]): any[] {
    // 简化的成交量指标计算
    return []
  }

  private detectVolumeAnomalies(volumes: number[]): any[] {
    // 简化的成交量异常检测
    return []
  }

  private calculatePriceMomentum(prices: number[]): number {
    const recent = prices.slice(-5)
    const earlier = prices.slice(-10, -5)
    const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length
    const earlierAvg = earlier.reduce((a, b) => a + b, 0) / earlier.length
    return (recentAvg - earlierAvg) / earlierAvg * 100
  }

  private calculateVolumeMomentum(volumes: number[]): number {
    const recent = volumes.slice(-5)
    const earlier = volumes.slice(-10, -5)
    const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length
    const earlierAvg = earlier.reduce((a, b) => a + b, 0) / earlier.length
    return (recentAvg - earlierAvg) / earlierAvg * 100
  }

  private calculateMomentumIndicators(data: TechnicalDataPoint[]): any[] {
    // 简化的动量指标计算
    return []
  }

  private detectMomentumDivergence(data: TechnicalDataPoint[]): boolean {
    // 简化的动量背离检测
    return false
  }

  private calculateMomentumStrength(priceMomentum: number, volumeMomentum: number): number {
    return (Math.abs(priceMomentum) + Math.abs(volumeMomentum)) / 2
  }

  private determineMomentumDirection(priceMomentum: number): 'UP' | 'DOWN' | 'NEUTRAL' {
    if (priceMomentum > 2) return 'UP'
    if (priceMomentum < -2) return 'DOWN'
    return 'NEUTRAL'
  }

  private calculateReturns(prices: number[]): number[] {
    const returns: number[] = []
    for (let i = 1; i < prices.length; i++) {
      returns.push((prices[i] - prices[i - 1]) / prices[i - 1])
    }
    return returns
  }

  private calculateHistoricalVolatility(returns: number[]): number {
    const mean = returns.reduce((a, b) => a + b, 0) / returns.length
    const variance = returns.reduce((acc, ret) => acc + Math.pow(ret - mean, 2), 0) / returns.length
    return Math.sqrt(variance) * Math.sqrt(252) * 100 // 年化波动率
  }

  private calculateVolatilityTrend(data: TechnicalDataPoint[]): 'INCREASING' | 'DECREASING' | 'STABLE' {
    // 简化的波动率趋势计算
    return 'STABLE'
  }

  private calculateVolatilityIndicators(data: TechnicalDataPoint[]): any[] {
    // 简化的波动率指标计算
    return []
  }

  private assessVolatilityState(current: number, trend: string): 'LOW' | 'NORMAL' | 'HIGH' | 'EXTREME' {
    if (current < 15) return 'LOW'
    if (current < 25) return 'NORMAL'
    if (current < 40) return 'HIGH'
    return 'EXTREME'
  }

  private calculateVolatilityPercentile(data: TechnicalDataPoint[]): number {
    // 简化的波动率百分位计算
    return 50
  }

  private forecastVolatility(data: TechnicalDataPoint[]): number {
    // 简化的波动率预测
    const prices = data.map(d => d.close)
    const returns = this.calculateReturns(prices)
    return this.calculateHistoricalVolatility(returns)
  }

  private findPriceSupportResistance(data: TechnicalDataPoint[]): SupportResistanceLevel[] {
    // 简化的价格支撑阻力位查找
    return []
  }

  private findVolumeSupportResistance(data: TechnicalDataPoint[]): SupportResistanceLevel[] {
    // 简化的成交量支撑阻力位查找
    return []
  }

  private findIndicatorSupportResistance(data: TechnicalDataPoint[]): SupportResistanceLevel[] {
    // 简化的指标支撑阻力位查找
    return []
  }

  private calculateOverallScore(analysis: any): number {
    // 综合评分计算
    let score = 50 // 基础分数

    // 基于指标信号调整分数
    const indicators = analysis.indicators || []
    indicators.forEach((indicator: TechnicalIndicator) => {
      const weight = this.getIndicatorWeight(indicator.name)
      if (indicator.signal === 'BUY') {
        score += (indicator.strength / 100) * weight
      } else if (indicator.signal === 'SELL') {
        score -= (indicator.strength / 100) * weight
      }
    })

    // 基于趋势分析调整分数
    if (analysis.trendAnalysis) {
      const trend = analysis.trendAnalysis.overall
      if (trend.direction === 'UP') {
        score += trend.strength * 0.3
      } else if (trend.direction === 'DOWN') {
        score -= trend.strength * 0.3
      }
    }

    // 基于动量分析调整分数
    if (analysis.momentumAnalysis) {
      const momentum = analysis.momentumAnalysis
      if (momentum.direction === 'UP') {
        score += momentum.strength * 0.2
      } else if (momentum.direction === 'DOWN') {
        score -= momentum.strength * 0.2
      }
    }

    return Math.max(0, Math.min(100, score))
  }

  private getIndicatorWeight(name: string): number {
    const weights: { [key: string]: number } = {
      'SMA_20': 15,
      'SMA_50': 10,
      'MACD': 20,
      'RSI': 15,
      'BOLLINGER': 15,
      'KDJ': 10
    }
    return weights[name] || 10
  }

  private generateRecommendation(score: number): 'STRONG_BUY' | 'BUY' | 'HOLD' | 'SELL' | 'STRONG_SELL' {
    if (score >= 80) return 'STRONG_BUY'
    if (score >= 60) return 'BUY'
    if (score >= 40) return 'HOLD'
    if (score >= 20) return 'SELL'
    return 'STRONG_SELL'
  }

  private calculateConfidence(signals: TechnicalSignal[]): number {
    if (signals.length === 0) return 0.5

    const consistentSignals = signals.filter(signal => 
      signals.filter(s => s.type === signal.type).length > 1
    )

    return Math.min(0.95, 0.5 + (consistentSignals.length / signals.length) * 0.45)
  }

  private assessRiskLevel(volatility: VolatilityAnalysis, trend: TrendAnalysis): 'LOW' | 'MEDIUM' | 'HIGH' | 'EXTREME' {
    const volState = volatility.state
    const trendReliability = trend.overall.reliability

    if (volState === 'EXTREME' || trendReliability < 0.3) return 'EXTREME'
    if (volState === 'HIGH' || trendReliability < 0.5) return 'HIGH'
    if (volState === 'NORMAL' && trendReliability > 0.7) return 'LOW'
    return 'MEDIUM'
  }
}