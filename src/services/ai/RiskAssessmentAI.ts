import { 
  RiskAssessment, 
  RiskMetrics,
  RiskScenario,
  RiskAlert,
  PortfolioRisk,
  MarketRisk,
  CompanyRisk,
  LiquidityRisk,
  VolatilityRisk,
  TechnicalDataPoint
} from '../../types/ai'

export interface RiskAssessmentConfig {
  riskModels: string[] // 使用的风险模型
  timeHorizons: number[] // 风险评估时间范围
  confidenceLevels: number[] // 置信度水平
  stressTestScenarios: string[] // 压力测试场景
  alertThresholds: { [key: string]: number } // 告警阈值
}

export interface RiskFactors {
  market: number[]
  credit: number[]
  liquidity: number[]
  operational: number[]
  regulatory: number[]
  concentration: number[]
  correlation: number[]
}

export interface StressTestScenario {
  name: string
  description: string
  marketShock: number
  volatilityShock: number
  liquidityShock: number
  correlationShock: number
  probability: number
}

export class RiskAssessmentAI {
  private config: RiskAssessmentConfig
  private riskModels: Map<string, any> = new Map()
  private cache: Map<string, RiskAssessment> = new Map()
  private cacheTimeout: number = 15 * 60 * 1000 // 15分钟缓存

  constructor(config: RiskAssessmentConfig) {
    this.config = config
    this.initializeRiskModels()
  }

  /**
   * 综合风险评估
   */
  async assessRisk(
    symbol: string,
    data: TechnicalDataPoint[],
    portfolioData?: { [symbol: string]: { weight: number, data: TechnicalDataPoint[] } }
  ): Promise<RiskAssessment> {
    const cacheKey = `risk_${symbol}_${Date.now()}`
    
    try {
      // 并行执行各类风险分析
      const [
        marketRisk,
        companyRisk,
        liquidityRisk,
        volatilityRisk,
        portfolioRisk,
        riskMetrics,
        riskScenarios,
        riskAlerts
      ] = await Promise.all([
        this.assessMarketRisk(data),
        this.assessCompanyRisk(symbol, data),
        this.assessLiquidityRisk(data),
        this.assessVolatilityRisk(data),
        portfolioData ? this.assessPortfolioRisk(portfolioData) : null,
        this.calculateRiskMetrics(data),
        this.generateRiskScenarios(data),
        this.generateRiskAlerts(symbol, data)
      ])

      // 计算综合风险评分
      const overallRiskScore = this.calculateOverallRiskScore({
        marketRisk,
        companyRisk,
        liquidityRisk,
        volatilityRisk,
        portfolioRisk
      })

      // 确定风险等级
      const riskLevel = this.determineRiskLevel(overallRiskScore)

      const assessment: RiskAssessment = {
        symbol,
        timestamp: Date.now(),
        overallRiskScore,
        riskLevel,
        marketRisk,
        companyRisk,
        liquidityRisk,
        volatilityRisk,
        portfolioRisk,
        riskMetrics,
        scenarios: riskScenarios,
        alerts: riskAlerts,
        recommendations: this.generateRiskRecommendations(overallRiskScore, riskLevel),
        nextReviewDate: Date.now() + 24 * 60 * 60 * 1000 // 24小时后复评
      }

      // 缓存结果
      this.cache.set(cacheKey, assessment)
      setTimeout(() => this.cache.delete(cacheKey), this.cacheTimeout)

      return assessment
    } catch (error) {
      console.error('风险评估失败:', error)
      throw new Error(`风险评估失败: ${error.message}`)
    }
  }

  /**
   * 批量风险评估
   */
  async batchAssessRisk(
    symbols: string[],
    data: { [symbol: string]: TechnicalDataPoint[] }
  ): Promise<{ [symbol: string]: RiskAssessment }> {
    const assessments: { [symbol: string]: RiskAssessment } = {}

    // 并行处理多个股票
    const results = await Promise.allSettled(
      symbols.map(symbol => 
        data[symbol] ? this.assessRisk(symbol, data[symbol]) : null
      )
    )

    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value) {
        assessments[symbols[index]] = result.value
      }
    })

    return assessments
  }

  /**
   * 投资组合风险评估
   */
  async assessPortfolioRisk(
    portfolioData: { [symbol: string]: { weight: number, data: TechnicalDataPoint[] } }
  ): Promise<PortfolioRisk> {
    try {
      const symbols = Object.keys(portfolioData)
      const weights = symbols.map(symbol => portfolioData[symbol].weight)
      const returns = this.calculatePortfolioReturns(portfolioData)

      // 计算投资组合风险指标
      const portfolioVolatility = this.calculatePortfolioVolatility(portfolioData)
      const portfolioVaR = this.calculatePortfolioVaR(returns, 0.05) // 5% VaR
      const portfolioES = this.calculateExpectedShortfall(returns, 0.05) // 5% ES
      const maxDrawdown = this.calculateMaxDrawdown(returns)
      const sharpeRatio = this.calculateSharpeRatio(returns)
      const beta = this.calculatePortfolioBeta(portfolioData)

      // 风险分解
      const riskContribution = this.calculateRiskContribution(portfolioData)
      const diversificationRatio = this.calculateDiversificationRatio(portfolioData)

      // 相关性分析
      const correlationMatrix = this.calculateCorrelationMatrix(portfolioData)
      const concentrationRisk = this.calculateConcentrationRisk(weights)

      // 压力测试
      const stressTestResults = await this.runPortfolioStressTests(portfolioData)

      return {
        portfolioVolatility,
        valueAtRisk: portfolioVaR,
        expectedShortfall: portfolioES,
        maxDrawdown,
        sharpeRatio,
        beta,
        riskContribution,
        diversificationRatio,
        correlationMatrix,
        concentrationRisk,
        stressTestResults,
        riskLevel: this.determinePortfolioRiskLevel(portfolioVolatility, portfolioVaR),
        recommendations: this.generatePortfolioRiskRecommendations(portfolioVolatility, concentrationRisk)
      }
    } catch (error) {
      console.error('投资组合风险评估失败:', error)
      throw new Error(`投资组合风险评估失败: ${error.message}`)
    }
  }

  /**
   * 市场风险评估
   */
  private async assessMarketRisk(data: TechnicalDataPoint[]): Promise<MarketRisk> {
    const prices = data.map(d => d.close)
    const returns = this.calculateReturns(prices)

    // 市场波动率
    const volatility = this.calculateVolatility(returns)

    // 与市场指数的相关性
    const marketCorrelation = this.calculateMarketCorrelation(returns)

    // Beta系数
    const beta = this.calculateBeta(returns)

    // 系统性风险
    const systematicRisk = this.calculateSystematicRisk(beta, volatility)

    // 市场风险因子
    const riskFactors = this.identifyMarketRiskFactors(data)

    return {
      volatility,
      beta,
      correlation: marketCorrelation,
      systematicRisk,
      riskFactors,
      riskLevel: this.categorizeRiskLevel(systematicRisk),
      explanation: this.explainMarketRisk(volatility, beta, marketCorrelation)
    }
  }

  /**
   * 公司特定风险评估
   */
  private async assessCompanyRisk(symbol: string, data: TechnicalDataPoint[]): Promise<CompanyRisk> {
    const prices = data.map(d => d.close)
    const volumes = data.map(d => d.volume)
    const returns = this.calculateReturns(prices)

    // 特异性风险
    const idiosyncraticRisk = this.calculateIdiosyncraticRisk(returns)

    // 基本面风险
    const fundamentalRisk = await this.assessFundamentalRisk(symbol)

    // 行业风险
    const sectorRisk = await this.assessSectorRisk(symbol)

    // 财务风险
    const financialRisk = await this.assessFinancialRisk(symbol)

    // 管理风险
    const managementRisk = await this.assessManagementRisk(symbol)

    // 合规风险
    const regulatoryRisk = await this.assessRegulatoryRisk(symbol)

    return {
      idiosyncraticRisk,
      fundamentalRisk,
      sectorRisk,
      financialRisk,
      managementRisk,
      regulatoryRisk,
      overallRisk: this.calculateCompanyOverallRisk({
        idiosyncraticRisk,
        fundamentalRisk,
        sectorRisk,
        financialRisk,
        managementRisk,
        regulatoryRisk
      }),
      riskLevel: this.categorizeRiskLevel(idiosyncraticRisk),
      keyRiskFactors: this.identifyKeyRiskFactors(symbol)
    }
  }

  /**
   * 流动性风险评估
   */
  private async assessLiquidityRisk(data: TechnicalDataPoint[]): Promise<LiquidityRisk> {
    const volumes = data.map(d => d.volume)
    const prices = data.map(d => d.close)

    // 成交量分析
    const avgVolume = volumes.reduce((a, b) => a + b, 0) / volumes.length
    const volumeVolatility = this.calculateVolatility(volumes.map(v => v / avgVolume))

    // 买卖价差（模拟）
    const bidAskSpread = this.estimateBidAskSpread(prices, volumes)

    // 市场深度
    const marketDepth = this.calculateMarketDepth(volumes)

    // 流动性比率
    const liquidityRatio = this.calculateLiquidityRatio(volumes, prices)

    // 冲击成本
    const impactCost = this.calculateImpactCost(volumes, prices)

    // 流动性风险评分
    const liquidityScore = this.calculateLiquidityScore(volumeVolatility, bidAskSpread, marketDepth)

    return {
      avgVolume,
      volumeVolatility,
      bidAskSpread,
      marketDepth,
      liquidityRatio,
      impactCost,
      liquidityScore,
      riskLevel: this.categorizeLiquidityRisk(liquidityScore),
      explanation: this.explainLiquidityRisk(volumeVolatility, bidAskSpread)
    }
  }

  /**
   * 波动率风险评估
   */
  private async assessVolatilityRisk(data: TechnicalDataPoint[]): Promise<VolatilityRisk> {
    const prices = data.map(d => d.close)
    const returns = this.calculateReturns(prices)

    // 历史波动率
    const historicalVolatility = this.calculateVolatility(returns)

    // 隐含波动率（模拟）
    const impliedVolatility = this.estimateImpliedVolatility(historicalVolatility)

    // 波动率聚集性
    const volatilityClustering = this.detectVolatilityClustering(returns)

    // GARCH模型预测
    const garchForecast = this.forecastGARCHVolatility(returns)

    // 波动率风险溢价
    const volatilityRiskPremium = impliedVolatility - historicalVolatility

    // 波动率状态
    const volatilityRegime = this.identifyVolatilityRegime(returns)

    return {
      historical: historicalVolatility,
      implied: impliedVolatility,
      clustering: volatilityClustering,
      forecast: garchForecast,
      riskPremium: volatilityRiskPremium,
      regime: volatilityRegime,
      riskLevel: this.categorizeVolatilityRisk(historicalVolatility),
      explanation: this.explainVolatilityRisk(historicalVolatility, volatilityClustering)
    }
  }

  /**
   * 计算风险指标
   */
  private async calculateRiskMetrics(data: TechnicalDataPoint[]): Promise<RiskMetrics> {
    const prices = data.map(d => d.close)
    const returns = this.calculateReturns(prices)

    // VaR计算
    const var95 = this.calculateVaR(returns, 0.05)
    const var99 = this.calculateVaR(returns, 0.01)

    // Expected Shortfall
    const es95 = this.calculateExpectedShortfall(returns, 0.05)
    const es99 = this.calculateExpectedShortfall(returns, 0.01)

    // 最大回撤
    const maxDrawdown = this.calculateMaxDrawdown(returns)

    // 夏普比率
    const sharpeRatio = this.calculateSharpeRatio(returns)

    // 索提诺比率
    const sortinoRatio = this.calculateSortinoRatio(returns)

    // 卡尔玛比率
    const calmarRatio = this.calculateCalmarRatio(returns, maxDrawdown)

    // 信息比率
    const informationRatio = this.calculateInformationRatio(returns)

    return {
      valueAtRisk: {
        var95,
        var99
      },
      expectedShortfall: {
        es95,
        es99
      },
      maxDrawdown,
      sharpeRatio,
      sortinoRatio,
      calmarRatio,
      informationRatio,
      riskAdjustedReturn: sharpeRatio,
      downside: {
        downsideDeviation: this.calculateDownsideDeviation(returns),
        downsideFrequency: this.calculateDownsideFrequency(returns)
      }
    }
  }

  /**
   * 生成风险情景
   */
  private async generateRiskScenarios(data: TechnicalDataPoint[]): Promise<RiskScenario[]> {
    const scenarios: RiskScenario[] = []
    const currentPrice = data[data.length - 1].close

    // 市场崩盘情景
    scenarios.push({
      name: '市场崩盘',
      description: '市场出现严重下跌，类似2008年金融危机',
      probability: 0.02,
      priceImpact: -0.30,
      timeframe: '1-3个月',
      triggers: ['系统性风险', '流动性危机', '经济衰退'],
      potentialLoss: currentPrice * 0.30,
      recoveryTime: '12-24个月'
    })

    // 高波动情景
    scenarios.push({
      name: '高波动环境',
      description: '市场波动率显著上升',
      probability: 0.15,
      priceImpact: -0.15,
      timeframe: '2-6个月',
      triggers: ['政策不确定性', '地缘政治风险', '通胀压力'],
      potentialLoss: currentPrice * 0.15,
      recoveryTime: '6-12个月'
    })

    // 行业特定风险
    scenarios.push({
      name: '行业危机',
      description: '特定行业面临重大挑战',
      probability: 0.08,
      priceImpact: -0.25,
      timeframe: '3-12个月',
      triggers: ['监管变化', '技术革新', '需求下降'],
      potentialLoss: currentPrice * 0.25,
      recoveryTime: '18-36个月'
    })

    // 流动性危机
    scenarios.push({
      name: '流动性枯竭',
      description: '市场流动性严重不足',
      probability: 0.05,
      priceImpact: -0.20,
      timeframe: '1-6个月',
      triggers: ['资金紧张', '恐慌性抛售', '做市商退出'],
      potentialLoss: currentPrice * 0.20,
      recoveryTime: '6-18个月'
    })

    return scenarios
  }

  /**
   * 生成风险告警
   */
  private async generateRiskAlerts(symbol: string, data: TechnicalDataPoint[]): Promise<RiskAlert[]> {
    const alerts: RiskAlert[] = []
    const prices = data.map(d => d.close)
    const volumes = data.map(d => d.volume)
    const returns = this.calculateReturns(prices)

    // 波动率告警
    const volatility = this.calculateVolatility(returns)
    if (volatility > this.config.alertThresholds.volatility || 0.25) {
      alerts.push({
        type: 'HIGH_VOLATILITY',
        severity: 'HIGH',
        message: `${symbol} 波动率异常高 (${(volatility * 100).toFixed(2)}%)`,
        timestamp: Date.now(),
        threshold: this.config.alertThresholds.volatility || 0.25,
        currentValue: volatility,
        recommendation: '建议减少仓位或增加对冲'
      })
    }

    // 流动性告警
    const avgVolume = volumes.reduce((a, b) => a + b, 0) / volumes.length
    const recentVolume = volumes.slice(-5).reduce((a, b) => a + b, 0) / 5
    if (recentVolume < avgVolume * 0.5) {
      alerts.push({
        type: 'LOW_LIQUIDITY',
        severity: 'MEDIUM',
        message: `${symbol} 流动性显著下降`,
        timestamp: Date.now(),
        threshold: avgVolume * 0.5,
        currentValue: recentVolume,
        recommendation: '谨慎交易，关注买卖价差'
      })
    }

    // 价格异常告警
    const priceChange = (prices[prices.length - 1] - prices[prices.length - 2]) / prices[prices.length - 2]
    if (Math.abs(priceChange) > 0.10) {
      alerts.push({
        type: 'PRICE_ANOMALY',
        severity: priceChange > 0 ? 'MEDIUM' : 'HIGH',
        message: `${symbol} 价格异常变动 (${(priceChange * 100).toFixed(2)}%)`,
        timestamp: Date.now(),
        threshold: 0.10,
        currentValue: Math.abs(priceChange),
        recommendation: '检查是否有重大消息或事件'
      })
    }

    return alerts
  }

  // 辅助计算方法
  private calculateReturns(prices: number[]): number[] {
    const returns: number[] = []
    for (let i = 1; i < prices.length; i++) {
      returns.push((prices[i] - prices[i - 1]) / prices[i - 1])
    }
    return returns
  }

  private calculateVolatility(returns: number[]): number {
    const mean = returns.reduce((a, b) => a + b, 0) / returns.length
    const variance = returns.reduce((acc, ret) => acc + Math.pow(ret - mean, 2), 0) / returns.length
    return Math.sqrt(variance) * Math.sqrt(252) // 年化波动率
  }

  private calculateVaR(returns: number[], confidence: number): number {
    const sortedReturns = returns.slice().sort((a, b) => a - b)
    const index = Math.floor(returns.length * confidence)
    return Math.abs(sortedReturns[index])
  }

  private calculateExpectedShortfall(returns: number[], confidence: number): number {
    const sortedReturns = returns.slice().sort((a, b) => a - b)
    const cutoff = Math.floor(returns.length * confidence)
    const tailReturns = sortedReturns.slice(0, cutoff)
    const avgTailReturn = tailReturns.reduce((a, b) => a + b, 0) / tailReturns.length
    return Math.abs(avgTailReturn)
  }

  private calculateMaxDrawdown(returns: number[]): number {
    let peak = 1
    let maxDrawdown = 0
    let currentValue = 1

    returns.forEach(ret => {
      currentValue *= (1 + ret)
      if (currentValue > peak) {
        peak = currentValue
      }
      const drawdown = (peak - currentValue) / peak
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown
      }
    })

    return maxDrawdown
  }

  private calculateSharpeRatio(returns: number[], riskFreeRate: number = 0.02): number {
    const excessReturns = returns.map(ret => ret - riskFreeRate / 252)
    const avgExcessReturn = excessReturns.reduce((a, b) => a + b, 0) / excessReturns.length
    const volatility = this.calculateVolatility(returns) / Math.sqrt(252) // 日波动率
    return avgExcessReturn / volatility
  }

  private calculateSortinoRatio(returns: number[], riskFreeRate: number = 0.02): number {
    const excessReturns = returns.map(ret => ret - riskFreeRate / 252)
    const avgExcessReturn = excessReturns.reduce((a, b) => a + b, 0) / excessReturns.length
    const downsideDeviation = this.calculateDownsideDeviation(returns)
    return avgExcessReturn / downsideDeviation
  }

  private calculateCalmarRatio(returns: number[], maxDrawdown: number): number {
    const annualReturn = returns.reduce((acc, ret) => acc * (1 + ret), 1) - 1
    return annualReturn / maxDrawdown
  }

  private calculateInformationRatio(returns: number[], benchmarkReturns?: number[]): number {
    // 简化实现，假设基准收益为0
    const avgReturn = returns.reduce((a, b) => a + b, 0) / returns.length
    const trackingError = this.calculateVolatility(returns) / Math.sqrt(252)
    return avgReturn / trackingError
  }

  private calculateDownsideDeviation(returns: number[], target: number = 0): number {
    const downsideReturns = returns.filter(ret => ret < target)
    if (downsideReturns.length === 0) return 0
    
    const avgDownside = downsideReturns.reduce((a, b) => a + b, 0) / downsideReturns.length
    const variance = downsideReturns.reduce((acc, ret) => acc + Math.pow(ret - avgDownside, 2), 0) / downsideReturns.length
    return Math.sqrt(variance)
  }

  private calculateDownsideFrequency(returns: number[], target: number = 0): number {
    const downsideCount = returns.filter(ret => ret < target).length
    return downsideCount / returns.length
  }

  // 简化的风险评估方法
  private calculateMarketCorrelation(returns: number[]): number {
    // 简化实现，返回模拟的市场相关性
    return 0.7 + Math.random() * 0.2
  }

  private calculateBeta(returns: number[]): number {
    // 简化实现，返回模拟的Beta值
    return 0.8 + Math.random() * 0.4
  }

  private calculateSystematicRisk(beta: number, volatility: number): number {
    return beta * volatility * 0.6 // 简化计算
  }

  private identifyMarketRiskFactors(data: TechnicalDataPoint[]): string[] {
    return ['市场波动', '利率风险', '通胀风险', '政策风险']
  }

  private calculateIdiosyncraticRisk(returns: number[]): number {
    const volatility = this.calculateVolatility(returns)
    const beta = this.calculateBeta(returns)
    const marketVol = 0.16 // 假设市场波动率16%
    return Math.sqrt(Math.pow(volatility, 2) - Math.pow(beta * marketVol, 2))
  }

  private async assessFundamentalRisk(symbol: string): Promise<number> {
    // 简化实现
    return 0.3 + Math.random() * 0.4
  }

  private async assessSectorRisk(symbol: string): Promise<number> {
    // 简化实现
    return 0.2 + Math.random() * 0.3
  }

  private async assessFinancialRisk(symbol: string): Promise<number> {
    // 简化实现
    return 0.25 + Math.random() * 0.35
  }

  private async assessManagementRisk(symbol: string): Promise<number> {
    // 简化实现
    return 0.15 + Math.random() * 0.25
  }

  private async assessRegulatoryRisk(symbol: string): Promise<number> {
    // 简化实现
    return 0.1 + Math.random() * 0.2
  }

  private calculateCompanyOverallRisk(risks: any): number {
    const weights = {
      idiosyncraticRisk: 0.3,
      fundamentalRisk: 0.25,
      sectorRisk: 0.2,
      financialRisk: 0.15,
      managementRisk: 0.05,
      regulatoryRisk: 0.05
    }

    let totalRisk = 0
    Object.keys(weights).forEach(key => {
      totalRisk += risks[key] * weights[key]
    })

    return totalRisk
  }

  private identifyKeyRiskFactors(symbol: string): string[] {
    return ['财务杠杆', '行业竞争', '监管变化', '技术创新']
  }

  private estimateBidAskSpread(prices: number[], volumes: number[]): number {
    // 简化实现，基于价格和成交量估算买卖价差
    const avgPrice = prices.reduce((a, b) => a + b, 0) / prices.length
    const avgVolume = volumes.reduce((a, b) => a + b, 0) / volumes.length
    return (avgPrice * 0.001) / Math.sqrt(avgVolume / 1000000) // 简化公式
  }

  private calculateMarketDepth(volumes: number[]): number {
    const avgVolume = volumes.reduce((a, b) => a + b, 0) / volumes.length
    return Math.min(avgVolume / 1000000, 1) // 标准化到0-1
  }

  private calculateLiquidityRatio(volumes: number[], prices: number[]): number {
    const avgVolume = volumes.reduce((a, b) => a + b, 0) / volumes.length
    const avgPrice = prices.reduce((a, b) => a + b, 0) / prices.length
    return avgVolume * avgPrice / 1000000 // 简化计算
  }

  private calculateImpactCost(volumes: number[], prices: number[]): number {
    // 简化实现
    const avgVolume = volumes.reduce((a, b) => a + b, 0) / volumes.length
    return 0.005 / Math.sqrt(avgVolume / 1000000) // 简化公式
  }

  private calculateLiquidityScore(volumeVol: number, spread: number, depth: number): number {
    return Math.max(0, 1 - (volumeVol + spread * 1000 - depth))
  }

  private estimateImpliedVolatility(historicalVol: number): number {
    // 简化实现，隐含波动率通常高于历史波动率
    return historicalVol * (1.1 + Math.random() * 0.2)
  }

  private detectVolatilityClustering(returns: number[]): boolean {
    // 简化的波动率聚集检测
    const volatilities = []
    const window = 20

    for (let i = window; i < returns.length; i++) {
      const windowReturns = returns.slice(i - window, i)
      const vol = this.calculateVolatility(windowReturns)
      volatilities.push(vol)
    }

    // 检查连续高波动率期间
    let highVolPeriods = 0
    const threshold = volatilities.reduce((a, b) => a + b, 0) / volatilities.length * 1.5

    for (let i = 1; i < volatilities.length; i++) {
      if (volatilities[i] > threshold && volatilities[i - 1] > threshold) {
        highVolPeriods++
      }
    }

    return highVolPeriods > volatilities.length * 0.1
  }

  private forecastGARCHVolatility(returns: number[]): number {
    // 简化的GARCH预测
    const currentVol = this.calculateVolatility(returns.slice(-20))
    const longTermVol = this.calculateVolatility(returns)
    return 0.9 * currentVol + 0.1 * longTermVol
  }

  private identifyVolatilityRegime(returns: number[]): 'LOW' | 'MEDIUM' | 'HIGH' {
    const volatility = this.calculateVolatility(returns)
    if (volatility < 0.15) return 'LOW'
    if (volatility < 0.25) return 'MEDIUM'
    return 'HIGH'
  }

  // 风险等级分类方法
  private categorizeRiskLevel(riskValue: number): 'LOW' | 'MEDIUM' | 'HIGH' | 'EXTREME' {
    if (riskValue < 0.15) return 'LOW'
    if (riskValue < 0.25) return 'MEDIUM'
    if (riskValue < 0.40) return 'HIGH'
    return 'EXTREME'
  }

  private categorizeLiquidityRisk(liquidityScore: number): 'LOW' | 'MEDIUM' | 'HIGH' | 'EXTREME' {
    if (liquidityScore > 0.8) return 'LOW'
    if (liquidityScore > 0.6) return 'MEDIUM'
    if (liquidityScore > 0.4) return 'HIGH'
    return 'EXTREME'
  }

  private categorizeVolatilityRisk(volatility: number): 'LOW' | 'MEDIUM' | 'HIGH' | 'EXTREME' {
    return this.categorizeRiskLevel(volatility)
  }

  private calculateOverallRiskScore(risks: any): number {
    const weights = {
      marketRisk: 0.3,
      companyRisk: 0.25,
      liquidityRisk: 0.2,
      volatilityRisk: 0.15,
      portfolioRisk: 0.1
    }

    let totalScore = 0
    let totalWeight = 0

    Object.keys(weights).forEach(key => {
      if (risks[key] && risks[key].overallRisk !== undefined) {
        totalScore += risks[key].overallRisk * weights[key]
        totalWeight += weights[key]
      } else if (risks[key] && typeof risks[key] === 'number') {
        totalScore += risks[key] * weights[key]
        totalWeight += weights[key]
      }
    })

    return totalWeight > 0 ? totalScore / totalWeight : 0.5
  }

  private determineRiskLevel(riskScore: number): 'LOW' | 'MEDIUM' | 'HIGH' | 'EXTREME' {
    return this.categorizeRiskLevel(riskScore)
  }

  private determinePortfolioRiskLevel(volatility: number, var: number): 'LOW' | 'MEDIUM' | 'HIGH' | 'EXTREME' {
    const combinedRisk = (volatility + var) / 2
    return this.categorizeRiskLevel(combinedRisk)
  }

  private generateRiskRecommendations(riskScore: number, riskLevel: string): string[] {
    const recommendations: string[] = []

    switch (riskLevel) {
      case 'LOW':
        recommendations.push('当前风险水平较低，可考虑适度增加仓位')
        recommendations.push('继续监控市场变化')
        break
      case 'MEDIUM':
        recommendations.push('风险水平适中，保持当前仓位')
        recommendations.push('加强风险监控')
        break
      case 'HIGH':
        recommendations.push('风险水平较高，建议减少仓位')
        recommendations.push('考虑增加对冲措施')
        break
      case 'EXTREME':
        recommendations.push('风险水平极高，建议大幅减仓')
        recommendations.push('暂停新增投资')
        recommendations.push('考虑止损退出')
        break
    }

    return recommendations
  }

  private generatePortfolioRiskRecommendations(volatility: number, concentrationRisk: number): string[] {
    const recommendations: string[] = []

    if (volatility > 0.25) {
      recommendations.push('投资组合波动率过高，建议增加低风险资产')
    }

    if (concentrationRisk > 0.5) {
      recommendations.push('投资组合过于集中，建议增加多元化')
    }

    return recommendations
  }

  // 投资组合风险计算方法
  private calculatePortfolioReturns(portfolioData: { [symbol: string]: { weight: number, data: TechnicalDataPoint[] } }): number[] {
    const symbols = Object.keys(portfolioData)
    const returns: number[] = []

    // 获取最短的数据长度
    const minLength = Math.min(...symbols.map(symbol => portfolioData[symbol].data.length))

    for (let i = 1; i < minLength; i++) {
      let portfolioReturn = 0
      symbols.forEach(symbol => {
        const data = portfolioData[symbol].data
        const weight = portfolioData[symbol].weight
        const stockReturn = (data[i].close - data[i - 1].close) / data[i - 1].close
        portfolioReturn += weight * stockReturn
      })
      returns.push(portfolioReturn)
    }

    return returns
  }

  private calculatePortfolioVolatility(portfolioData: { [symbol: string]: { weight: number, data: TechnicalDataPoint[] } }): number {
    const portfolioReturns = this.calculatePortfolioReturns(portfolioData)
    return this.calculateVolatility(portfolioReturns)
  }

  private calculatePortfolioVaR(returns: number[], confidence: number): number {
    return this.calculateVaR(returns, confidence)
  }

  private calculatePortfolioBeta(portfolioData: { [symbol: string]: { weight: number, data: TechnicalDataPoint[] } }): number {
    const symbols = Object.keys(portfolioData)
    let portfolioBeta = 0

    symbols.forEach(symbol => {
      const weight = portfolioData[symbol].weight
      const stockBeta = this.calculateBeta(this.calculateReturns(portfolioData[symbol].data.map(d => d.close)))
      portfolioBeta += weight * stockBeta
    })

    return portfolioBeta
  }

  private calculateRiskContribution(portfolioData: { [symbol: string]: { weight: number, data: TechnicalDataPoint[] } }): { [symbol: string]: number } {
    const symbols = Object.keys(portfolioData)
    const contributions: { [symbol: string]: number } = {}

    symbols.forEach(symbol => {
      const weight = portfolioData[symbol].weight
      const stockVol = this.calculateVolatility(this.calculateReturns(portfolioData[symbol].data.map(d => d.close)))
      contributions[symbol] = weight * stockVol
    })

    return contributions
  }

  private calculateDiversificationRatio(portfolioData: { [symbol: string]: { weight: number, data: TechnicalDataPoint[] } }): number {
    const symbols = Object.keys(portfolioData)
    let weightedAvgVol = 0

    symbols.forEach(symbol => {
      const weight = portfolioData[symbol].weight
      const stockVol = this.calculateVolatility(this.calculateReturns(portfolioData[symbol].data.map(d => d.close)))
      weightedAvgVol += weight * stockVol
    })

    const portfolioVol = this.calculatePortfolioVolatility(portfolioData)
    return weightedAvgVol / portfolioVol
  }

  private calculateCorrelationMatrix(portfolioData: { [symbol: string]: { weight: number, data: TechnicalDataPoint[] } }): { [key: string]: { [key: string]: number } } {
    const symbols = Object.keys(portfolioData)
    const matrix: { [key: string]: { [key: string]: number } } = {}

    symbols.forEach(symbol1 => {
      matrix[symbol1] = {}
      symbols.forEach(symbol2 => {
        if (symbol1 === symbol2) {
          matrix[symbol1][symbol2] = 1
        } else {
          // 简化实现，返回随机相关系数
          matrix[symbol1][symbol2] = Math.random() * 0.8 + 0.1
        }
      })
    })

    return matrix
  }

  private calculateConcentrationRisk(weights: number[]): number {
    // 使用Herfindahl指数计算集中度
    const herfindahl = weights.reduce((acc, weight) => acc + weight * weight, 0)
    return herfindahl
  }

  private async runPortfolioStressTests(portfolioData: { [symbol: string]: { weight: number, data: TechnicalDataPoint[] } }): Promise<any[]> {
    const stressTests = [
      {
        name: '市场下跌20%',
        impact: -0.20,
        description: '全市场下跌20%的冲击测试'
      },
      {
        name: '波动率上升50%',
        impact: -0.15,
        description: '市场波动率上升50%的冲击测试'
      },
      {
        name: '流动性危机',
        impact: -0.25,
        description: '流动性枯竭的冲击测试'
      }
    ]

    return stressTests
  }

  private initializeRiskModels(): void {
    // 初始化风险模型
    this.riskModels.set('VaR', { type: 'VaR', confidence: [0.95, 0.99] })
    this.riskModels.set('ES', { type: 'ExpectedShortfall', confidence: [0.95, 0.99] })
    this.riskModels.set('GARCH', { type: 'GARCH', parameters: { p: 1, q: 1 } })
  }

  private explainMarketRisk(volatility: number, beta: number, correlation: number): string {
    return `市场风险主要来源于系统性因素，当前波动率${(volatility * 100).toFixed(2)}%，贝塔系数${beta.toFixed(2)}，与市场相关性${(correlation * 100).toFixed(2)}%`
  }

  private explainLiquidityRisk(volumeVol: number, spread: number): string {
    return `流动性风险反映了资产变现的难易程度，当前成交量波动率${(volumeVol * 100).toFixed(2)}%，估计买卖价差${(spread * 100).toFixed(4)}%`
  }

  private explainVolatilityRisk(volatility: number, clustering: boolean): string {
    const clusteringText = clustering ? '存在波动率聚集现象' : '波动率相对稳定'
    return `波动率风险体现了价格变动的不确定性，当前年化波动率${(volatility * 100).toFixed(2)}%，${clusteringText}`
  }
}