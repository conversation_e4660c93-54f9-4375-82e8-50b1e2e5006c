import type {
  StockData,
  FeatureConfig,
  FeatureVector,
  FeatureImportance,
  FeatureEngineeringOptions,
  TechnicalIndicatorConfig,
  FeatureSelectionMethod,
  NormalizationMethod
} from '@/types/ai'

/**
 * 特征工程服务
 * 负责股票数据的特征提取、转换和选择
 */
export class FeatureEngineering {
  private cache: Map<string, FeatureVector[]> = new Map()
  private featureImportance: Map<string, FeatureImportance[]> = new Map()
  private normalizationParams: Map<string, any> = new Map()

  /**
   * 提取股票特征
   */
  async extractFeatures(
    stockData: StockData[],
    config: FeatureConfig
  ): Promise<FeatureVector[]> {
    const cacheKey = this.generateCacheKey(stockData, config)
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!
    }

    const features: FeatureVector[] = []

    for (const data of stockData) {
      const featureVector: FeatureVector = {
        symbol: data.symbol,
        timestamp: data.timestamp,
        features: {},
        metadata: {
          featureCount: 0,
          extractionTime: new Date(),
          config: config
        }
      }

      // 基础价格特征
      if (config.includeBasicFeatures) {
        await this.extractBasicFeatures(data, featureVector)
      }

      // 技术指标特征
      if (config.includeTechnicalIndicators) {
        await this.extractTechnicalFeatures(data, featureVector, config.technicalConfig)
      }

      // 统计特征
      if (config.includeStatisticalFeatures) {
        await this.extractStatisticalFeatures(data, featureVector)
      }

      // 基本面特征
      if (config.includeFundamentalFeatures) {
        await this.extractFundamentalFeatures(data, featureVector)
      }

      // 市场微观结构特征
      if (config.includeMarketMicrostructure) {
        await this.extractMicrostructureFeatures(data, featureVector)
      }

      // 情绪特征
      if (config.includeSentimentFeatures) {
        await this.extractSentimentFeatures(data, featureVector)
      }

      featureVector.metadata.featureCount = Object.keys(featureVector.features).length
      features.push(featureVector)
    }

    // 缓存结果
    this.cache.set(cacheKey, features)
    return features
  }

  /**
   * 提取基础价格特征
   */
  private async extractBasicFeatures(
    data: StockData,
    featureVector: FeatureVector
  ): Promise<void> {
    const { open, high, low, close, volume } = data

    // 价格特征
    featureVector.features.price_open = open
    featureVector.features.price_high = high
    featureVector.features.price_low = low
    featureVector.features.price_close = close
    featureVector.features.volume = volume

    // 价格变化特征
    featureVector.features.price_change = close - open
    featureVector.features.price_change_pct = (close - open) / open
    featureVector.features.high_low_ratio = high / low
    featureVector.features.close_open_ratio = close / open

    // 波动率特征
    featureVector.features.intraday_volatility = (high - low) / open
    featureVector.features.price_range = high - low
    featureVector.features.price_range_pct = (high - low) / open

    // 成交量特征
    if (data.avgVolume) {
      featureVector.features.volume_ratio = volume / data.avgVolume
      featureVector.features.volume_change = volume - data.avgVolume
    }

    // 价量关系
    featureVector.features.price_volume_trend = (close - open) * volume
    featureVector.features.volume_weighted_price = (high + low + close) / 3
  }

  /**
   * 提取技术指标特征
   */
  private async extractTechnicalFeatures(
    data: StockData,
    featureVector: FeatureVector,
    config?: TechnicalIndicatorConfig
  ): Promise<void> {
    const prices = data.historicalPrices || []
    if (prices.length < 20) return

    // 移动平均线
    featureVector.features.sma_5 = this.calculateSMA(prices, 5)
    featureVector.features.sma_10 = this.calculateSMA(prices, 10)
    featureVector.features.sma_20 = this.calculateSMA(prices, 20)
    featureVector.features.sma_50 = this.calculateSMA(prices, 50)

    // 指数移动平均线
    featureVector.features.ema_12 = this.calculateEMA(prices, 12)
    featureVector.features.ema_26 = this.calculateEMA(prices, 26)

    // MACD
    const macd = this.calculateMACD(prices)
    featureVector.features.macd = macd.macd
    featureVector.features.macd_signal = macd.signal
    featureVector.features.macd_histogram = macd.histogram

    // RSI
    featureVector.features.rsi_14 = this.calculateRSI(prices, 14)

    // 布林带
    const bollinger = this.calculateBollingerBands(prices, 20, 2)
    featureVector.features.bb_upper = bollinger.upper
    featureVector.features.bb_middle = bollinger.middle
    featureVector.features.bb_lower = bollinger.lower
    featureVector.features.bb_width = bollinger.width
    featureVector.features.bb_position = bollinger.position

    // KDJ
    const kdj = this.calculateKDJ(prices)
    featureVector.features.k_value = kdj.k
    featureVector.features.d_value = kdj.d
    featureVector.features.j_value = kdj.j

    // 威廉指标
    featureVector.features.williams_r = this.calculateWilliamsR(prices, 14)

    // 动量指标
    featureVector.features.momentum_10 = this.calculateMomentum(prices, 10)
    featureVector.features.roc_10 = this.calculateROC(prices, 10)

    // 平均真实波幅
    featureVector.features.atr_14 = this.calculateATR(prices, 14)

    // 成交量指标
    if (data.volumeHistory) {
      featureVector.features.obv = this.calculateOBV(prices, data.volumeHistory)
      featureVector.features.volume_sma_20 = this.calculateSMA(data.volumeHistory, 20)
    }
  }

  /**
   * 提取统计特征
   */
  private async extractStatisticalFeatures(
    data: StockData,
    featureVector: FeatureVector
  ): Promise<void> {
    const prices = data.historicalPrices || []
    if (prices.length < 10) return

    // 基础统计量
    featureVector.features.price_mean = this.calculateMean(prices)
    featureVector.features.price_std = this.calculateStd(prices)
    featureVector.features.price_variance = this.calculateVariance(prices)
    featureVector.features.price_skewness = this.calculateSkewness(prices)
    featureVector.features.price_kurtosis = this.calculateKurtosis(prices)

    // 分位数
    featureVector.features.price_q25 = this.calculateQuantile(prices, 0.25)
    featureVector.features.price_q50 = this.calculateQuantile(prices, 0.5)
    featureVector.features.price_q75 = this.calculateQuantile(prices, 0.75)

    // 极值特征
    featureVector.features.price_min = Math.min(...prices)
    featureVector.features.price_max = Math.max(...prices)
    featureVector.features.price_range_stat = Math.max(...prices) - Math.min(...prices)

    // 趋势特征
    featureVector.features.price_trend = this.calculateTrend(prices)
    featureVector.features.price_autocorr = this.calculateAutocorrelation(prices, 1)

    // 波动率特征
    const returns = this.calculateReturns(prices)
    featureVector.features.return_volatility = this.calculateStd(returns)
    featureVector.features.return_mean = this.calculateMean(returns)
    featureVector.features.return_skewness = this.calculateSkewness(returns)
    featureVector.features.return_kurtosis = this.calculateKurtosis(returns)

    // 风险度量
    featureVector.features.var_95 = this.calculateVaR(returns, 0.05)
    featureVector.features.cvar_95 = this.calculateCVaR(returns, 0.05)
    featureVector.features.max_drawdown = this.calculateMaxDrawdown(prices)
    featureVector.features.sharpe_ratio = this.calculateSharpeRatio(returns)
  }

  /**
   * 提取基本面特征
   */
  private async extractFundamentalFeatures(
    data: StockData,
    featureVector: FeatureVector
  ): Promise<void> {
    const fundamental = data.fundamentalData
    if (!fundamental) return

    // 估值指标
    featureVector.features.pe_ratio = fundamental.peRatio
    featureVector.features.pb_ratio = fundamental.pbRatio
    featureVector.features.ps_ratio = fundamental.psRatio
    featureVector.features.pcf_ratio = fundamental.pcfRatio
    featureVector.features.peg_ratio = fundamental.pegRatio

    // 盈利能力
    featureVector.features.roe = fundamental.roe
    featureVector.features.roa = fundamental.roa
    featureVector.features.gross_margin = fundamental.grossMargin
    featureVector.features.operating_margin = fundamental.operatingMargin
    featureVector.features.net_margin = fundamental.netMargin

    // 成长性指标
    featureVector.features.revenue_growth = fundamental.revenueGrowth
    featureVector.features.earnings_growth = fundamental.earningsGrowth
    featureVector.features.book_value_growth = fundamental.bookValueGrowth

    // 财务健康度
    featureVector.features.debt_to_equity = fundamental.debtToEquity
    featureVector.features.current_ratio = fundamental.currentRatio
    featureVector.features.quick_ratio = fundamental.quickRatio
    featureVector.features.cash_ratio = fundamental.cashRatio

    // 效率指标
    featureVector.features.asset_turnover = fundamental.assetTurnover
    featureVector.features.inventory_turnover = fundamental.inventoryTurnover
    featureVector.features.receivables_turnover = fundamental.receivablesTurnover

    // 分红指标
    featureVector.features.dividend_yield = fundamental.dividendYield
    featureVector.features.dividend_payout_ratio = fundamental.dividendPayoutRatio
  }

  /**
   * 提取市场微观结构特征
   */
  private async extractMicrostructureFeatures(
    data: StockData,
    featureVector: FeatureVector
  ): Promise<void> {
    const orderBook = data.orderBook
    if (!orderBook) return

    // 买卖价差
    featureVector.features.bid_ask_spread = orderBook.ask - orderBook.bid
    featureVector.features.bid_ask_spread_pct = (orderBook.ask - orderBook.bid) / orderBook.mid

    // 订单簿深度
    featureVector.features.bid_depth = orderBook.bidDepth
    featureVector.features.ask_depth = orderBook.askDepth
    featureVector.features.depth_imbalance = (orderBook.bidDepth - orderBook.askDepth) / (orderBook.bidDepth + orderBook.askDepth)

    // 成交量分布
    if (data.volumeProfile) {
      featureVector.features.volume_at_bid = data.volumeProfile.bidVolume
      featureVector.features.volume_at_ask = data.volumeProfile.askVolume
      featureVector.features.volume_imbalance = (data.volumeProfile.bidVolume - data.volumeProfile.askVolume) / data.volume
    }

    // 价格影响
    featureVector.features.price_impact = this.calculatePriceImpact(data)
    featureVector.features.market_impact = this.calculateMarketImpact(data)
  }

  /**
   * 提取情绪特征
   */
  private async extractSentimentFeatures(
    data: StockData,
    featureVector: FeatureVector
  ): Promise<void> {
    const sentiment = data.sentimentData
    if (!sentiment) return

    // 新闻情绪
    featureVector.features.news_sentiment = sentiment.newsScore
    featureVector.features.news_volume = sentiment.newsVolume
    featureVector.features.news_impact = sentiment.newsImpact

    // 社交媒体情绪
    featureVector.features.social_sentiment = sentiment.socialScore
    featureVector.features.social_volume = sentiment.socialVolume
    featureVector.features.social_buzz = sentiment.socialBuzz

    // 分析师情绪
    featureVector.features.analyst_sentiment = sentiment.analystScore
    featureVector.features.analyst_upgrades = sentiment.analystUpgrades
    featureVector.features.analyst_downgrades = sentiment.analystDowngrades

    // 内部人士情绪
    featureVector.features.insider_sentiment = sentiment.insiderScore
    featureVector.features.insider_buying = sentiment.insiderBuying
    featureVector.features.insider_selling = sentiment.insiderSelling

    // 综合情绪指标
    featureVector.features.overall_sentiment = sentiment.overallScore
    featureVector.features.sentiment_momentum = sentiment.momentum
    featureVector.features.sentiment_volatility = sentiment.volatility
  }

  /**
   * 特征选择
   */
  async selectFeatures(
    features: FeatureVector[],
    method: FeatureSelectionMethod,
    targetVariable?: string,
    topK?: number
  ): Promise<{
    selectedFeatures: string[]
    importance: FeatureImportance[]
    reducedFeatures: FeatureVector[]
  }> {
    let selectedFeatures: string[] = []
    let importance: FeatureImportance[] = []

    switch (method) {
      case 'correlation':
        ({ selectedFeatures, importance } = await this.correlationBasedSelection(features, targetVariable, topK))
        break
      case 'mutual_information':
        ({ selectedFeatures, importance } = await this.mutualInformationSelection(features, targetVariable, topK))
        break
      case 'chi_square':
        ({ selectedFeatures, importance } = await this.chiSquareSelection(features, targetVariable, topK))
        break
      case 'recursive_elimination':
        ({ selectedFeatures, importance } = await this.recursiveFeatureElimination(features, targetVariable, topK))
        break
      case 'lasso':
        ({ selectedFeatures, importance } = await this.lassoFeatureSelection(features, targetVariable, topK))
        break
      case 'random_forest':
        ({ selectedFeatures, importance } = await this.randomForestFeatureSelection(features, targetVariable, topK))
        break
      default:
        selectedFeatures = Object.keys(features[0]?.features || {})
    }

    // 生成降维后的特征向量
    const reducedFeatures = features.map(fv => ({
      ...fv,
      features: Object.fromEntries(
        Object.entries(fv.features).filter(([key]) => selectedFeatures.includes(key))
      )
    }))

    // 缓存特征重要性
    this.featureImportance.set(method, importance)

    return {
      selectedFeatures,
      importance,
      reducedFeatures
    }
  }

  /**
   * 数据标准化
   */
  async normalizeFeatures(
    features: FeatureVector[],
    method: NormalizationMethod,
    fitParams?: any
  ): Promise<{
    normalizedFeatures: FeatureVector[]
    normalizationParams: any
  }> {
    const featureNames = Object.keys(features[0]?.features || {})
    const normalizationParams: any = {}

    // 计算标准化参数
    for (const featureName of featureNames) {
      const values = features.map(f => f.features[featureName]).filter(v => v !== undefined && !isNaN(v))
      
      switch (method) {
        case 'standard':
          normalizationParams[featureName] = {
            mean: this.calculateMean(values),
            std: this.calculateStd(values)
          }
          break
        case 'minmax':
          normalizationParams[featureName] = {
            min: Math.min(...values),
            max: Math.max(...values)
          }
          break
        case 'robust':
          normalizationParams[featureName] = {
            median: this.calculateQuantile(values, 0.5),
            iqr: this.calculateQuantile(values, 0.75) - this.calculateQuantile(values, 0.25)
          }
          break
      }
    }

    // 应用标准化
    const normalizedFeatures = features.map(fv => ({
      ...fv,
      features: Object.fromEntries(
        Object.entries(fv.features).map(([key, value]) => {
          if (typeof value !== 'number' || isNaN(value)) return [key, value]
          
          const params = normalizationParams[key]
          let normalizedValue = value

          switch (method) {
            case 'standard':
              normalizedValue = (value - params.mean) / (params.std || 1)
              break
            case 'minmax':
              normalizedValue = (value - params.min) / (params.max - params.min || 1)
              break
            case 'robust':
              normalizedValue = (value - params.median) / (params.iqr || 1)
              break
          }

          return [key, normalizedValue]
        })
      )
    }))

    // 缓存标准化参数
    this.normalizationParams.set(method, normalizationParams)

    return {
      normalizedFeatures,
      normalizationParams
    }
  }

  // 技术指标计算方法
  private calculateSMA(prices: number[], period: number): number {
    if (prices.length < period) return NaN
    const sum = prices.slice(-period).reduce((a, b) => a + b, 0)
    return sum / period
  }

  private calculateEMA(prices: number[], period: number): number {
    if (prices.length < period) return NaN
    const multiplier = 2 / (period + 1)
    let ema = prices[0]
    
    for (let i = 1; i < prices.length; i++) {
      ema = (prices[i] * multiplier) + (ema * (1 - multiplier))
    }
    
    return ema
  }

  private calculateMACD(prices: number[]): { macd: number, signal: number, histogram: number } {
    const ema12 = this.calculateEMA(prices, 12)
    const ema26 = this.calculateEMA(prices, 26)
    const macd = ema12 - ema26
    
    // 简化的信号线计算
    const signal = this.calculateEMA([macd], 9)
    const histogram = macd - signal
    
    return { macd, signal, histogram }
  }

  private calculateRSI(prices: number[], period: number): number {
    if (prices.length < period + 1) return NaN
    
    let gains = 0
    let losses = 0
    
    for (let i = 1; i <= period; i++) {
      const change = prices[i] - prices[i - 1]
      if (change > 0) gains += change
      else losses -= change
    }
    
    const avgGain = gains / period
    const avgLoss = losses / period
    const rs = avgGain / (avgLoss || 1)
    
    return 100 - (100 / (1 + rs))
  }

  private calculateBollingerBands(prices: number[], period: number, stdDev: number): {
    upper: number
    middle: number
    lower: number
    width: number
    position: number
  } {
    const sma = this.calculateSMA(prices, period)
    const std = this.calculateStd(prices.slice(-period))
    const upper = sma + (std * stdDev)
    const lower = sma - (std * stdDev)
    const currentPrice = prices[prices.length - 1]
    
    return {
      upper,
      middle: sma,
      lower,
      width: upper - lower,
      position: (currentPrice - lower) / (upper - lower)
    }
  }

  private calculateKDJ(prices: number[]): { k: number, d: number, j: number } {
    // 简化的KDJ计算
    const period = 9
    if (prices.length < period) return { k: NaN, d: NaN, j: NaN }
    
    const recentPrices = prices.slice(-period)
    const high = Math.max(...recentPrices)
    const low = Math.min(...recentPrices)
    const close = prices[prices.length - 1]
    
    const rsv = ((close - low) / (high - low)) * 100
    const k = rsv * (2/3) + 50 * (1/3) // 简化计算
    const d = k * (2/3) + 50 * (1/3)   // 简化计算
    const j = 3 * k - 2 * d
    
    return { k, d, j }
  }

  private calculateWilliamsR(prices: number[], period: number): number {
    if (prices.length < period) return NaN
    
    const recentPrices = prices.slice(-period)
    const high = Math.max(...recentPrices)
    const low = Math.min(...recentPrices)
    const close = prices[prices.length - 1]
    
    return ((high - close) / (high - low)) * -100
  }

  private calculateMomentum(prices: number[], period: number): number {
    if (prices.length < period + 1) return NaN
    return prices[prices.length - 1] - prices[prices.length - 1 - period]
  }

  private calculateROC(prices: number[], period: number): number {
    if (prices.length < period + 1) return NaN
    const current = prices[prices.length - 1]
    const previous = prices[prices.length - 1 - period]
    return ((current - previous) / previous) * 100
  }

  private calculateATR(prices: number[], period: number): number {
    // 简化的ATR计算，假设prices包含high, low, close信息
    // 这里使用价格变动的标准差作为近似
    if (prices.length < period + 1) return NaN
    
    const trueRanges = []
    for (let i = 1; i < prices.length; i++) {
      trueRanges.push(Math.abs(prices[i] - prices[i - 1]))
    }
    
    return this.calculateSMA(trueRanges, period)
  }

  private calculateOBV(prices: number[], volumes: number[]): number {
    if (prices.length !== volumes.length || prices.length < 2) return NaN
    
    let obv = volumes[0]
    for (let i = 1; i < prices.length; i++) {
      if (prices[i] > prices[i - 1]) {
        obv += volumes[i]
      } else if (prices[i] < prices[i - 1]) {
        obv -= volumes[i]
      }
    }
    
    return obv
  }

  // 统计计算方法
  private calculateMean(values: number[]): number {
    return values.reduce((a, b) => a + b, 0) / values.length
  }

  private calculateStd(values: number[]): number {
    const mean = this.calculateMean(values)
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
    return Math.sqrt(variance)
  }

  private calculateVariance(values: number[]): number {
    const mean = this.calculateMean(values)
    return values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
  }

  private calculateSkewness(values: number[]): number {
    const mean = this.calculateMean(values)
    const std = this.calculateStd(values)
    const n = values.length
    
    const skewness = values.reduce((sum, val) => {
      return sum + Math.pow((val - mean) / std, 3)
    }, 0) / n
    
    return skewness
  }

  private calculateKurtosis(values: number[]): number {
    const mean = this.calculateMean(values)
    const std = this.calculateStd(values)
    const n = values.length
    
    const kurtosis = values.reduce((sum, val) => {
      return sum + Math.pow((val - mean) / std, 4)
    }, 0) / n
    
    return kurtosis - 3 // 超额峰度
  }

  private calculateQuantile(values: number[], q: number): number {
    const sorted = [...values].sort((a, b) => a - b)
    const index = (sorted.length - 1) * q
    const lower = Math.floor(index)
    const upper = Math.ceil(index)
    const weight = index - lower
    
    return sorted[lower] * (1 - weight) + sorted[upper] * weight
  }

  private calculateTrend(values: number[]): number {
    const n = values.length
    const x = Array.from({ length: n }, (_, i) => i)
    const y = values
    
    const sumX = x.reduce((a, b) => a + b, 0)
    const sumY = y.reduce((a, b) => a + b, 0)
    const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0)
    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0)
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX)
    return slope
  }

  private calculateAutocorrelation(values: number[], lag: number): number {
    if (values.length <= lag) return NaN
    
    const mean = this.calculateMean(values)
    const n = values.length - lag
    
    let numerator = 0
    let denominator = 0
    
    for (let i = 0; i < n; i++) {
      numerator += (values[i] - mean) * (values[i + lag] - mean)
    }
    
    for (let i = 0; i < values.length; i++) {
      denominator += Math.pow(values[i] - mean, 2)
    }
    
    return numerator / denominator
  }

  private calculateReturns(prices: number[]): number[] {
    const returns = []
    for (let i = 1; i < prices.length; i++) {
      returns.push((prices[i] - prices[i - 1]) / prices[i - 1])
    }
    return returns
  }

  private calculateVaR(returns: number[], alpha: number): number {
    const sorted = [...returns].sort((a, b) => a - b)
    const index = Math.floor(alpha * sorted.length)
    return sorted[index]
  }

  private calculateCVaR(returns: number[], alpha: number): number {
    const var = this.calculateVaR(returns, alpha)
    const tailReturns = returns.filter(r => r <= var)
    return this.calculateMean(tailReturns)
  }

  private calculateMaxDrawdown(prices: number[]): number {
    let maxDrawdown = 0
    let peak = prices[0]
    
    for (const price of prices) {
      if (price > peak) {
        peak = price
      } else {
        const drawdown = (peak - price) / peak
        maxDrawdown = Math.max(maxDrawdown, drawdown)
      }
    }
    
    return maxDrawdown
  }

  private calculateSharpeRatio(returns: number[], riskFreeRate: number = 0): number {
    const meanReturn = this.calculateMean(returns)
    const stdReturn = this.calculateStd(returns)
    return (meanReturn - riskFreeRate) / stdReturn
  }

  private calculatePriceImpact(data: StockData): number {
    // 简化的价格影响计算
    if (!data.orderBook || !data.volume) return NaN
    
    const spread = data.orderBook.ask - data.orderBook.bid
    const volumeRatio = data.volume / (data.avgVolume || data.volume)
    
    return spread * volumeRatio
  }

  private calculateMarketImpact(data: StockData): number {
    // 简化的市场影响计算
    if (!data.volume || !data.avgVolume) return NaN
    
    const volumeShock = data.volume / data.avgVolume
    const priceChange = Math.abs((data.close - data.open) / data.open)
    
    return priceChange * Math.sqrt(volumeShock)
  }

  // 特征选择方法实现
  private async correlationBasedSelection(
    features: FeatureVector[],
    targetVariable?: string,
    topK?: number
  ): Promise<{ selectedFeatures: string[], importance: FeatureImportance[] }> {
    // 实现基于相关性的特征选择
    const featureNames = Object.keys(features[0]?.features || {})
    const importance: FeatureImportance[] = []
    
    // 计算每个特征与目标变量的相关性
    for (const featureName of featureNames) {
      const featureValues = features.map(f => f.features[featureName]).filter(v => v !== undefined && !isNaN(v))
      // 这里需要目标变量值，简化处理
      const correlation = Math.random() // 占位符
      
      importance.push({
        feature: featureName,
        importance: Math.abs(correlation),
        rank: 0,
        method: 'correlation'
      })
    }
    
    // 排序并选择top K
    importance.sort((a, b) => b.importance - a.importance)
    importance.forEach((item, index) => item.rank = index + 1)
    
    const selectedFeatures = importance
      .slice(0, topK || featureNames.length)
      .map(item => item.feature)
    
    return { selectedFeatures, importance }
  }

  private async mutualInformationSelection(
    features: FeatureVector[],
    targetVariable?: string,
    topK?: number
  ): Promise<{ selectedFeatures: string[], importance: FeatureImportance[] }> {
    // 实现基于互信息的特征选择
    const featureNames = Object.keys(features[0]?.features || {})
    const importance: FeatureImportance[] = featureNames.map(name => ({
      feature: name,
      importance: Math.random(), // 占位符
      rank: 0,
      method: 'mutual_information'
    }))
    
    importance.sort((a, b) => b.importance - a.importance)
    importance.forEach((item, index) => item.rank = index + 1)
    
    const selectedFeatures = importance
      .slice(0, topK || featureNames.length)
      .map(item => item.feature)
    
    return { selectedFeatures, importance }
  }

  private async chiSquareSelection(
    features: FeatureVector[],
    targetVariable?: string,
    topK?: number
  ): Promise<{ selectedFeatures: string[], importance: FeatureImportance[] }> {
    // 实现卡方检验特征选择
    const featureNames = Object.keys(features[0]?.features || {})
    const importance: FeatureImportance[] = featureNames.map(name => ({
      feature: name,
      importance: Math.random(), // 占位符
      rank: 0,
      method: 'chi_square'
    }))
    
    importance.sort((a, b) => b.importance - a.importance)
    importance.forEach((item, index) => item.rank = index + 1)
    
    const selectedFeatures = importance
      .slice(0, topK || featureNames.length)
      .map(item => item.feature)
    
    return { selectedFeatures, importance }
  }

  private async recursiveFeatureElimination(
    features: FeatureVector[],
    targetVariable?: string,
    topK?: number
  ): Promise<{ selectedFeatures: string[], importance: FeatureImportance[] }> {
    // 实现递归特征消除
    const featureNames = Object.keys(features[0]?.features || {})
    const importance: FeatureImportance[] = featureNames.map(name => ({
      feature: name,
      importance: Math.random(), // 占位符
      rank: 0,
      method: 'recursive_elimination'
    }))
    
    importance.sort((a, b) => b.importance - a.importance)
    importance.forEach((item, index) => item.rank = index + 1)
    
    const selectedFeatures = importance
      .slice(0, topK || featureNames.length)
      .map(item => item.feature)
    
    return { selectedFeatures, importance }
  }

  private async lassoFeatureSelection(
    features: FeatureVector[],
    targetVariable?: string,
    topK?: number
  ): Promise<{ selectedFeatures: string[], importance: FeatureImportance[] }> {
    // 实现LASSO特征选择
    const featureNames = Object.keys(features[0]?.features || {})
    const importance: FeatureImportance[] = featureNames.map(name => ({
      feature: name,
      importance: Math.random(), // 占位符
      rank: 0,
      method: 'lasso'
    }))
    
    importance.sort((a, b) => b.importance - a.importance)
    importance.forEach((item, index) => item.rank = index + 1)
    
    const selectedFeatures = importance
      .slice(0, topK || featureNames.length)
      .map(item => item.feature)
    
    return { selectedFeatures, importance }
  }

  private async randomForestFeatureSelection(
    features: FeatureVector[],
    targetVariable?: string,
    topK?: number
  ): Promise<{ selectedFeatures: string[], importance: FeatureImportance[] }> {
    // 实现随机森林特征选择
    const featureNames = Object.keys(features[0]?.features || {})
    const importance: FeatureImportance[] = featureNames.map(name => ({
      feature: name,
      importance: Math.random(), // 占位符
      rank: 0,
      method: 'random_forest'
    }))
    
    importance.sort((a, b) => b.importance - a.importance)
    importance.forEach((item, index) => item.rank = index + 1)
    
    const selectedFeatures = importance
      .slice(0, topK || featureNames.length)
      .map(item => item.feature)
    
    return { selectedFeatures, importance }
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(stockData: StockData[], config: FeatureConfig): string {
    const dataHash = stockData.map(d => `${d.symbol}_${d.timestamp}`).join('|')
    const configHash = JSON.stringify(config)
    return `${dataHash}_${configHash}`
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    this.cache.clear()
    this.featureImportance.clear()
    this.normalizationParams.clear()
  }

  /**
   * 获取特征重要性
   */
  getFeatureImportance(method: FeatureSelectionMethod): FeatureImportance[] | undefined {
    return this.featureImportance.get(method)
  }

  /**
   * 获取标准化参数
   */
  getNormalizationParams(method: NormalizationMethod): any {
    return this.normalizationParams.get(method)
  }
} 