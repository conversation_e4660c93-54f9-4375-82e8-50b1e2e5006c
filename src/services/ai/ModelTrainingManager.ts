import type {
  TrainingConfig,
  TrainingData,
  TrainingMetrics,
  ModelVersion,
  TrainingJob,
  ValidationResult,
  HyperParameters,
  TrainingProgress,
  ModelArtifact,
  ExperimentConfig
} from '@/types/ai'

/**
 * 模型训练管理器
 * 负责模型训练数据管理、训练流程控制和模型版本管理
 */
export class ModelTrainingManager {
  private trainingJobs: Map<string, TrainingJob> = new Map()
  private modelVersions: Map<string, ModelVersion[]> = new Map()
  private trainingData: Map<string, TrainingData> = new Map()
  private experiments: Map<string, ExperimentConfig> = new Map()
  private isTraining: boolean = false

  /**
   * 创建训练作业
   */
  async createTrainingJob(config: TrainingConfig): Promise<string> {
    const jobId = this.generateJobId()
    
    const trainingJob: TrainingJob = {
      id: jobId,
      modelName: config.modelName,
      config,
      status: 'pending',
      progress: {
        currentEpoch: 0,
        totalEpochs: config.epochs || 100,
        currentLoss: 0,
        bestLoss: Infinity,
        accuracy: 0,
        validationLoss: 0,
        validationAccuracy: 0,
        learningRate: config.hyperParameters?.learningRate || 0.001,
        startTime: new Date(),
        estimatedEndTime: null
      },
      metrics: {
        trainLoss: [],
        validationLoss: [],
        accuracy: [],
        validationAccuracy: [],
        learningRate: [],
        epochTimes: []
      },
      artifacts: [],
      logs: [],
      createdAt: new Date(),
      updatedAt: new Date()
    }

    this.trainingJobs.set(jobId, trainingJob)
    return jobId
  }

  /**
   * 启动训练作业
   */
  async startTraining(jobId: string): Promise<void> {
    const job = this.trainingJobs.get(jobId)
    if (!job) {
      throw new Error(`Training job ${jobId} not found`)
    }

    if (job.status !== 'pending') {
      throw new Error(`Training job ${jobId} is not in pending status`)
    }

    try {
      job.status = 'running'
      job.progress.startTime = new Date()
      job.updatedAt = new Date()

      // 准备训练数据
      await this.prepareTrainingData(job)

      // 开始训练循环
      await this.runTrainingLoop(job)

    } catch (error) {
      job.status = 'failed'
      job.error = error instanceof Error ? error.message : 'Unknown error'
      job.updatedAt = new Date()
      throw error
    }
  }

  /**
   * 停止训练作业
   */
  async stopTraining(jobId: string): Promise<void> {
    const job = this.trainingJobs.get(jobId)
    if (!job) {
      throw new Error(`Training job ${jobId} not found`)
    }

    if (job.status === 'running') {
      job.status = 'stopped'
      job.progress.endTime = new Date()
      job.updatedAt = new Date()
      
      // 保存当前模型状态
      await this.saveModelCheckpoint(job)
    }
  }

  /**
   * 获取训练进度
   */
  getTrainingProgress(jobId: string): TrainingProgress | null {
    const job = this.trainingJobs.get(jobId)
    return job?.progress || null
  }

  /**
   * 获取训练指标
   */
  getTrainingMetrics(jobId: string): TrainingMetrics | null {
    const job = this.trainingJobs.get(jobId)
    return job?.metrics || null
  }

  /**
   * 验证模型
   */
  async validateModel(jobId: string, validationData?: TrainingData): Promise<ValidationResult> {
    const job = this.trainingJobs.get(jobId)
    if (!job) {
      throw new Error(`Training job ${jobId} not found`)
    }

    const startTime = Date.now()
    
    try {
      // 使用验证数据集或默认验证数据
      const valData = validationData || await this.getValidationData(job.modelName)
      
      // 运行验证
      const predictions = await this.runInference(job, valData)
      const metrics = await this.calculateValidationMetrics(valData, predictions)

      const validationResult: ValidationResult = {
        jobId,
        accuracy: metrics.accuracy,
        precision: metrics.precision,
        recall: metrics.recall,
        f1Score: metrics.f1Score,
        loss: metrics.loss,
        confusionMatrix: metrics.confusionMatrix,
        rocAuc: metrics.rocAuc,
        validationTime: Date.now() - startTime,
        timestamp: new Date()
      }

      // 更新作业验证结果
      job.validationResult = validationResult
      job.updatedAt = new Date()

      return validationResult

    } catch (error) {
      throw new Error(`Model validation failed: ${error}`)
    }
  }

  /**
   * 保存模型版本
   */
  async saveModelVersion(
    jobId: string, 
    version: string, 
    description?: string
  ): Promise<ModelVersion> {
    const job = this.trainingJobs.get(jobId)
    if (!job) {
      throw new Error(`Training job ${jobId} not found`)
    }

    const modelVersion: ModelVersion = {
      id: this.generateVersionId(),
      modelName: job.modelName,
      version,
      description: description || `Model version ${version}`,
      config: job.config,
      metrics: job.metrics,
      validationResult: job.validationResult,
      artifacts: job.artifacts,
      status: 'active',
      createdAt: new Date(),
      createdBy: 'system'
    }

    // 保存到版本历史
    if (!this.modelVersions.has(job.modelName)) {
      this.modelVersions.set(job.modelName, [])
    }
    this.modelVersions.get(job.modelName)!.push(modelVersion)

    // 保存模型文件
    await this.saveModelArtifacts(modelVersion)

    return modelVersion
  }

  /**
   * 获取模型版本列表
   */
  getModelVersions(modelName: string): ModelVersion[] {
    return this.modelVersions.get(modelName) || []
  }

  /**
   * 部署模型版本
   */
  async deployModel(modelName: string, version: string): Promise<void> {
    const versions = this.modelVersions.get(modelName)
    if (!versions) {
      throw new Error(`Model ${modelName} not found`)
    }

    const targetVersion = versions.find(v => v.version === version)
    if (!targetVersion) {
      throw new Error(`Model version ${version} not found`)
    }

    // 停用其他版本
    versions.forEach(v => {
      if (v.version !== version) {
        v.status = 'inactive'
      }
    })

    // 激活目标版本
    targetVersion.status = 'deployed'
    targetVersion.deployedAt = new Date()

    // 执行部署逻辑
    await this.executeModelDeployment(targetVersion)
  }

  /**
   * 超参数优化
   */
  async optimizeHyperParameters(
    modelName: string,
    parameterSpace: Record<string, any[]>,
    maxTrials: number = 50
  ): Promise<HyperParameters> {
    const experimentId = this.generateExperimentId()
    
    const experiment: ExperimentConfig = {
      id: experimentId,
      modelName,
      type: 'hyperparameter_optimization',
      parameterSpace,
      maxTrials,
      trials: [],
      bestTrial: null,
      status: 'running',
      createdAt: new Date()
    }

    this.experiments.set(experimentId, experiment)

    try {
      // 运行超参数优化
      const bestParams = await this.runHyperParameterOptimization(experiment)
      
      experiment.status = 'completed'
      experiment.completedAt = new Date()
      
      return bestParams

    } catch (error) {
      experiment.status = 'failed'
      experiment.error = error instanceof Error ? error.message : 'Unknown error'
      throw error
    }
  }

  /**
   * 自动化特征选择
   */
  async selectFeatures(
    trainingData: TrainingData,
    method: 'correlation' | 'mutual_info' | 'recursive' | 'lasso' = 'correlation',
    maxFeatures?: number
  ): Promise<{
    selectedFeatures: string[]
    featureImportance: Record<string, number>
    selectionMetrics: any
  }> {
    const startTime = Date.now()

    try {
      let selectedFeatures: string[] = []
      let featureImportance: Record<string, number> = {}
      let selectionMetrics: any = {}

      switch (method) {
        case 'correlation':
          ({ selectedFeatures, featureImportance, selectionMetrics } = 
            await this.correlationFeatureSelection(trainingData, maxFeatures))
          break

        case 'mutual_info':
          ({ selectedFeatures, featureImportance, selectionMetrics } = 
            await this.mutualInfoFeatureSelection(trainingData, maxFeatures))
          break

        case 'recursive':
          ({ selectedFeatures, featureImportance, selectionMetrics } = 
            await this.recursiveFeatureSelection(trainingData, maxFeatures))
          break

        case 'lasso':
          ({ selectedFeatures, featureImportance, selectionMetrics } = 
            await this.lassoFeatureSelection(trainingData, maxFeatures))
          break
      }

      selectionMetrics.selectionTime = Date.now() - startTime
      selectionMetrics.method = method
      selectionMetrics.originalFeatureCount = trainingData.features?.length || 0
      selectionMetrics.selectedFeatureCount = selectedFeatures.length

      return {
        selectedFeatures,
        featureImportance,
        selectionMetrics
      }

    } catch (error) {
      throw new Error(`Feature selection failed: ${error}`)
    }
  }

  /**
   * 交叉验证
   */
  async crossValidate(
    config: TrainingConfig,
    folds: number = 5
  ): Promise<{
    meanAccuracy: number
    stdAccuracy: number
    foldResults: ValidationResult[]
  }> {
    const foldResults: ValidationResult[] = []
    
    try {
      // 准备交叉验证数据
      const trainingData = await this.getTrainingData(config.modelName)
      const folds_data = this.createCrossValidationFolds(trainingData, folds)

      // 对每个fold进行训练和验证
      for (let i = 0; i < folds; i++) {
        const foldConfig = { ...config, foldIndex: i }
        const jobId = await this.createTrainingJob(foldConfig)
        
        // 设置fold特定的训练和验证数据
        await this.setFoldData(jobId, folds_data[i])
        
        // 训练模型
        await this.startTraining(jobId)
        
        // 验证模型
        const validationResult = await this.validateModel(jobId, folds_data[i].validation)
        foldResults.push(validationResult)
      }

      // 计算交叉验证统计
      const accuracies = foldResults.map(r => r.accuracy)
      const meanAccuracy = accuracies.reduce((a, b) => a + b, 0) / accuracies.length
      const stdAccuracy = Math.sqrt(
        accuracies.reduce((sum, acc) => sum + Math.pow(acc - meanAccuracy, 2), 0) / accuracies.length
      )

      return {
        meanAccuracy,
        stdAccuracy,
        foldResults
      }

    } catch (error) {
      throw new Error(`Cross validation failed: ${error}`)
    }
  }

  // 私有方法实现

  /**
   * 准备训练数据
   */
  private async prepareTrainingData(job: TrainingJob): Promise<void> {
    const trainingData = await this.getTrainingData(job.modelName)
    
    // 数据预处理
    const processedData = await this.preprocessTrainingData(trainingData, job.config)
    
    // 数据分割
    const { trainData, validationData, testData } = this.splitTrainingData(processedData)
    
    // 保存处理后的数据
    this.trainingData.set(`${job.id}_train`, trainData)
    this.trainingData.set(`${job.id}_validation`, validationData)
    
    if (testData) {
      this.trainingData.set(`${job.id}_test`, testData)
    }
  }

  /**
   * 运行训练循环
   */
  private async runTrainingLoop(job: TrainingJob): Promise<void> {
    const trainData = this.trainingData.get(`${job.id}_train`)
    const validationData = this.trainingData.get(`${job.id}_validation`)
    
    if (!trainData) {
      throw new Error('Training data not found')
    }

    const totalEpochs = job.config.epochs || 100
    
    for (let epoch = 1; epoch <= totalEpochs; epoch++) {
      if (job.status !== 'running') {
        break // 训练被停止
      }

      const epochStartTime = Date.now()
      
      // 训练一个epoch
      const trainMetrics = await this.trainEpoch(job, trainData)
      
      // 验证
      let validationMetrics = null
      if (validationData && epoch % (job.config.validationInterval || 1) === 0) {
        validationMetrics = await this.validateEpoch(job, validationData)
      }

      // 更新进度和指标
      job.progress.currentEpoch = epoch
      job.progress.currentLoss = trainMetrics.loss
      job.progress.accuracy = trainMetrics.accuracy
      
      if (validationMetrics) {
        job.progress.validationLoss = validationMetrics.loss
        job.progress.validationAccuracy = validationMetrics.accuracy
      }

      // 记录指标
      job.metrics.trainLoss.push(trainMetrics.loss)
      job.metrics.accuracy.push(trainMetrics.accuracy)
      
      if (validationMetrics) {
        job.metrics.validationLoss.push(validationMetrics.loss)
        job.metrics.validationAccuracy.push(validationMetrics.accuracy)
      }

      const epochTime = Date.now() - epochStartTime
      job.metrics.epochTimes.push(epochTime)

      // 更新最佳损失
      if (trainMetrics.loss < job.progress.bestLoss) {
        job.progress.bestLoss = trainMetrics.loss
        
        // 保存最佳模型检查点
        await this.saveModelCheckpoint(job)
      }

      // 早停检查
      if (this.shouldEarlyStop(job)) {
        job.status = 'completed'
        break
      }

      // 学习率调度
      await this.updateLearningRate(job, epoch)

      job.updatedAt = new Date()
    }

    if (job.status === 'running') {
      job.status = 'completed'
      job.progress.endTime = new Date()
    }
  }

  /**
   * 训练一个epoch
   */
  private async trainEpoch(job: TrainingJob, trainData: TrainingData): Promise<any> {
    // 模拟训练过程
    await new Promise(resolve => setTimeout(resolve, 100))
    
    // 计算模拟指标
    const loss = Math.random() * 0.5 + 0.1
    const accuracy = 0.7 + Math.random() * 0.25
    
    return { loss, accuracy }
  }

  /**
   * 验证一个epoch
   */
  private async validateEpoch(job: TrainingJob, validationData: TrainingData): Promise<any> {
    // 模拟验证过程
    await new Promise(resolve => setTimeout(resolve, 50))
    
    // 计算模拟指标
    const loss = Math.random() * 0.6 + 0.15
    const accuracy = 0.65 + Math.random() * 0.3
    
    return { loss, accuracy }
  }

  /**
   * 早停检查
   */
  private shouldEarlyStop(job: TrainingJob): boolean {
    const patience = job.config.earlyStopping?.patience || 10
    const minDelta = job.config.earlyStopping?.minDelta || 0.001
    
    if (job.metrics.validationLoss.length < patience) {
      return false
    }

    const recentLosses = job.metrics.validationLoss.slice(-patience)
    const bestRecentLoss = Math.min(...recentLosses)
    const currentLoss = recentLosses[recentLosses.length - 1]
    
    return (currentLoss - bestRecentLoss) > minDelta
  }

  /**
   * 更新学习率
   */
  private async updateLearningRate(job: TrainingJob, epoch: number): Promise<void> {
    const scheduler = job.config.learningRateScheduler
    if (!scheduler) return

    let newLearningRate = job.progress.learningRate

    switch (scheduler.type) {
      case 'step':
        if (epoch % (scheduler.stepSize || 10) === 0) {
          newLearningRate *= (scheduler.gamma || 0.1)
        }
        break

      case 'exponential':
        newLearningRate *= (scheduler.gamma || 0.95)
        break

      case 'cosine':
        const maxEpochs = job.config.epochs || 100
        newLearningRate = (scheduler.minLr || 0) + 
          (job.progress.learningRate - (scheduler.minLr || 0)) * 
          (1 + Math.cos(Math.PI * epoch / maxEpochs)) / 2
        break
    }

    job.progress.learningRate = newLearningRate
    job.metrics.learningRate.push(newLearningRate)
  }

  /**
   * 保存模型检查点
   */
  private async saveModelCheckpoint(job: TrainingJob): Promise<void> {
    const checkpoint: ModelArtifact = {
      id: this.generateArtifactId(),
      jobId: job.id,
      type: 'checkpoint',
      name: `checkpoint_epoch_${job.progress.currentEpoch}`,
      path: `/models/${job.modelName}/checkpoints/`,
      size: Math.floor(Math.random() * 1000000), // 模拟文件大小
      metrics: {
        epoch: job.progress.currentEpoch,
        loss: job.progress.currentLoss,
        accuracy: job.progress.accuracy
      },
      createdAt: new Date()
    }

    job.artifacts.push(checkpoint)
  }

  /**
   * 运行推理
   */
  private async runInference(job: TrainingJob, data: TrainingData): Promise<any[]> {
    // 模拟推理过程
    await new Promise(resolve => setTimeout(resolve, 200))
    
    // 返回模拟预测结果
    return Array.from({ length: data.samples?.length || 100 }, () => Math.random())
  }

  /**
   * 计算验证指标
   */
  private async calculateValidationMetrics(
    validationData: TrainingData, 
    predictions: any[]
  ): Promise<any> {
    // 模拟指标计算
    return {
      accuracy: 0.8 + Math.random() * 0.15,
      precision: 0.75 + Math.random() * 0.2,
      recall: 0.7 + Math.random() * 0.25,
      f1Score: 0.72 + Math.random() * 0.23,
      loss: Math.random() * 0.4 + 0.1,
      confusionMatrix: [[80, 10], [5, 85]],
      rocAuc: 0.85 + Math.random() * 0.1
    }
  }

  /**
   * 保存模型制品
   */
  private async saveModelArtifacts(version: ModelVersion): Promise<void> {
    // 模拟保存模型文件
    const modelArtifact: ModelArtifact = {
      id: this.generateArtifactId(),
      jobId: version.id,
      type: 'model',
      name: `${version.modelName}_v${version.version}`,
      path: `/models/${version.modelName}/versions/${version.version}/`,
      size: Math.floor(Math.random() * 5000000),
      metrics: version.metrics,
      createdAt: new Date()
    }

    version.artifacts.push(modelArtifact)
  }

  /**
   * 执行模型部署
   */
  private async executeModelDeployment(version: ModelVersion): Promise<void> {
    // 模拟部署过程
    console.log(`Deploying model ${version.modelName} version ${version.version}`)
    await new Promise(resolve => setTimeout(resolve, 1000))
    console.log(`Model ${version.modelName} version ${version.version} deployed successfully`)
  }

  /**
   * 运行超参数优化
   */
  private async runHyperParameterOptimization(
    experiment: ExperimentConfig
  ): Promise<HyperParameters> {
    let bestParams: HyperParameters = {}
    let bestScore = -Infinity

    for (let trial = 0; trial < experiment.maxTrials; trial++) {
      // 生成随机参数组合
      const params = this.generateRandomParameters(experiment.parameterSpace)
      
      // 运行训练试验
      const score = await this.runTrainingTrial(experiment.modelName, params)
      
      // 记录试验结果
      experiment.trials.push({
        id: trial,
        parameters: params,
        score,
        timestamp: new Date()
      })

      // 更新最佳参数
      if (score > bestScore) {
        bestScore = score
        bestParams = params
        experiment.bestTrial = {
          id: trial,
          parameters: params,
          score,
          timestamp: new Date()
        }
      }
    }

    return bestParams
  }

  /**
   * 生成随机参数
   */
  private generateRandomParameters(parameterSpace: Record<string, any[]>): HyperParameters {
    const params: HyperParameters = {}
    
    for (const [key, values] of Object.entries(parameterSpace)) {
      const randomIndex = Math.floor(Math.random() * values.length)
      params[key] = values[randomIndex]
    }
    
    return params
  }

  /**
   * 运行训练试验
   */
  private async runTrainingTrial(modelName: string, params: HyperParameters): Promise<number> {
    // 模拟训练试验
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 返回模拟分数
    return Math.random()
  }

  // 特征选择方法实现

  private async correlationFeatureSelection(
    data: TrainingData, 
    maxFeatures?: number
  ): Promise<any> {
    // 模拟相关性特征选择
    const features = data.features || []
    const importance = features.reduce((acc, feature, index) => {
      acc[feature] = Math.random()
      return acc
    }, {} as Record<string, number>)

    const sortedFeatures = Object.entries(importance)
      .sort(([,a], [,b]) => b - a)
      .slice(0, maxFeatures)
      .map(([feature]) => feature)

    return {
      selectedFeatures: sortedFeatures,
      featureImportance: importance,
      selectionMetrics: { method: 'correlation' }
    }
  }

  private async mutualInfoFeatureSelection(
    data: TrainingData, 
    maxFeatures?: number
  ): Promise<any> {
    // 模拟互信息特征选择
    const features = data.features || []
    const importance = features.reduce((acc, feature, index) => {
      acc[feature] = Math.random()
      return acc
    }, {} as Record<string, number>)

    const sortedFeatures = Object.entries(importance)
      .sort(([,a], [,b]) => b - a)
      .slice(0, maxFeatures)
      .map(([feature]) => feature)

    return {
      selectedFeatures: sortedFeatures,
      featureImportance: importance,
      selectionMetrics: { method: 'mutual_info' }
    }
  }

  private async recursiveFeatureSelection(
    data: TrainingData, 
    maxFeatures?: number
  ): Promise<any> {
    // 模拟递归特征选择
    const features = data.features || []
    const importance = features.reduce((acc, feature, index) => {
      acc[feature] = Math.random()
      return acc
    }, {} as Record<string, number>)

    const sortedFeatures = Object.entries(importance)
      .sort(([,a], [,b]) => b - a)
      .slice(0, maxFeatures)
      .map(([feature]) => feature)

    return {
      selectedFeatures: sortedFeatures,
      featureImportance: importance,
      selectionMetrics: { method: 'recursive' }
    }
  }

  private async lassoFeatureSelection(
    data: TrainingData, 
    maxFeatures?: number
  ): Promise<any> {
    // 模拟LASSO特征选择
    const features = data.features || []
    const importance = features.reduce((acc, feature, index) => {
      acc[feature] = Math.random()
      return acc
    }, {} as Record<string, number>)

    const sortedFeatures = Object.entries(importance)
      .sort(([,a], [,b]) => b - a)
      .slice(0, maxFeatures)
      .map(([feature]) => feature)

    return {
      selectedFeatures: sortedFeatures,
      featureImportance: importance,
      selectionMetrics: { method: 'lasso' }
    }
  }

  // 辅助方法

  /**
   * 获取训练数据
   */
  private async getTrainingData(modelName: string): Promise<TrainingData> {
    // 模拟获取训练数据
    return {
      modelName,
      features: ['feature1', 'feature2', 'feature3', 'feature4', 'feature5'],
      samples: Array.from({ length: 1000 }, (_, i) => ({
        id: i,
        features: Array.from({ length: 5 }, () => Math.random()),
        target: Math.random() > 0.5 ? 1 : 0
      })),
      metadata: {
        sampleCount: 1000,
        featureCount: 5,
        createdAt: new Date()
      }
    }
  }

  /**
   * 获取验证数据
   */
  private async getValidationData(modelName: string): Promise<TrainingData> {
    // 模拟获取验证数据
    return {
      modelName,
      features: ['feature1', 'feature2', 'feature3', 'feature4', 'feature5'],
      samples: Array.from({ length: 200 }, (_, i) => ({
        id: i,
        features: Array.from({ length: 5 }, () => Math.random()),
        target: Math.random() > 0.5 ? 1 : 0
      })),
      metadata: {
        sampleCount: 200,
        featureCount: 5,
        createdAt: new Date()
      }
    }
  }

  /**
   * 预处理训练数据
   */
  private async preprocessTrainingData(
    data: TrainingData, 
    config: TrainingConfig
  ): Promise<TrainingData> {
    // 模拟数据预处理
    return data
  }

  /**
   * 分割训练数据
   */
  private splitTrainingData(data: TrainingData): {
    trainData: TrainingData
    validationData: TrainingData
    testData?: TrainingData
  } {
    const samples = data.samples || []
    const trainSize = Math.floor(samples.length * 0.7)
    const validationSize = Math.floor(samples.length * 0.2)
    
    return {
      trainData: {
        ...data,
        samples: samples.slice(0, trainSize)
      },
      validationData: {
        ...data,
        samples: samples.slice(trainSize, trainSize + validationSize)
      },
      testData: {
        ...data,
        samples: samples.slice(trainSize + validationSize)
      }
    }
  }

  /**
   * 创建交叉验证折叠
   */
  private createCrossValidationFolds(data: TrainingData, folds: number): any[] {
    // 模拟创建交叉验证折叠
    return Array.from({ length: folds }, (_, i) => ({
      train: data,
      validation: data
    }))
  }

  /**
   * 设置折叠数据
   */
  private async setFoldData(jobId: string, foldData: any): Promise<void> {
    this.trainingData.set(`${jobId}_train`, foldData.train)
    this.trainingData.set(`${jobId}_validation`, foldData.validation)
  }

  // ID生成方法

  private generateJobId(): string {
    return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateVersionId(): string {
    return `version_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateExperimentId(): string {
    return `exp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateArtifactId(): string {
    return `artifact_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
} 