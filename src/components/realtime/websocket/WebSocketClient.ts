import { 
  WebSocketConfig, 
  WebSocketState, 
  WebSocketMessage, 
  MessageType,
  ConnectionOptions,
  ConnectionStats,
  ConnectionEvent,
  SubscriptionConfig,
  QueuedMessage,
  HeartbeatConfig,
  IWebSocketClient
} from '@/types/realtime'

/**
 * WebSocket客户端类
 * 提供连接管理、心跳检测、断线重连、消息队列等功能
 */
export class WebSocketClient implements IWebSocketClient {
  private ws: WebSocket | null = null
  private config: WebSocketConfig
  private state: WebSocketState = WebSocketState.DISCONNECTED
  private subscriptions: Map<string, SubscriptionConfig> = new Map()
  private messageQueue: QueuedMessage[] = []
  private eventListeners: Map<string, Function[]> = new Map()
  private stats: ConnectionStats = {
    messagesReceived: 0,
    messagesSent: 0,
    reconnectCount: 0,
    errorCount: 0,
    averageLatency: 0,
    dataTransferred: 0
  }
  
  // 重连相关
  private reconnectTimer: number | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts: number
  private reconnectInterval: number
  private backoffFactor = 1.5
  
  // 心跳相关
  private heartbeatTimer: number | null = null
  private heartbeatTimeoutTimer: number | null = null
  private heartbeatConfig: HeartbeatConfig
  private lastPongTime = 0
  private missedHeartbeats = 0
  
  // 消息相关
  private messageId = 0
  private pendingMessages: Map<string, { resolve: Function, reject: Function, timestamp: number }> = new Map()

  constructor(config: WebSocketConfig) {
    this.config = {
      reconnect: true,
      reconnectInterval: 5000,
      reconnectAttempts: 10,
      heartbeatInterval: 30000,
      heartbeatTimeout: 10000,
      messageQueueSize: 1000,
      autoConnect: false,
      ...config
    }
    
    this.maxReconnectAttempts = this.config.reconnectAttempts!
    this.reconnectInterval = this.config.reconnectInterval!
    
    this.heartbeatConfig = {
      interval: this.config.heartbeatInterval!,
      timeout: this.config.heartbeatTimeout!,
      maxMissed: 3,
      message: { type: MessageType.PING, timestamp: Date.now(), data: {} }
    }
    
    if (this.config.autoConnect) {
      this.connect()
    }
  }

  /**
   * 连接WebSocket
   */
  async connect(options?: ConnectionOptions): Promise<void> {
    if (this.state === WebSocketState.CONNECTED || this.state === WebSocketState.CONNECTING) {
      return Promise.resolve()
    }

    return new Promise((resolve, reject) => {
      try {
        this.setState(WebSocketState.CONNECTING)
        this.emit('connecting')
        
        // 创建WebSocket连接
        this.ws = new WebSocket(this.config.url, this.config.protocols)
        
        // 连接超时处理
        const timeout = options?.timeout || 10000
        const timeoutTimer = setTimeout(() => {
          if (this.state === WebSocketState.CONNECTING) {
            this.ws?.close()
            reject(new Error('连接超时'))
          }
        }, timeout)

        // 连接成功
        this.ws.onopen = (event) => {
          clearTimeout(timeoutTimer)
          this.handleOpen(event)
          resolve()
        }

        // 接收消息
        this.ws.onmessage = (event) => {
          this.handleMessage(event)
        }

        // 连接关闭
        this.ws.onclose = (event) => {
          clearTimeout(timeoutTimer)
          this.handleClose(event)
          if (this.state === WebSocketState.CONNECTING) {
            reject(new Error(`连接失败: ${event.code} ${event.reason}`))
          }
        }

        // 连接错误
        this.ws.onerror = (event) => {
          clearTimeout(timeoutTimer)
          this.handleError(event)
          if (this.state === WebSocketState.CONNECTING) {
            reject(new Error('连接错误'))
          }
        }
      } catch (error) {
        this.setState(WebSocketState.ERROR)
        reject(error)
      }
    })
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    if (this.state === WebSocketState.DISCONNECTED) {
      return Promise.resolve()
    }

    return new Promise((resolve) => {
      this.setState(WebSocketState.DISCONNECTING)
      this.stopHeartbeat()
      this.stopReconnect()
      
      if (this.ws) {
        const closeHandler = () => {
          resolve()
        }
        
        this.ws.addEventListener('close', closeHandler, { once: true })
        this.ws.close(1000, '正常关闭')
        
        // 强制关闭超时
        setTimeout(() => {
          if (this.state !== WebSocketState.DISCONNECTED) {
            this.ws = null
            this.setState(WebSocketState.DISCONNECTED)
            resolve()
          }
        }, 5000)
      } else {
        this.setState(WebSocketState.DISCONNECTED)
        resolve()
      }
    })
  }

  /**
   * 发送消息
   */
  async send(message: WebSocketMessage): Promise<void> {
    if (this.state !== WebSocketState.CONNECTED) {
      // 如果未连接，加入消息队列
      if (this.messageQueue.length < this.config.messageQueueSize!) {
        const queuedMessage: QueuedMessage = {
          id: this.generateMessageId(),
          message: { ...message, id: message.id || this.generateMessageId() },
          timestamp: Date.now(),
          retries: 0,
          priority: 0
        }
        this.messageQueue.push(queuedMessage)
      }
      throw new Error('WebSocket未连接')
    }

    return new Promise((resolve, reject) => {
      try {
        const messageWithId = {
          ...message,
          id: message.id || this.generateMessageId(),
          timestamp: message.timestamp || Date.now()
        }

        const messageStr = JSON.stringify(messageWithId)
        this.ws!.send(messageStr)
        
        this.stats.messagesSent++
        this.stats.dataTransferred += messageStr.length
        
        // 如果需要响应，添加到待处理消息
        if (messageWithId.id && message.type !== MessageType.PING) {
          this.pendingMessages.set(messageWithId.id, {
            resolve,
            reject,
            timestamp: Date.now()
          })
          
          // 消息超时处理
          setTimeout(() => {
            if (this.pendingMessages.has(messageWithId.id!)) {
              this.pendingMessages.delete(messageWithId.id!)
              reject(new Error('消息发送超时'))
            }
          }, 30000)
        } else {
          resolve()
        }
      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * 订阅消息
   */
  subscribe(config: SubscriptionConfig): string {
    const subscriptionId = this.generateSubscriptionId()
    this.subscriptions.set(subscriptionId, config)
    
    // 发送订阅消息
    if (this.state === WebSocketState.CONNECTED) {
      this.send({
        type: MessageType.SUBSCRIBE,
        channel: config.channel,
        timestamp: Date.now(),
        data: {
          subscriptionId,
          type: config.type,
          params: config.params
        }
      }).catch(error => {
        config.onError?.(error)
      })
    }
    
    return subscriptionId
  }

  /**
   * 取消订阅
   */
  unsubscribe(subscriptionId: string): void {
    const config = this.subscriptions.get(subscriptionId)
    if (config) {
      this.subscriptions.delete(subscriptionId)
      
      // 发送取消订阅消息
      if (this.state === WebSocketState.CONNECTED) {
        this.send({
          type: MessageType.UNSUBSCRIBE,
          channel: config.channel,
          timestamp: Date.now(),
          data: { subscriptionId }
        }).catch(console.error)
      }
    }
  }

  /**
   * 获取连接状态
   */
  getState(): WebSocketState {
    return this.state
  }

  /**
   * 获取连接统计
   */
  getStats(): ConnectionStats {
    return { ...this.stats }
  }

  /**
   * 添加事件监听器
   */
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event)!.push(callback)
  }

  /**
   * 移除事件监听器
   */
  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  /**
   * 处理连接打开
   */
  private handleOpen(event: Event): void {
    this.setState(WebSocketState.CONNECTED)
    this.stats.connectedAt = Date.now()
    this.reconnectAttempts = 0
    
    // 开始心跳
    this.startHeartbeat()
    
    // 处理消息队列
    this.processMessageQueue()
    
    // 重新订阅
    this.resubscribe()
    
    this.emit('open', { type: 'open', timestamp: Date.now(), data: event })
  }

  /**
   * 处理消息接收
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data)
      this.stats.messagesReceived++
      this.stats.lastMessageAt = Date.now()
      this.stats.dataTransferred += event.data.length
      
      // 处理心跳响应
      if (message.type === MessageType.PONG) {
        this.handlePong(message)
        return
      }
      
      // 处理待处理消息的响应
      if (message.id && this.pendingMessages.has(message.id)) {
        const pending = this.pendingMessages.get(message.id)!
        this.pendingMessages.delete(message.id)
        
        // 计算延迟
        const latency = Date.now() - pending.timestamp
        this.updateAverageLatency(latency)
        
        pending.resolve(message)
        return
      }
      
      // 路由消息到订阅者
      this.routeMessage(message)
      
      this.emit('message', { 
        type: 'message', 
        timestamp: Date.now(), 
        data: message 
      })
    } catch (error) {
      console.error('消息解析错误:', error)
      this.stats.errorCount++
    }
  }

  /**
   * 处理连接关闭
   */
  private handleClose(event: CloseEvent): void {
    this.setState(WebSocketState.DISCONNECTED)
    this.stopHeartbeat()
    this.ws = null
    
    this.emit('close', { 
      type: 'close', 
      timestamp: Date.now(), 
      data: { code: event.code, reason: event.reason } 
    })
    
    // 自动重连
    if (this.config.reconnect && event.code !== 1000) {
      this.scheduleReconnect()
    }
  }

  /**
   * 处理连接错误
   */
  private handleError(event: Event): void {
    this.setState(WebSocketState.ERROR)
    this.stats.errorCount++
    
    this.emit('error', { 
      type: 'error', 
      timestamp: Date.now(), 
      error: new Error('WebSocket连接错误') 
    })
  }

  /**
   * 开始心跳
   */
  private startHeartbeat(): void {
    this.stopHeartbeat()
    
    this.heartbeatTimer = window.setInterval(() => {
      if (this.state === WebSocketState.CONNECTED) {
        this.sendHeartbeat()
      }
    }, this.heartbeatConfig.interval)
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
    
    if (this.heartbeatTimeoutTimer) {
      clearTimeout(this.heartbeatTimeoutTimer)
      this.heartbeatTimeoutTimer = null
    }
  }

  /**
   * 发送心跳
   */
  private sendHeartbeat(): void {
    const heartbeatMessage = {
      ...this.heartbeatConfig.message,
      timestamp: Date.now()
    }
    
    this.send(heartbeatMessage).catch(() => {
      this.missedHeartbeats++
      if (this.missedHeartbeats >= this.heartbeatConfig.maxMissed) {
        this.ws?.close(1000, '心跳超时')
      }
    })
    
    // 设置心跳超时
    this.heartbeatTimeoutTimer = window.setTimeout(() => {
      this.missedHeartbeats++
      if (this.missedHeartbeats >= this.heartbeatConfig.maxMissed) {
        this.ws?.close(1000, '心跳超时')
      }
    }, this.heartbeatConfig.timeout)
  }

  /**
   * 处理心跳响应
   */
  private handlePong(message: WebSocketMessage): void {
    this.lastPongTime = Date.now()
    this.missedHeartbeats = 0
    
    if (this.heartbeatTimeoutTimer) {
      clearTimeout(this.heartbeatTimeoutTimer)
      this.heartbeatTimeoutTimer = null
    }
    
    // 计算延迟
    if (message.timestamp) {
      const latency = this.lastPongTime - message.timestamp
      this.updateAverageLatency(latency)
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('重连次数已达上限')
      return
    }
    
    this.stopReconnect()
    
    // 指数退避算法
    const delay = this.reconnectInterval * Math.pow(this.backoffFactor, this.reconnectAttempts)
    const jitter = Math.random() * 0.1 * delay // 添加抖动
    const actualDelay = delay + jitter
    
    this.reconnectTimer = window.setTimeout(() => {
      this.reconnectAttempts++
      this.stats.reconnectCount++
      console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      this.connect().catch(error => {
        console.error('重连失败:', error)
        this.scheduleReconnect()
      })
    }, actualDelay)
  }

  /**
   * 停止重连
   */
  private stopReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }

  /**
   * 处理消息队列
   */
  private processMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.state === WebSocketState.CONNECTED) {
      const queuedMessage = this.messageQueue.shift()!
      this.send(queuedMessage.message).catch(console.error)
    }
  }

  /**
   * 重新订阅
   */
  private resubscribe(): void {
    for (const [subscriptionId, config] of this.subscriptions) {
      this.send({
        type: MessageType.SUBSCRIBE,
        channel: config.channel,
        timestamp: Date.now(),
        data: {
          subscriptionId,
          type: config.type,
          params: config.params
        }
      }).catch(error => {
        config.onError?.(error)
      })
    }
  }

  /**
   * 路由消息到订阅者
   */
  private routeMessage(message: WebSocketMessage): void {
    for (const [subscriptionId, config] of this.subscriptions) {
      if (config.channel === message.channel && config.type === message.type) {
        try {
          config.callback?.(message)
        } catch (error) {
          console.error('订阅回调错误:', error)
          config.onError?.(error as Error)
        }
      }
    }
  }

  /**
   * 设置连接状态
   */
  private setState(state: WebSocketState): void {
    if (this.state !== state) {
      const oldState = this.state
      this.state = state
      this.emit('stateChange', { oldState, newState: state, timestamp: Date.now() })
    }
  }

  /**
   * 触发事件
   */
  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('事件回调错误:', error)
        }
      })
    }
  }

  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `msg_${++this.messageId}_${Date.now()}`
  }

  /**
   * 生成订阅ID
   */
  private generateSubscriptionId(): string {
    return `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 更新平均延迟
   */
  private updateAverageLatency(latency: number): void {
    if (this.stats.averageLatency === 0) {
      this.stats.averageLatency = latency
    } else {
      // 使用指数移动平均
      this.stats.averageLatency = this.stats.averageLatency * 0.9 + latency * 0.1
    }
  }

  /**
   * 销毁客户端
   */
  destroy(): void {
    this.disconnect()
    this.subscriptions.clear()
    this.messageQueue.length = 0
    this.eventListeners.clear()
    this.pendingMessages.clear()
  }
} 