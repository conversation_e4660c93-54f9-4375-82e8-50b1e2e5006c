import { 
  WebSocketConfig, 
  ConnectionStats,
  IConnectionManager,
  IWebSocketClient
} from '@/types/realtime'
import { WebSocketClient } from './WebSocketClient'

/**
 * WebSocket连接管理器
 * 管理多个WebSocket连接，提供连接池、负载均衡等功能
 */
export class ConnectionManager implements IConnectionManager {
  private connections: Map<string, IWebSocketClient> = new Map()
  private connectionConfigs: Map<string, WebSocketConfig> = new Map()
  private defaultConfig: Partial<WebSocketConfig> = {}
  private maxConnections = 10
  private connectionCounter = 0

  constructor(defaultConfig?: Partial<WebSocketConfig>) {
    this.defaultConfig = {
      reconnect: true,
      reconnectInterval: 5000,
      reconnectAttempts: 10,
      heartbeatInterval: 30000,
      heartbeatTimeout: 10000,
      messageQueueSize: 1000,
      autoConnect: false,
      ...defaultConfig
    }
  }

  /**
   * 创建新的WebSocket连接
   */
  createConnection(config: WebSocketConfig): IWebSocketClient {
    const connectionId = this.generateConnectionId()
    
    // 合并配置
    const finalConfig: WebSocketConfig = {
      ...this.defaultConfig,
      ...config
    }
    
    // 检查连接数限制
    if (this.connections.size >= this.maxConnections) {
      throw new Error(`连接数已达上限: ${this.maxConnections}`)
    }
    
    // 创建WebSocket客户端
    const client = new WebSocketClient(finalConfig)
    
    // 添加连接事件监听
    this.setupConnectionEvents(client, connectionId)
    
    // 存储连接
    this.connections.set(connectionId, client)
    this.connectionConfigs.set(connectionId, finalConfig)
    
    console.log(`创建WebSocket连接: ${connectionId}`)
    return client
  }

  /**
   * 获取指定连接
   */
  getConnection(id: string): IWebSocketClient | undefined {
    return this.connections.get(id)
  }

  /**
   * 获取所有连接
   */
  getAllConnections(): Map<string, IWebSocketClient> {
    return new Map(this.connections)
  }

  /**
   * 获取连接数量
   */
  getConnectionCount(): number {
    return this.connections.size
  }

  /**
   * 关闭指定连接
   */
  async closeConnection(id: string): Promise<void> {
    const client = this.connections.get(id)
    if (client) {
      await client.disconnect()
      this.connections.delete(id)
      this.connectionConfigs.delete(id)
      console.log(`关闭WebSocket连接: ${id}`)
    }
  }

  /**
   * 关闭所有连接
   */
  async closeAllConnections(): Promise<void> {
    const closePromises = Array.from(this.connections.entries()).map(
      async ([id, client]) => {
        try {
          await client.disconnect()
          console.log(`关闭WebSocket连接: ${id}`)
        } catch (error) {
          console.error(`关闭连接失败 ${id}:`, error)
        }
      }
    )
    
    await Promise.allSettled(closePromises)
    this.connections.clear()
    this.connectionConfigs.clear()
  }

  /**
   * 获取所有连接的统计信息
   */
  getConnectionStats(): Record<string, ConnectionStats> {
    const stats: Record<string, ConnectionStats> = {}
    
    for (const [id, client] of this.connections) {
      stats[id] = client.getStats()
    }
    
    return stats
  }

  /**
   * 获取连接总体统计
   */
  getOverallStats(): {
    totalConnections: number
    connectedConnections: number
    totalMessagesReceived: number
    totalMessagesSent: number
    totalDataTransferred: number
    averageLatency: number
  } {
    let connectedCount = 0
    let totalMessagesReceived = 0
    let totalMessagesSent = 0
    let totalDataTransferred = 0
    let totalLatency = 0
    let latencyCount = 0

    for (const client of this.connections.values()) {
      const stats = client.getStats()
      
      if (client.getState() === 'connected') {
        connectedCount++
      }
      
      totalMessagesReceived += stats.messagesReceived
      totalMessagesSent += stats.messagesSent
      totalDataTransferred += stats.dataTransferred
      
      if (stats.averageLatency > 0) {
        totalLatency += stats.averageLatency
        latencyCount++
      }
    }

    return {
      totalConnections: this.connections.size,
      connectedConnections: connectedCount,
      totalMessagesReceived,
      totalMessagesSent,
      totalDataTransferred,
      averageLatency: latencyCount > 0 ? totalLatency / latencyCount : 0
    }
  }

  /**
   * 查找最佳连接（负载最轻）
   */
  findBestConnection(): IWebSocketClient | undefined {
    let bestClient: IWebSocketClient | undefined
    let lowestLoad = Infinity

    for (const client of this.connections.values()) {
      if (client.getState() === 'connected') {
        const stats = client.getStats()
        const load = stats.messagesReceived + stats.messagesSent
        
        if (load < lowestLoad) {
          lowestLoad = load
          bestClient = client
        }
      }
    }

    return bestClient
  }

  /**
   * 按URL查找连接
   */
  findConnectionByUrl(url: string): IWebSocketClient | undefined {
    for (const [id, client] of this.connections) {
      const config = this.connectionConfigs.get(id)
      if (config && config.url === url) {
        return client
      }
    }
    return undefined
  }

  /**
   * 重连所有失败的连接
   */
  async reconnectFailedConnections(): Promise<void> {
    const reconnectPromises: Promise<void>[] = []

    for (const client of this.connections.values()) {
      const state = client.getState()
      if (state === 'disconnected' || state === 'error') {
        reconnectPromises.push(
          client.connect().catch(error => {
            console.error('重连失败:', error)
          })
        )
      }
    }

    await Promise.allSettled(reconnectPromises)
  }

  /**
   * 设置最大连接数
   */
  setMaxConnections(max: number): void {
    this.maxConnections = max
  }

  /**
   * 获取最大连接数
   */
  getMaxConnections(): number {
    return this.maxConnections
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{
    healthy: boolean
    totalConnections: number
    healthyConnections: number
    unhealthyConnections: string[]
  }> {
    const unhealthyConnections: string[] = []
    let healthyCount = 0

    for (const [id, client] of this.connections) {
      const state = client.getState()
      if (state === 'connected') {
        healthyCount++
      } else {
        unhealthyConnections.push(id)
      }
    }

    const totalConnections = this.connections.size
    const healthy = totalConnections > 0 && unhealthyConnections.length === 0

    return {
      healthy,
      totalConnections,
      healthyConnections: healthyCount,
      unhealthyConnections
    }
  }

  /**
   * 清理无效连接
   */
  cleanupConnections(): void {
    const toRemove: string[] = []

    for (const [id, client] of this.connections) {
      const state = client.getState()
      if (state === 'error') {
        const stats = client.getStats()
        // 如果连接错误且长时间无活动，则清理
        if (stats.lastMessageAt && Date.now() - stats.lastMessageAt > 300000) { // 5分钟
          toRemove.push(id)
        }
      }
    }

    for (const id of toRemove) {
      this.closeConnection(id).catch(console.error)
    }
  }

  /**
   * 设置连接事件监听
   */
  private setupConnectionEvents(client: IWebSocketClient, connectionId: string): void {
    client.on('open', () => {
      console.log(`连接已打开: ${connectionId}`)
    })

    client.on('close', (event: any) => {
      console.log(`连接已关闭: ${connectionId}`, event)
    })

    client.on('error', (event: any) => {
      console.error(`连接错误: ${connectionId}`, event)
    })

    client.on('stateChange', (event: any) => {
      console.log(`连接状态变化: ${connectionId}`, event)
    })
  }

  /**
   * 生成连接ID
   */
  private generateConnectionId(): string {
    return `conn_${++this.connectionCounter}_${Date.now()}`
  }

  /**
   * 销毁管理器
   */
  async destroy(): Promise<void> {
    await this.closeAllConnections()
    console.log('ConnectionManager已销毁')
  }
}

// 创建全局连接管理器实例
export const globalConnectionManager = new ConnectionManager() 