// 表单组件类型定义

// 基础字段类型
export type FieldType = 
  | 'input' | 'textarea' | 'password' | 'number' 
  | 'select' | 'radio' | 'checkbox' | 'switch'
  | 'date' | 'datetime' | 'daterange' | 'time'
  | 'slider' | 'rate' | 'color'
  | 'upload' | 'image' | 'file'
  | 'cascader' | 'tree-select' | 'transfer'
  | 'custom' | 'slot'

// 字段配置接口
export interface FormField {
  key: string
  type: FieldType
  label: string
  placeholder?: string
  defaultValue?: any
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  hidden?: boolean
  span?: number
  offset?: number
  order?: number
  
  // 验证规则
  rules?: FormRule[]
  validator?: (value: any, formData: any) => Promise<string | undefined> | string | undefined
  
  // 字段选项（select、radio、checkbox等）
  options?: FieldOption[]
  multiple?: boolean
  
  // 条件渲染
  visible?: boolean | ((formData: any) => boolean)
  dependencies?: string[]
  
  // 字段特定配置
  config?: FieldConfig
  
  // 样式和布局
  className?: string
  style?: Record<string, any>
  labelWidth?: string | number
  labelPosition?: 'left' | 'right' | 'top'
  
  // 事件处理
  onChange?: (value: any, formData: any) => void
  onFocus?: (event: Event) => void
  onBlur?: (event: Event) => void
  
  // 帮助信息
  help?: string
  tooltip?: string
  
  // 分组信息
  group?: string
  step?: number
  tab?: string
}

// 字段选项
export interface FieldOption {
  label: string
  value: any
  disabled?: boolean
  children?: FieldOption[]
  icon?: string
  color?: string
}

// 验证规则
export interface FormRule {
  type?: 'required' | 'pattern' | 'min' | 'max' | 'minLength' | 'maxLength' | 'email' | 'url' | 'custom'
  message: string
  trigger?: 'change' | 'blur' | 'submit'
  pattern?: RegExp
  min?: number
  max?: number
  minLength?: number
  maxLength?: number
  validator?: (value: any) => boolean | Promise<boolean>
}

// 字段特定配置
export interface FieldConfig {
  // Input配置
  maxlength?: number
  minlength?: number
  showWordLimit?: boolean
  clearable?: boolean
  showPassword?: boolean
  prefixIcon?: string
  suffixIcon?: string
  
  // Number配置
  min?: number
  max?: number
  step?: number
  precision?: number
  controls?: boolean
  controlsPosition?: 'right' | ''
  
  // Select配置
  filterable?: boolean
  remote?: boolean
  remoteMethod?: (query: string) => Promise<FieldOption[]>
  loading?: boolean
  noDataText?: string
  
  // Date配置
  format?: string
  valueFormat?: string
  disabledDate?: (date: Date) => boolean
  shortcuts?: any[]
  
  // Upload配置
  action?: string
  headers?: Record<string, string>
  accept?: string
  maxSize?: number
  maxCount?: number
  listType?: 'text' | 'picture' | 'picture-card'
  
  // Slider配置
  range?: boolean
  marks?: Record<number, string>
  
  // 其他配置
  [key: string]: any
}

// 表单布局类型
export type FormLayout = 'horizontal' | 'vertical' | 'inline'

// 表单布局配置
export interface FormLayoutConfig {
  type: FormLayout
  labelWidth?: string | number
  labelPosition?: 'left' | 'right' | 'top'
  size?: 'large' | 'default' | 'small'
  disabled?: boolean
  
  // 网格布局
  gutter?: number
  columns?: number
  responsive?: {
    xs?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
    xxl?: number
  }
  
  // 分组布局
  groups?: FormGroup[]
  
  // 分步布局
  steps?: FormStep[]
  
  // 标签页布局
  tabs?: FormTab[]
}

// 表单分组
export interface FormGroup {
  key: string
  title: string
  description?: string
  collapsible?: boolean
  collapsed?: boolean
  fields: string[]
  span?: number
}

// 表单步骤
export interface FormStep {
  key: string
  title: string
  description?: string
  icon?: string
  status?: 'wait' | 'process' | 'finish' | 'error'
  fields: string[]
  validation?: boolean
}

// 表单标签页
export interface FormTab {
  key: string
  title: string
  icon?: string
  closable?: boolean
  disabled?: boolean
  fields: string[]
}

// 表单配置
export interface FormConfig {
  fields: FormField[]
  layout: FormLayoutConfig
  initialValues?: Record<string, any>
  validation?: FormValidationConfig
  submission?: FormSubmissionConfig
  
  // 表单行为
  resetOnSubmit?: boolean
  validateOnChange?: boolean
  validateOnBlur?: boolean
  showValidationSummary?: boolean
  
  // 表单样式
  className?: string
  style?: Record<string, any>
  
  // 表单模式
  mode?: 'create' | 'edit' | 'view' | 'search'
  readonly?: boolean
  disabled?: boolean
}

// 表单验证配置
export interface FormValidationConfig {
  mode?: 'onChange' | 'onBlur' | 'onSubmit' | 'manual'
  stopOnFirstError?: boolean
  scrollToError?: boolean
  showErrorSummary?: boolean
  errorSummaryTitle?: string
}

// 表单提交配置
export interface FormSubmissionConfig {
  url?: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  headers?: Record<string, string>
  transformData?: (data: any) => any
  onSuccess?: (response: any) => void
  onError?: (error: any) => void
  loading?: boolean
}

// 表单状态
export interface FormState {
  values: Record<string, any>
  errors: Record<string, string[]>
  touched: Record<string, boolean>
  dirty: Record<string, boolean>
  submitting: boolean
  validating: boolean
  valid: boolean
}

// 表单上下文
export interface FormContext {
  formData: Record<string, any>
  formState: FormState
  formConfig: FormConfig
  
  // 方法
  setValue: (field: string, value: any) => void
  setValues: (values: Record<string, any>) => void
  setError: (field: string, error: string | string[]) => void
  clearError: (field: string) => void
  clearErrors: () => void
  validate: (field?: string) => Promise<boolean>
  validateAll: () => Promise<boolean>
  reset: () => void
  submit: () => Promise<any>
  
  // 字段方法
  getFieldValue: (field: string) => any
  getFieldError: (field: string) => string[]
  isFieldTouched: (field: string) => boolean
  isFieldDirty: (field: string) => boolean
  isFieldVisible: (field: string) => boolean
  isFieldDisabled: (field: string) => boolean
}

// 表单构建器属性
export interface FormBuilderProps {
  config: FormConfig
  modelValue?: Record<string, any>
  loading?: boolean
  readonly?: boolean
  disabled?: boolean
}

// 表单构建器事件
export interface FormBuilderEmits {
  (e: 'update:modelValue', value: Record<string, any>): void
  (e: 'change', field: string, value: any, formData: Record<string, any>): void
  (e: 'validate', field: string, valid: boolean, errors: string[]): void
  (e: 'submit', formData: Record<string, any>): void
  (e: 'reset'): void
  (e: 'error', field: string, errors: string[]): void
}

// 字段渲染器属性
export interface FieldRendererProps {
  field: FormField
  value: any
  error?: string[]
  disabled?: boolean
  readonly?: boolean
  context: FormContext
}

// 字段渲染器事件
export interface FieldRendererEmits {
  (e: 'change', value: any): void
  (e: 'blur', event: Event): void
  (e: 'focus', event: Event): void
}

// 表单预设
export interface FormPreset {
  id: string
  name: string
  description?: string
  config: FormConfig
  category?: string
  tags?: string[]
  version?: string
  author?: string
  createdAt?: Date
  updatedAt?: Date
}

// 表单模板
export interface FormTemplate {
  id: string
  name: string
  description?: string
  fields: FormField[]
  layout?: Partial<FormLayoutConfig>
  category?: string
  tags?: string[]
  preview?: string
  usage?: number
}

// 常量定义
export const FIELD_TYPES: Record<FieldType, { label: string; icon: string; category: string }> = {
  // 基础字段
  input: { label: '单行文本', icon: 'edit', category: 'basic' },
  textarea: { label: '多行文本', icon: 'document', category: 'basic' },
  password: { label: '密码', icon: 'lock', category: 'basic' },
  number: { label: '数字', icon: 'calculator', category: 'basic' },
  
  // 选择字段
  select: { label: '下拉选择', icon: 'arrow-down', category: 'select' },
  radio: { label: '单选', icon: 'radio', category: 'select' },
  checkbox: { label: '多选', icon: 'checkbox', category: 'select' },
  switch: { label: '开关', icon: 'switch', category: 'select' },
  
  // 日期字段
  date: { label: '日期', icon: 'calendar', category: 'date' },
  datetime: { label: '日期时间', icon: 'time', category: 'date' },
  daterange: { label: '日期范围', icon: 'date-range', category: 'date' },
  time: { label: '时间', icon: 'clock', category: 'date' },
  
  // 数值字段
  slider: { label: '滑块', icon: 'slider', category: 'number' },
  rate: { label: '评分', icon: 'star', category: 'number' },
  color: { label: '颜色', icon: 'color', category: 'number' },
  
  // 文件字段
  upload: { label: '文件上传', icon: 'upload', category: 'file' },
  image: { label: '图片上传', icon: 'picture', category: 'file' },
  file: { label: '附件', icon: 'paperclip', category: 'file' },
  
  // 复合字段
  cascader: { label: '级联选择', icon: 'menu', category: 'complex' },
  'tree-select': { label: '树选择', icon: 'tree', category: 'complex' },
  transfer: { label: '穿梭框', icon: 'sort', category: 'complex' },
  
  // 自定义字段
  custom: { label: '自定义', icon: 'component', category: 'custom' },
  slot: { label: '插槽', icon: 'slot', category: 'custom' }
}

export const VALIDATION_RULES = {
  required: { message: '此字段为必填项' },
  email: { message: '请输入有效的邮箱地址', pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ },
  url: { message: '请输入有效的URL', pattern: /^https?:\/\/.+/ },
  phone: { message: '请输入有效的手机号', pattern: /^1[3-9]\d{9}$/ },
  number: { message: '请输入有效的数字', pattern: /^\d+(\.\d+)?$/ },
  integer: { message: '请输入整数', pattern: /^\d+$/ },
  chinese: { message: '请输入中文', pattern: /^[\u4e00-\u9fa5]+$/ },
  english: { message: '请输入英文', pattern: /^[a-zA-Z]+$/ },
  alphanumeric: { message: '请输入字母或数字', pattern: /^[a-zA-Z0-9]+$/ }
} as const

export const DEFAULT_LAYOUT_CONFIG: FormLayoutConfig = {
  type: 'horizontal',
  labelWidth: 120,
  labelPosition: 'right',
  size: 'default',
  gutter: 16,
  columns: 2
}

export const DEFAULT_VALIDATION_CONFIG: FormValidationConfig = {
  mode: 'onChange',
  stopOnFirstError: false,
  scrollToError: true,
  showErrorSummary: false
} 