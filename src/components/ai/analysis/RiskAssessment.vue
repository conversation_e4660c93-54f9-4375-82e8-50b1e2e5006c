<template>
  <div class="risk-assessment">
    <div class="assessment-header">
      <h3>AI风险评估</h3>
      <div class="controls">
        <el-select
          v-model="selectedSymbol"
          placeholder="选择股票"
          @change="onSymbolChange"
          :loading="loading"
        >
          <el-option
            v-for="symbol in popularSymbols"
            :key="symbol"
            :label="symbol"
            :value="symbol"
          />
        </el-select>
        
        <el-select
          v-model="assessmentType"
          placeholder="评估类型"
          @change="onTypeChange"
        >
          <el-option label="综合评估" value="comprehensive" />
          <el-option label="市场风险" value="market" />
          <el-option label="信用风险" value="credit" />
          <el-option label="流动性风险" value="liquidity" />
          <el-option label="操作风险" value="operational" />
        </el-select>
        
        <el-button
          type="primary"
          @click="performAssessment"
          :loading="loading"
          :disabled="!selectedSymbol"
        >
          开始评估
        </el-button>
      </div>
    </div>

    <!-- 风险概览卡片 -->
    <div v-if="assessment" class="risk-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="risk-card overall-risk">
            <div class="card-header">
              <i class="el-icon-warning-outline"></i>
              <span>整体风险</span>
            </div>
            <div class="card-content">
              <div :class="getRiskScoreClass(assessment.overallRisk.score)">
                {{ assessment.overallRisk.score.toFixed(1) }}
              </div>
              <div class="risk-level">
                {{ getRiskLevelLabel(assessment.overallRisk.level) }}
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="risk-card market-risk">
            <div class="card-header">
              <i class="el-icon-data-line"></i>
              <span>市场风险</span>
            </div>
            <div class="card-content">
              <div :class="getRiskScoreClass(assessment.marketRisk.score)">
                {{ assessment.marketRisk.score.toFixed(1) }}
              </div>
              <div class="risk-indicator">
                Beta: {{ assessment.marketRisk.beta.toFixed(2) }}
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="risk-card credit-risk">
            <div class="card-header">
              <i class="el-icon-money"></i>
              <span>信用风险</span>
            </div>
            <div class="card-content">
              <div :class="getRiskScoreClass(assessment.creditRisk.score)">
                {{ assessment.creditRisk.score.toFixed(1) }}
              </div>
              <div class="risk-indicator">
                评级: {{ assessment.creditRisk.rating }}
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="risk-card liquidity-risk">
            <div class="card-header">
              <i class="el-icon-coin"></i>
              <span>流动性风险</span>
            </div>
            <div class="card-content">
              <div :class="getRiskScoreClass(assessment.liquidityRisk.score)">
                {{ assessment.liquidityRisk.score.toFixed(1) }}
              </div>
              <div class="risk-indicator">
                换手率: {{ (assessment.liquidityRisk.turnoverRate * 100).toFixed(1) }}%
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细风险分析 -->
    <div v-if="assessment" class="risk-details">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 风险矩阵 -->
        <el-tab-pane label="风险矩阵" name="matrix">
          <div class="risk-matrix">
            <div class="matrix-chart">
              <h4>风险-收益矩阵</h4>
              <div ref="matrixChartRef" class="chart-container"></div>
            </div>
            
            <div class="matrix-analysis">
              <h4>风险分布分析</h4>
              <div class="risk-distribution">
                <div
                  v-for="risk in assessment.riskBreakdown"
                  :key="risk.category"
                  class="risk-item"
                >
                  <div class="risk-category">{{ risk.category }}</div>
                  <div class="risk-bar">
                    <div 
                      class="risk-fill"
                      :class="getRiskBarClass(risk.score)"
                      :style="{ width: (risk.score * 10) + '%' }"
                    ></div>
                  </div>
                  <div class="risk-score">{{ risk.score.toFixed(1) }}</div>
                  <div class="risk-description">{{ risk.description }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- VaR分析 -->
        <el-tab-pane label="VaR分析" name="var">
          <div class="var-analysis">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-card>
                  <div slot="header">风险价值 (VaR)</div>
                  <div class="var-metrics">
                    <div class="var-item">
                      <span class="var-label">1日VaR (95%)</span>
                      <span class="var-value negative">
                        -¥{{ assessment.varAnalysis.oneDay.toFixed(2) }}
                      </span>
                    </div>
                    <div class="var-item">
                      <span class="var-label">1周VaR (95%)</span>
                      <span class="var-value negative">
                        -¥{{ assessment.varAnalysis.oneWeek.toFixed(2) }}
                      </span>
                    </div>
                    <div class="var-item">
                      <span class="var-label">1月VaR (95%)</span>
                      <span class="var-value negative">
                        -¥{{ assessment.varAnalysis.oneMonth.toFixed(2) }}
                      </span>
                    </div>
                    <div class="var-item">
                      <span class="var-label">条件VaR (CVaR)</span>
                      <span class="var-value negative">
                        -¥{{ assessment.varAnalysis.conditionalVar.toFixed(2) }}
                      </span>
                    </div>
                  </div>
                </el-card>
              </el-col>
              
              <el-col :span="12">
                <el-card>
                  <div slot="header">压力测试</div>
                  <div class="stress-test">
                    <div
                      v-for="scenario in assessment.stressTest"
                      :key="scenario.name"
                      class="stress-scenario"
                    >
                      <div class="scenario-name">{{ scenario.name }}</div>
                      <div class="scenario-impact">
                        <span :class="getImpactClass(scenario.impact)">
                          {{ (scenario.impact * 100).toFixed(1) }}%
                        </span>
                      </div>
                      <div class="scenario-probability">
                        概率: {{ (scenario.probability * 100).toFixed(1) }}%
                      </div>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
            
            <div class="var-chart">
              <h4>VaR历史回测</h4>
              <div ref="varChartRef" class="chart-container"></div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 风险因子 -->
        <el-tab-pane label="风险因子" name="factors">
          <div class="risk-factors">
            <div class="factors-analysis">
              <h4>主要风险因子</h4>
              <el-table :data="assessment.riskFactors" stripe>
                <el-table-column prop="name" label="风险因子" width="150" />
                <el-table-column prop="exposure" label="暴露度" width="100">
                  <template #default="{ row }">
                    <span :class="getExposureClass(row.exposure)">
                      {{ (row.exposure * 100).toFixed(1) }}%
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="contribution" label="贡献度" width="100">
                  <template #default="{ row }">
                    {{ (row.contribution * 100).toFixed(1) }}%
                  </template>
                </el-table-column>
                <el-table-column prop="volatility" label="波动率" width="100">
                  <template #default="{ row }">
                    {{ (row.volatility * 100).toFixed(1) }}%
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述" show-overflow-tooltip />
              </el-table>
            </div>
            
            <div class="correlation-matrix">
              <h4>风险因子相关性矩阵</h4>
              <div ref="correlationChartRef" class="chart-container"></div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 监控预警 -->
        <el-tab-pane label="监控预警" name="monitoring">
          <div class="risk-monitoring">
            <div class="alert-settings">
              <h4>风险预警设置</h4>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-card>
                    <div slot="header">阈值设置</div>
                    <div class="threshold-settings">
                      <div class="threshold-item">
                        <label>整体风险阈值</label>
                        <el-slider
                          v-model="riskThresholds.overall"
                          :min="0"
                          :max="10"
                          :step="0.1"
                          show-input
                        />
                      </div>
                      <div class="threshold-item">
                        <label>VaR阈值</label>
                        <el-input-number
                          v-model="riskThresholds.var"
                          :precision="2"
                          :step="100"
                          controls-position="right"
                        />
                      </div>
                      <div class="threshold-item">
                        <label>波动率阈值</label>
                        <el-slider
                          v-model="riskThresholds.volatility"
                          :min="0"
                          :max="1"
                          :step="0.01"
                          show-input
                        />
                      </div>
                    </div>
                  </el-card>
                </el-col>
                
                <el-col :span="16">
                  <el-card>
                    <div slot="header">当前预警</div>
                    <div class="current-alerts">
                      <div
                        v-for="alert in assessment.currentAlerts"
                        :key="alert.id"
                        class="alert-item"
                        :class="getAlertClass(alert.severity)"
                      >
                        <div class="alert-icon">
                          <i :class="getAlertIcon(alert.severity)"></i>
                        </div>
                        <div class="alert-content">
                          <div class="alert-title">{{ alert.title }}</div>
                          <div class="alert-message">{{ alert.message }}</div>
                          <div class="alert-time">{{ formatTime(alert.timestamp) }}</div>
                        </div>
                        <div class="alert-actions">
                          <el-button size="small" @click="acknowledgeAlert(alert.id)">
                            确认
                          </el-button>
                        </div>
                      </div>
                      
                      <div v-if="assessment.currentAlerts.length === 0" class="no-alerts">
                        <i class="el-icon-success"></i>
                        <span>当前无风险预警</span>
                      </div>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </div>
            
            <div class="monitoring-charts">
              <h4>风险监控图表</h4>
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="monitoring-chart">
                    <h5>风险趋势</h5>
                    <div ref="riskTrendChartRef" class="chart-container"></div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="monitoring-chart">
                    <h5>预警频率</h5>
                    <div ref="alertFrequencyChartRef" class="chart-container"></div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading && !assessment" class="loading-container">
      <el-progress
        :percentage="progress"
        :format="formatProgress"
        status="success"
      />
      <p class="loading-message">{{ loadingMessage }}</p>
    </div>

    <!-- 错误状态 -->
    <el-alert
      v-if="error"
      :title="error"
      type="error"
      :closable="false"
      show-icon
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { useRiskAssessment } from '@/composables/ai/useRiskAssessment'
import type { 
  RiskAssessment, 
  RiskLevel,
  AssessmentType,
  RiskAlert,
  AlertSeverity
} from '@/types/ai'

// 组合式API
const {
  assessment,
  loading,
  error,
  performAssessment: assess,
  clearAssessment,
  acknowledgeAlert
} = useRiskAssessment()

// 响应式数据
const selectedSymbol = ref<string>('')
const assessmentType = ref<AssessmentType>('comprehensive')
const activeTab = ref('matrix')
const progress = ref(0)
const loadingMessage = ref('')

// 图表引用
const matrixChartRef = ref<HTMLElement>()
const varChartRef = ref<HTMLElement>()
const correlationChartRef = ref<HTMLElement>()
const riskTrendChartRef = ref<HTMLElement>()
const alertFrequencyChartRef = ref<HTMLElement>()

// 风险阈值设置
const riskThresholds = reactive({
  overall: 7.0,
  var: 1000,
  volatility: 0.3
})

// 热门股票列表
const popularSymbols = [
  '000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '600519.SH',
  '000858.SZ', '002415.SZ', '300059.SZ', '600276.SH', '000725.SZ'
]

// 方法
const onSymbolChange = () => {
  clearAssessment()
}

const onTypeChange = () => {
  if (selectedSymbol.value) {
    performAssessment()
  }
}

const performAssessment = async () => {
  if (!selectedSymbol.value) {
    ElMessage.warning('请先选择股票')
    return
  }

  progress.value = 0
  loadingMessage.value = '正在收集风险数据...'
  
  const progressInterval = setInterval(() => {
    if (progress.value < 90) {
      progress.value += Math.random() * 8
      if (progress.value > 25 && progress.value < 50) {
        loadingMessage.value = '正在分析市场风险...'
      } else if (progress.value >= 50 && progress.value < 75) {
        loadingMessage.value = '正在评估信用风险...'
      } else if (progress.value >= 75) {
        loadingMessage.value = '正在生成风险报告...'
      }
    }
  }, 200)

  try {
    await assess(selectedSymbol.value, assessmentType.value)
    progress.value = 100
    loadingMessage.value = '评估完成'
    
    // 渲染图表
    await nextTick()
    renderCharts()
    
  } catch (err) {
    ElMessage.error('风险评估失败: ' + (err as Error).message)
  } finally {
    clearInterval(progressInterval)
  }
}

const renderCharts = () => {
  renderMatrixChart()
  renderVarChart()
  renderCorrelationChart()
  renderRiskTrendChart()
  renderAlertFrequencyChart()
}

const renderMatrixChart = () => {
  if (!matrixChartRef.value || !assessment.value) return

  const chart = echarts.init(matrixChartRef.value)
  
  const data = assessment.value.riskBreakdown.map(risk => [
    risk.score,
    risk.expectedReturn || Math.random() * 0.2 - 0.1,
    risk.category
  ])

  const option = {
    title: {
      text: '风险-收益散点图',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.data[2]}<br/>风险: ${params.data[0].toFixed(1)}<br/>收益: ${(params.data[1] * 100).toFixed(1)}%`
      }
    },
    xAxis: {
      type: 'value',
      name: '风险评分',
      min: 0,
      max: 10
    },
    yAxis: {
      type: 'value',
      name: '预期收益率',
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [{
      type: 'scatter',
      data: data,
      symbolSize: 20,
      itemStyle: {
        color: (params: any) => {
          const risk = params.data[0]
          if (risk < 3) return '#67C23A'
          if (risk < 7) return '#E6A23C'
          return '#F56C6C'
        }
      }
    }]
  }

  chart.setOption(option)
}

const renderVarChart = () => {
  if (!varChartRef.value || !assessment.value) return

  const chart = echarts.init(varChartRef.value)
  
  // 模拟VaR历史数据
  const dates = []
  const varValues = []
  const actualLosses = []
  
  for (let i = 30; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    dates.push(date.toISOString().split('T')[0])
    varValues.push(-assessment.value.varAnalysis.oneDay * (0.8 + Math.random() * 0.4))
    actualLosses.push(Math.random() > 0.95 ? -assessment.value.varAnalysis.oneDay * 1.2 : Math.random() * 100 - 200)
  }

  const option = {
    title: {
      text: 'VaR回测分析',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['VaR值', '实际损失'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates
    },
    yAxis: {
      type: 'value',
      name: '金额 (¥)'
    },
    series: [
      {
        name: 'VaR值',
        type: 'line',
        data: varValues,
        itemStyle: { color: '#F56C6C' },
        lineStyle: { type: 'dashed' }
      },
      {
        name: '实际损失',
        type: 'bar',
        data: actualLosses,
        itemStyle: { 
          color: (params: any) => params.data < 0 ? '#F56C6C' : '#67C23A'
        }
      }
    ]
  }

  chart.setOption(option)
}

const renderCorrelationChart = () => {
  if (!correlationChartRef.value || !assessment.value) return

  const chart = echarts.init(correlationChartRef.value)
  
  const factors = assessment.value.riskFactors.map(f => f.name)
  const correlationData = []
  
  // 生成相关性矩阵数据
  for (let i = 0; i < factors.length; i++) {
    for (let j = 0; j < factors.length; j++) {
      const correlation = i === j ? 1 : (Math.random() * 2 - 1)
      correlationData.push([i, j, correlation])
    }
  }

  const option = {
    title: {
      text: '风险因子相关性矩阵',
      left: 'center'
    },
    tooltip: {
      position: 'top',
      formatter: (params: any) => {
        return `${factors[params.data[0]]} vs ${factors[params.data[1]]}<br/>相关系数: ${params.data[2].toFixed(3)}`
      }
    },
    grid: {
      height: '50%',
      top: '10%'
    },
    xAxis: {
      type: 'category',
      data: factors,
      splitArea: {
        show: true
      }
    },
    yAxis: {
      type: 'category',
      data: factors,
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: -1,
      max: 1,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '15%',
      inRange: {
        color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
      }
    },
    series: [{
      name: '相关性',
      type: 'heatmap',
      data: correlationData,
      label: {
        show: true,
        formatter: (params: any) => params.data[2].toFixed(2)
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }

  chart.setOption(option)
}

const renderRiskTrendChart = () => {
  if (!riskTrendChartRef.value || !assessment.value) return

  const chart = echarts.init(riskTrendChartRef.value)
  
  // 模拟风险趋势数据
  const dates = []
  const riskScores = []
  
  for (let i = 30; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    dates.push(date.toISOString().split('T')[0])
    riskScores.push(assessment.value.overallRisk.score + (Math.random() - 0.5) * 2)
  }

  const option = {
    title: {
      text: '风险评分趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates
    },
    yAxis: {
      type: 'value',
      name: '风险评分',
      min: 0,
      max: 10
    },
    series: [{
      type: 'line',
      data: riskScores,
      smooth: true,
      itemStyle: { color: '#409EFF' },
      areaStyle: { opacity: 0.3 }
    }]
  }

  chart.setOption(option)
}

const renderAlertFrequencyChart = () => {
  if (!alertFrequencyChartRef.value || !assessment.value) return

  const chart = echarts.init(alertFrequencyChartRef.value)
  
  const alertData = [
    { name: '低风险', value: 5, itemStyle: { color: '#67C23A' } },
    { name: '中风险', value: 12, itemStyle: { color: '#E6A23C' } },
    { name: '高风险', value: 8, itemStyle: { color: '#F56C6C' } },
    { name: '极高风险', value: 3, itemStyle: { color: '#C0392B' } }
  ]

  const option = {
    title: {
      text: '预警频率分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [{
      name: '预警次数',
      type: 'pie',
      radius: '60%',
      center: ['50%', '60%'],
      data: alertData,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }

  chart.setOption(option)
}

// 样式类方法
const getRiskScoreClass = (score: number) => {
  if (score < 3) return 'risk-score-low'
  if (score < 7) return 'risk-score-medium'
  return 'risk-score-high'
}

const getRiskLevelLabel = (level: RiskLevel) => {
  const labels = {
    'low': '低风险',
    'medium': '中风险',
    'high': '高风险',
    'very_high': '极高风险'
  }
  return labels[level]
}

const getRiskBarClass = (score: number) => {
  if (score < 3) return 'risk-bar-low'
  if (score < 7) return 'risk-bar-medium'
  return 'risk-bar-high'
}

const getExposureClass = (exposure: number) => {
  if (Math.abs(exposure) < 0.1) return 'exposure-low'
  if (Math.abs(exposure) < 0.3) return 'exposure-medium'
  return 'exposure-high'
}

const getImpactClass = (impact: number) => {
  if (impact > 0) return 'impact-positive'
  if (impact < -0.1) return 'impact-negative'
  return 'impact-neutral'
}

const getAlertClass = (severity: AlertSeverity) => {
  const classes = {
    'low': 'alert-low',
    'medium': 'alert-medium',
    'high': 'alert-high',
    'critical': 'alert-critical'
  }
  return classes[severity]
}

const getAlertIcon = (severity: AlertSeverity) => {
  const icons = {
    'low': 'el-icon-info',
    'medium': 'el-icon-warning',
    'high': 'el-icon-warning-outline',
    'critical': 'el-icon-error'
  }
  return icons[severity]
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleString()
}

const formatProgress = (percentage: number) => {
  return `${percentage.toFixed(0)}%`
}

onMounted(() => {
  // 默认选择第一个股票
  if (popularSymbols.length > 0) {
    selectedSymbol.value = popularSymbols[0]
  }
})
</script>

<style scoped lang="scss">
.risk-assessment {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;

  .assessment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

    h3 {
      margin: 0;
      color: #2c3e50;
      font-size: 20px;
    }

    .controls {
      display: flex;
      gap: 15px;
      align-items: center;

      .el-select {
        width: 150px;
      }
    }
  }

  .risk-overview {
    margin-bottom: 20px;

    .risk-card {
      height: 120px;
      
      .card-header {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        color: #666;
        margin-bottom: 15px;

        i {
          font-size: 18px;
        }
      }

      .card-content {
        text-align: center;

        div:first-child {
          font-size: 32px;
          font-weight: bold;
          margin-bottom: 5px;
        }

        .risk-level {
          font-size: 14px;
          color: #666;
        }

        .risk-indicator {
          font-size: 12px;
          color: #999;
          margin-top: 5px;
        }
      }

      // 风险评分样式
      .risk-score-low {
        color: #67C23A;
      }

      .risk-score-medium {
        color: #E6A23C;
      }

      .risk-score-high {
        color: #F56C6C;
      }
    }
  }

  .risk-details {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

    .risk-matrix {
      padding: 20px;

      .matrix-chart {
        margin-bottom: 30px;

        h4 {
          margin-bottom: 15px;
          color: #2c3e50;
        }

        .chart-container {
          height: 400px;
          width: 100%;
        }
      }

      .matrix-analysis {
        h4 {
          margin-bottom: 15px;
          color: #2c3e50;
        }

        .risk-distribution {
          .risk-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;

            &:last-child {
              border-bottom: none;
            }

            .risk-category {
              width: 120px;
              font-weight: 600;
              color: #2c3e50;
            }

            .risk-bar {
              flex: 1;
              height: 8px;
              background: #f0f0f0;
              border-radius: 4px;
              margin: 0 15px;
              overflow: hidden;

              .risk-fill {
                height: 100%;
                transition: width 0.3s ease;

                &.risk-bar-low {
                  background: #67C23A;
                }

                &.risk-bar-medium {
                  background: #E6A23C;
                }

                &.risk-bar-high {
                  background: #F56C6C;
                }
              }
            }

            .risk-score {
              width: 40px;
              text-align: center;
              font-weight: 600;
              color: #2c3e50;
            }

            .risk-description {
              width: 200px;
              font-size: 12px;
              color: #666;
              text-align: right;
            }
          }
        }
      }
    }

    .var-analysis {
      padding: 20px;

      .var-metrics {
        .var-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px 0;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .var-label {
            font-weight: 600;
            color: #2c3e50;
          }

          .var-value {
            font-family: monospace;
            font-weight: bold;

            &.negative {
              color: #F56C6C;
            }
          }
        }
      }

      .stress-test {
        .stress-scenario {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 0;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .scenario-name {
            font-weight: 600;
            color: #2c3e50;
            flex: 1;
          }

          .scenario-impact {
            flex: 0 0 80px;
            text-align: center;
            font-weight: bold;
          }

          .scenario-probability {
            flex: 0 0 100px;
            text-align: right;
            font-size: 12px;
            color: #666;
          }
        }
      }

      .var-chart {
        margin-top: 30px;

        h4 {
          margin-bottom: 15px;
          color: #2c3e50;
        }

        .chart-container {
          height: 300px;
          width: 100%;
        }
      }
    }

    .risk-factors {
      padding: 20px;

      .factors-analysis {
        margin-bottom: 30px;

        h4 {
          margin-bottom: 15px;
          color: #2c3e50;
        }
      }

      .correlation-matrix {
        h4 {
          margin-bottom: 15px;
          color: #2c3e50;
        }

        .chart-container {
          height: 400px;
          width: 100%;
        }
      }
    }

    .risk-monitoring {
      padding: 20px;

      .alert-settings {
        margin-bottom: 30px;

        h4 {
          margin-bottom: 15px;
          color: #2c3e50;
        }

        .threshold-settings {
          .threshold-item {
            margin-bottom: 20px;

            label {
              display: block;
              margin-bottom: 8px;
              font-weight: 600;
              color: #2c3e50;
            }
          }
        }

        .current-alerts {
          max-height: 300px;
          overflow-y: auto;

          .alert-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 6px;
            border-left: 4px solid;

            &.alert-low {
              background: #f0f9ff;
              border-left-color: #409EFF;
            }

            &.alert-medium {
              background: #fdf6ec;
              border-left-color: #E6A23C;
            }

            &.alert-high {
              background: #fef0f0;
              border-left-color: #F56C6C;
            }

            &.alert-critical {
              background: #fef0f0;
              border-left-color: #C0392B;
            }

            .alert-icon {
              margin-right: 15px;
              font-size: 20px;

              i {
                color: inherit;
              }
            }

            .alert-content {
              flex: 1;

              .alert-title {
                font-weight: 600;
                color: #2c3e50;
                margin-bottom: 5px;
              }

              .alert-message {
                color: #666;
                font-size: 14px;
                margin-bottom: 5px;
              }

              .alert-time {
                color: #999;
                font-size: 12px;
              }
            }

            .alert-actions {
              margin-left: 15px;
            }
          }

          .no-alerts {
            text-align: center;
            padding: 40px;
            color: #67C23A;

            i {
              font-size: 48px;
              margin-bottom: 10px;
              display: block;
            }

            span {
              font-size: 16px;
            }
          }
        }
      }

      .monitoring-charts {
        h4 {
          margin-bottom: 15px;
          color: #2c3e50;
        }

        .monitoring-chart {
          h5 {
            margin-bottom: 10px;
            color: #2c3e50;
            font-size: 14px;
          }

          .chart-container {
            height: 250px;
            width: 100%;
          }
        }
      }
    }
  }

  .loading-container {
    text-align: center;
    padding: 40px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

    .loading-message {
      margin-top: 15px;
      color: #666;
      font-size: 14px;
    }
  }

  // 暴露度样式
  .exposure-low {
    color: #67C23A;
    font-weight: bold;
  }

  .exposure-medium {
    color: #E6A23C;
    font-weight: bold;
  }

  .exposure-high {
    color: #F56C6C;
    font-weight: bold;
  }

  // 影响样式
  .impact-positive {
    color: #67C23A;
    font-weight: bold;
  }

  .impact-negative {
    color: #F56C6C;
    font-weight: bold;
  }

  .impact-neutral {
    color: #666;
  }
}
</style> 