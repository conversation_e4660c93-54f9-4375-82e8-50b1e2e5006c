<template>
  <div class="stock-analyzer">
    <!-- 分析控制面板 -->
    <div class="analyzer-controls">
      <el-card class="control-card">
        <template #header>
          <div class="card-header">
            <h3>智能股票分析器</h3>
            <el-button 
              type="primary" 
              :loading="loading" 
              @click="startAnalysis"
              :disabled="!selectedSymbol"
            >
              开始分析
            </el-button>
          </div>
        </template>

        <div class="control-form">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="股票代码">
                <el-select
                  v-model="selectedSymbol"
                  placeholder="选择或输入股票代码"
                  filterable
                  allow-create
                  style="width: 100%"
                >
                  <el-option
                    v-for="symbol in popularSymbols"
                    :key="symbol"
                    :label="symbol"
                    :value="symbol"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="分析时间范围">
                <el-select v-model="analysisOptions.timeframe" style="width: 100%">
                  <el-option label="1天" value="1D" />
                  <el-option label="1周" value="1W" />
                  <el-option label="1月" value="1M" />
                  <el-option label="3月" value="3M" />
                  <el-option label="1年" value="1Y" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="分析深度">
                <el-select v-model="analysisOptions.depth" style="width: 100%">
                  <el-option label="快速分析" value="quick" />
                  <el-option label="标准分析" value="standard" />
                  <el-option label="深度分析" value="deep" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>

    <!-- 分析结果展示 -->
    <div v-if="analysisResult" class="analysis-results">
      <!-- 综合评分卡片 -->
      <el-row :gutter="20" class="score-cards">
        <el-col :span="6">
          <el-card class="score-card overall-score">
            <div class="score-content">
              <div class="score-value" :class="getScoreClass(analysisResult.score)">
                {{ analysisResult.score.toFixed(1) }}
              </div>
              <div class="score-grade">{{ analysisResult.grade }}</div>
              <div class="score-label">综合评分</div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="score-card">
            <div class="score-content">
              <div class="score-value recommendation" :class="getRecommendationClass(analysisResult.recommendation)">
                {{ getRecommendationText(analysisResult.recommendation) }}
              </div>
              <div class="score-label">投资建议</div>
              <div class="confidence">置信度: {{ (analysisResult.confidence * 100).toFixed(1) }}%</div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="score-card">
            <div class="score-content">
              <div class="score-value trend" :class="getTrendClass(analysisResult.technicalAnalysis.trend)">
                {{ getTrendText(analysisResult.technicalAnalysis.trend) }}
              </div>
              <div class="score-label">技术趋势</div>
              <div class="momentum">动量: {{ (analysisResult.technicalAnalysis.momentum * 100).toFixed(1) }}%</div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="score-card">
            <div class="score-content">
              <div class="score-value risk" :class="getRiskClass(analysisResult.riskAssessment.overall_risk)">
                {{ getRiskText(analysisResult.riskAssessment.overall_risk) }}
              </div>
              <div class="score-label">风险等级</div>
              <div class="volatility">波动率: {{ (analysisResult.riskAssessment.volatility * 100).toFixed(1) }}%</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 详细分析 -->
      <el-card class="analysis-details">
        <el-tabs v-model="activeTab" type="card">
          <!-- 因子分析 -->
          <el-tab-pane label="因子分析" name="factors">
            <div class="factors-analysis">
              <h4>关键因子分析</h4>
              <div
                v-for="factor in analysisResult.factors"
                :key="factor.name"
                class="factor-item"
              >
                <div class="factor-header">
                  <span class="factor-name">{{ factor.name }}</span>
                  <span class="factor-value" :class="getImpactClass(factor.impact)">
                    {{ factor.value.toFixed(1) }}
                  </span>
                </div>
                <div class="factor-explanation">
                  {{ factor.explanation }}
                </div>
                <el-progress
                  :percentage="factor.value"
                  :color="getProgressColor(factor.impact)"
                  :show-text="false"
                  class="factor-progress"
                />
              </div>
            </div>
          </el-tab-pane>

          <!-- 价格预测 -->
          <el-tab-pane label="价格预测" name="prediction">
            <div class="price-prediction">
              <h4>价格目标</h4>
              <div class="target-list">
                <div class="target-item">
                  <label>短期目标 (1个月)</label>
                  <span class="target-price">
                    ${{ analysisResult.predictions.price_target.short_term.toFixed(2) }}
                  </span>
                </div>
                <div class="target-item">
                  <label>中期目标 (3个月)</label>
                  <span class="target-price">
                    ${{ analysisResult.predictions.price_target.medium_term.toFixed(2) }}
                  </span>
                </div>
                <div class="target-item">
                  <label>长期目标 (12个月)</label>
                  <span class="target-price">
                    ${{ analysisResult.predictions.price_target.long_term.toFixed(2) }}
                  </span>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading && !analysisResult" class="loading-container">
      <el-card>
        <div class="loading-content">
          <h3>AI正在分析中...</h3>
          <p>{{ loadingMessage }}</p>
          <el-progress :percentage="loadingProgress" :show-text="false" />
        </div>
      </el-card>
    </div>

    <!-- 错误状态 -->
    <div v-if="error" class="error-container">
      <el-alert
        :title="error"
        type="error"
        show-icon
        :closable="false"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useStockAI } from '@/composables/ai/useAI'
import { StockAIAnalysis } from '@/types/ai'

// 组合式API
const { loading, error, analyzeStock } = useStockAI()

// 响应式数据
const selectedSymbol = ref('')
const activeTab = ref('factors')
const analysisResult = ref<StockAIAnalysis | null>(null)
const loadingProgress = ref(0)
const loadingMessage = ref('正在初始化AI模型...')

// 分析选项
const analysisOptions = reactive({
  timeframe: '1M',
  depth: 'standard',
  modules: ['technical', 'fundamental', 'sentiment', 'risk'],
  indicators: ['MA', 'RSI', 'MACD', 'BB']
})

// 热门股票代码
const popularSymbols = [
  'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA',
  'NVDA', 'META', 'NFLX', 'AMD', 'INTC'
]

// 开始分析
const startAnalysis = async () => {
  if (!selectedSymbol.value) return
  
  // 模拟加载进度
  loadingProgress.value = 0
  const progressInterval = setInterval(() => {
    if (loadingProgress.value < 90) {
      loadingProgress.value += Math.random() * 10
      updateLoadingMessage()
    }
  }, 500)

  try {
    const result = await analyzeStock(selectedSymbol.value, analysisOptions)
    if (result) {
      analysisResult.value = result
      loadingProgress.value = 100
      loadingMessage.value = '分析完成！'
    }
  } finally {
    clearInterval(progressInterval)
    setTimeout(() => {
      loadingProgress.value = 0
      loadingMessage.value = '正在初始化AI模型...'
    }, 1000)
  }
}

// 更新加载消息
const updateLoadingMessage = () => {
  const messages = [
    '正在获取股票数据...',
    '正在执行技术分析...',
    '正在分析基本面...',
    '正在评估市场情绪...',
    '正在计算风险指标...',
    '正在生成预测模型...',
    '正在综合分析结果...'
  ]
  const index = Math.floor(loadingProgress.value / 15) % messages.length
  loadingMessage.value = messages[index]
}

// 样式辅助函数
const getScoreClass = (score: number) => {
  if (score >= 80) return 'excellent'
  if (score >= 70) return 'good'
  if (score >= 60) return 'average'
  if (score >= 40) return 'poor'
  return 'very-poor'
}

const getRecommendationClass = (rec: string) => {
  if (rec.includes('buy')) return 'buy'
  if (rec.includes('sell')) return 'sell'
  return 'hold'
}

const getRecommendationText = (rec: string) => {
  const map: Record<string, string> = {
    'strong_buy': '强烈买入',
    'buy': '买入',
    'hold': '持有',
    'sell': '卖出',
    'strong_sell': '强烈卖出'
  }
  return map[rec] || rec
}

const getTrendClass = (trend: string) => {
  return trend === 'bullish' ? 'bullish' : trend === 'bearish' ? 'bearish' : 'sideways'
}

const getTrendText = (trend: string) => {
  const map: Record<string, string> = {
    'bullish': '看涨',
    'bearish': '看跌',
    'sideways': '震荡'
  }
  return map[trend] || trend
}

const getRiskClass = (risk: string) => {
  return `risk-${risk}`
}

const getRiskText = (risk: string) => {
  const map: Record<string, string> = {
    'low': '低风险',
    'medium': '中风险',
    'high': '高风险'
  }
  return map[risk] || risk
}

const getImpactClass = (impact: string) => {
  return `impact-${impact}`
}

const getProgressColor = (impact: string) => {
  const colors: Record<string, string> = {
    'positive': '#67C23A',
    'negative': '#F56C6C',
    'neutral': '#909399'
  }
  return colors[impact] || '#409EFF'
}
</script>

<style scoped lang="scss">
.stock-analyzer {
  padding: 20px;

  .analyzer-controls {
    margin-bottom: 20px;

    .control-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
          margin: 0;
          color: #303133;
        }
      }

      .control-form {
        margin-top: 20px;
      }
    }
  }

  .analysis-results {
    .score-cards {
      margin-bottom: 20px;

      .score-card {
        text-align: center;
        
        .score-content {
          .score-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 8px;

            &.excellent { color: #67C23A; }
            &.good { color: #409EFF; }
            &.average { color: #E6A23C; }
            &.poor { color: #F56C6C; }
            &.very-poor { color: #F56C6C; }

            &.recommendation {
              font-size: 1.5em;
              
              &.buy { color: #67C23A; }
              &.sell { color: #F56C6C; }
              &.hold { color: #E6A23C; }
            }

            &.trend {
              font-size: 1.5em;
              
              &.bullish { color: #67C23A; }
              &.bearish { color: #F56C6C; }
              &.sideways { color: #E6A23C; }
            }

            &.risk {
              font-size: 1.5em;
              
              &.risk-low { color: #67C23A; }
              &.risk-medium { color: #E6A23C; }
              &.risk-high { color: #F56C6C; }
            }
          }

          .score-grade {
            font-size: 1.2em;
            font-weight: bold;
            color: #606266;
            margin-bottom: 5px;
          }

          .score-label {
            font-size: 0.9em;
            color: #909399;
            margin-bottom: 5px;
          }

          .confidence, .momentum, .volatility {
            font-size: 0.8em;
            color: #606266;
          }
        }
      }
    }

    .analysis-details {
      .factors-analysis {
        h4 {
          margin-bottom: 20px;
          color: #303133;
        }

        .factor-item {
          margin-bottom: 20px;
          padding: 15px;
          border: 1px solid #EBEEF5;
          border-radius: 4px;

          .factor-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .factor-name {
              font-weight: bold;
              color: #303133;
            }

            .factor-value {
              font-weight: bold;
              
              &.impact-positive { color: #67C23A; }
              &.impact-negative { color: #F56C6C; }
              &.impact-neutral { color: #909399; }
            }
          }

          .factor-explanation {
            font-size: 0.85em;
            color: #909399;
            margin-bottom: 10px;
          }

          .factor-progress {
            margin-top: 10px;
          }
        }
      }

      .price-prediction {
        h4 {
          margin-bottom: 20px;
          color: #303133;
        }

        .target-list {
          .target-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            margin-bottom: 10px;
            background: #F5F7FA;
            border-radius: 4px;

            label {
              font-weight: bold;
              color: #606266;
            }

            .target-price {
              font-size: 1.1em;
              font-weight: bold;
              color: #67C23A;
            }
          }
        }
      }
    }
  }

  .loading-container {
    text-align: center;
    padding: 40px;

    .loading-content {
      h3 {
        color: #303133;
        margin-bottom: 10px;
      }

      p {
        color: #606266;
        margin-bottom: 20px;
      }
    }
  }

  .error-container {
    margin: 20px 0;
  }
}
</style> 