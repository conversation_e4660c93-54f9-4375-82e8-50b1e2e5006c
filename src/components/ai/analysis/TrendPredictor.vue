<template>
  <div class="trend-predictor">
    <div class="predictor-header">
      <h3>市场趋势预测</h3>
      <div class="controls">
        <el-select
          v-model="selectedSymbol"
          placeholder="选择股票"
          @change="onSymbolChange"
          :loading="loading"
        >
          <el-option
            v-for="symbol in popularSymbols"
            :key="symbol"
            :label="symbol"
            :value="symbol"
          />
        </el-select>
        
        <el-select
          v-model="predictionHorizon"
          placeholder="预测周期"
          @change="onHorizonChange"
        >
          <el-option label="短期 (1-7天)" value="short" />
          <el-option label="中期 (1-4周)" value="medium" />
          <el-option label="长期 (1-3月)" value="long" />
        </el-select>
        
        <el-button
          type="primary"
          @click="generatePrediction"
          :loading="loading"
          :disabled="!selectedSymbol"
        >
          生成预测
        </el-button>
      </div>
    </div>

    <!-- 预测结果卡片 -->
    <div v-if="prediction" class="prediction-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="prediction-card trend-card">
            <div class="card-header">
              <i class="el-icon-trend-charts"></i>
              <span>趋势方向</span>
            </div>
            <div class="card-value">
              <span :class="getTrendClass(prediction.trendDirection)">
                {{ getTrendLabel(prediction.trendDirection) }}
              </span>
              <div class="confidence">
                置信度: {{ (prediction.confidence * 100).toFixed(1) }}%
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="prediction-card price-card">
            <div class="card-header">
              <i class="el-icon-money"></i>
              <span>目标价格</span>
            </div>
            <div class="card-value">
              <span class="price">¥{{ prediction.targetPrice.toFixed(2) }}</span>
              <div class="price-range">
                范围: ¥{{ prediction.priceRange.min.toFixed(2) }} - ¥{{ prediction.priceRange.max.toFixed(2) }}
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="prediction-card volatility-card">
            <div class="card-header">
              <i class="el-icon-warning"></i>
              <span>波动性</span>
            </div>
            <div class="card-value">
              <span :class="getVolatilityClass(prediction.volatility)">
                {{ getVolatilityLabel(prediction.volatility) }}
              </span>
              <div class="volatility-value">
                {{ (prediction.volatility * 100).toFixed(1) }}%
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="prediction-card probability-card">
            <div class="card-header">
              <i class="el-icon-pie-chart"></i>
              <span>成功概率</span>
            </div>
            <div class="card-value">
              <span class="probability">{{ (prediction.successProbability * 100).toFixed(1) }}%</span>
              <div class="probability-bar">
                <div 
                  class="probability-fill"
                  :style="{ width: (prediction.successProbability * 100) + '%' }"
                ></div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 预测详情标签页 -->
    <div v-if="prediction" class="prediction-details">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 趋势分析 -->
        <el-tab-pane label="趋势分析" name="trend">
          <div class="trend-analysis">
            <div class="trend-chart">
              <h4>价格趋势预测</h4>
              <div ref="trendChartRef" class="chart-container"></div>
            </div>
            
            <div class="trend-factors">
              <h4>关键影响因子</h4>
              <div class="factors-list">
                <div
                  v-for="factor in prediction.keyFactors"
                  :key="factor.name"
                  class="factor-item"
                >
                  <div class="factor-name">{{ factor.name }}</div>
                  <div class="factor-impact">
                    <span :class="getImpactClass(factor.impact)">
                      {{ getImpactLabel(factor.impact) }}
                    </span>
                    <div class="factor-weight">
                      权重: {{ (factor.weight * 100).toFixed(1) }}%
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 技术指标 -->
        <el-tab-pane label="技术指标" name="technical">
          <div class="technical-indicators">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-card>
                  <div slot="header">移动平均线信号</div>
                  <div class="indicator-list">
                    <div
                      v-for="ma in prediction.technicalIndicators.movingAverages"
                      :key="ma.period"
                      class="indicator-item"
                    >
                      <span class="indicator-name">MA{{ ma.period }}</span>
                      <span :class="getSignalClass(ma.signal)">
                        {{ getSignalLabel(ma.signal) }}
                      </span>
                      <span class="indicator-value">{{ ma.value.toFixed(2) }}</span>
                    </div>
                  </div>
                </el-card>
              </el-col>
              
              <el-col :span="12">
                <el-card>
                  <div slot="header">动量指标</div>
                  <div class="indicator-list">
                    <div class="indicator-item">
                      <span class="indicator-name">RSI</span>
                      <span :class="getRSIClass(prediction.technicalIndicators.rsi)">
                        {{ prediction.technicalIndicators.rsi.toFixed(1) }}
                      </span>
                    </div>
                    <div class="indicator-item">
                      <span class="indicator-name">MACD</span>
                      <span :class="getSignalClass(prediction.technicalIndicators.macd.signal)">
                        {{ getSignalLabel(prediction.technicalIndicators.macd.signal) }}
                      </span>
                    </div>
                    <div class="indicator-item">
                      <span class="indicator-name">布林带</span>
                      <span :class="getSignalClass(prediction.technicalIndicators.bollingerBands.signal)">
                        {{ getSignalLabel(prediction.technicalIndicators.bollingerBands.signal) }}
                      </span>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>

        <!-- 风险评估 -->
        <el-tab-pane label="风险评估" name="risk">
          <div class="risk-assessment">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-card class="risk-card">
                  <div slot="header">整体风险等级</div>
                  <div class="risk-level">
                    <div :class="getRiskLevelClass(prediction.riskAssessment.overallRisk)">
                      {{ getRiskLevelLabel(prediction.riskAssessment.overallRisk) }}
                    </div>
                    <div class="risk-score">
                      风险评分: {{ prediction.riskAssessment.riskScore.toFixed(1) }}/10
                    </div>
                  </div>
                </el-card>
              </el-col>
              
              <el-col :span="16">
                <el-card>
                  <div slot="header">风险分解</div>
                  <div class="risk-breakdown">
                    <div
                      v-for="risk in prediction.riskAssessment.riskFactors"
                      :key="risk.type"
                      class="risk-factor"
                    >
                      <div class="risk-name">{{ risk.name }}</div>
                      <div class="risk-level">
                        <span :class="getRiskLevelClass(risk.level)">
                          {{ getRiskLevelLabel(risk.level) }}
                        </span>
                      </div>
                      <div class="risk-description">{{ risk.description }}</div>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>

        <!-- 场景分析 -->
        <el-tab-pane label="场景分析" name="scenarios">
          <div class="scenario-analysis">
            <h4>不同市场情景下的预测</h4>
            <el-table :data="prediction.scenarioAnalysis" stripe>
              <el-table-column prop="scenario" label="情景" width="120">
                <template #default="{ row }">
                  <span :class="getScenarioClass(row.scenario)">
                    {{ getScenarioLabel(row.scenario) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="probability" label="概率" width="100">
                <template #default="{ row }">
                  {{ (row.probability * 100).toFixed(1) }}%
                </template>
              </el-table-column>
              <el-table-column prop="priceTarget" label="目标价格" width="120">
                <template #default="{ row }">
                  ¥{{ row.priceTarget.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column prop="expectedReturn" label="预期收益" width="120">
                <template #default="{ row }">
                  <span :class="getReturnClass(row.expectedReturn)">
                    {{ (row.expectedReturn * 100).toFixed(1) }}%
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="description" label="描述" show-overflow-tooltip />
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading && !prediction" class="loading-container">
      <el-progress
        :percentage="progress"
        :format="formatProgress"
        status="success"
      />
      <p class="loading-message">{{ loadingMessage }}</p>
    </div>

    <!-- 错误状态 -->
    <el-alert
      v-if="error"
      :title="error"
      type="error"
      :closable="false"
      show-icon
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { useTrendPrediction } from '@/composables/ai/useTrendPrediction'
import type { 
  TrendPrediction, 
  PredictionHorizon,
  TrendDirection,
  RiskLevel,
  MarketScenario
} from '@/types/ai'

// 组合式API
const {
  prediction,
  loading,
  error,
  generatePrediction: predict,
  clearPrediction
} = useTrendPrediction()

// 响应式数据
const selectedSymbol = ref<string>('')
const predictionHorizon = ref<PredictionHorizon>('medium')
const activeTab = ref('trend')
const progress = ref(0)
const loadingMessage = ref('')
const trendChartRef = ref<HTMLElement>()

// 热门股票列表
const popularSymbols = [
  '000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '600519.SH',
  '000858.SZ', '002415.SZ', '300059.SZ', '600276.SH', '000725.SZ'
]

// 方法
const onSymbolChange = () => {
  clearPrediction()
}

const onHorizonChange = () => {
  if (selectedSymbol.value) {
    generatePrediction()
  }
}

const generatePrediction = async () => {
  if (!selectedSymbol.value) {
    ElMessage.warning('请先选择股票')
    return
  }

  progress.value = 0
  loadingMessage.value = '正在分析历史数据...'
  
  const progressInterval = setInterval(() => {
    if (progress.value < 90) {
      progress.value += Math.random() * 10
      if (progress.value > 30 && progress.value < 60) {
        loadingMessage.value = '正在运行预测模型...'
      } else if (progress.value >= 60) {
        loadingMessage.value = '正在生成预测结果...'
      }
    }
  }, 200)

  try {
    await predict(selectedSymbol.value, predictionHorizon.value)
    progress.value = 100
    loadingMessage.value = '预测完成'
    
    // 渲染趋势图表
    await nextTick()
    renderTrendChart()
    
  } catch (err) {
    ElMessage.error('预测生成失败: ' + (err as Error).message)
  } finally {
    clearInterval(progressInterval)
  }
}

const renderTrendChart = () => {
  if (!trendChartRef.value || !prediction.value) return

  const chart = echarts.init(trendChartRef.value)
  
  const option = {
    title: {
      text: '价格趋势预测',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['历史价格', '预测价格', '置信区间'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: prediction.value.historicalData.dates.concat(prediction.value.forecastData.dates)
    },
    yAxis: {
      type: 'value',
      name: '价格 (¥)'
    },
    series: [
      {
        name: '历史价格',
        type: 'line',
        data: prediction.value.historicalData.prices,
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '预测价格',
        type: 'line',
        data: Array(prediction.value.historicalData.prices.length).fill(null)
          .concat(prediction.value.forecastData.prices),
        itemStyle: { color: '#67C23A' },
        lineStyle: { type: 'dashed' }
      },
      {
        name: '置信区间',
        type: 'line',
        data: Array(prediction.value.historicalData.prices.length).fill(null)
          .concat(prediction.value.forecastData.upperBound),
        itemStyle: { color: '#E6A23C', opacity: 0.3 },
        areaStyle: { opacity: 0.1 }
      }
    ]
  }

  chart.setOption(option)
}

// 样式类方法
const getTrendClass = (direction: TrendDirection) => {
  const classes = {
    'bullish': 'trend-bullish',
    'bearish': 'trend-bearish',
    'sideways': 'trend-sideways'
  }
  return classes[direction]
}

const getTrendLabel = (direction: TrendDirection) => {
  const labels = {
    'bullish': '看涨',
    'bearish': '看跌',
    'sideways': '震荡'
  }
  return labels[direction]
}

const getVolatilityClass = (volatility: number) => {
  if (volatility < 0.1) return 'volatility-low'
  if (volatility < 0.2) return 'volatility-medium'
  return 'volatility-high'
}

const getVolatilityLabel = (volatility: number) => {
  if (volatility < 0.1) return '低波动'
  if (volatility < 0.2) return '中波动'
  return '高波动'
}

const getSignalClass = (signal: string) => {
  const classes = {
    'buy': 'signal-buy',
    'sell': 'signal-sell',
    'hold': 'signal-hold'
  }
  return classes[signal as keyof typeof classes]
}

const getSignalLabel = (signal: string) => {
  const labels = {
    'buy': '买入',
    'sell': '卖出',
    'hold': '持有'
  }
  return labels[signal as keyof typeof labels]
}

const getRSIClass = (rsi: number) => {
  if (rsi > 70) return 'rsi-overbought'
  if (rsi < 30) return 'rsi-oversold'
  return 'rsi-normal'
}

const getRiskLevelClass = (level: RiskLevel) => {
  const classes = {
    'low': 'risk-low',
    'medium': 'risk-medium',
    'high': 'risk-high',
    'very_high': 'risk-very-high'
  }
  return classes[level]
}

const getRiskLevelLabel = (level: RiskLevel) => {
  const labels = {
    'low': '低风险',
    'medium': '中风险',
    'high': '高风险',
    'very_high': '极高风险'
  }
  return labels[level]
}

const getScenarioClass = (scenario: MarketScenario) => {
  const classes = {
    'bull': 'scenario-bull',
    'bear': 'scenario-bear',
    'neutral': 'scenario-neutral'
  }
  return classes[scenario]
}

const getScenarioLabel = (scenario: MarketScenario) => {
  const labels = {
    'bull': '牛市',
    'bear': '熊市',
    'neutral': '中性'
  }
  return labels[scenario]
}

const getReturnClass = (returnValue: number) => {
  if (returnValue > 0) return 'return-positive'
  if (returnValue < 0) return 'return-negative'
  return 'return-neutral'
}

const getImpactClass = (impact: number) => {
  if (impact > 0.1) return 'impact-positive'
  if (impact < -0.1) return 'impact-negative'
  return 'impact-neutral'
}

const getImpactLabel = (impact: number) => {
  if (impact > 0.1) return '积极'
  if (impact < -0.1) return '消极'
  return '中性'
}

const formatProgress = (percentage: number) => {
  return `${percentage.toFixed(0)}%`
}

onMounted(() => {
  // 默认选择第一个股票
  if (popularSymbols.length > 0) {
    selectedSymbol.value = popularSymbols[0]
  }
})
</script>

<style scoped lang="scss">
.trend-predictor {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;

  .predictor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

    h3 {
      margin: 0;
      color: #2c3e50;
      font-size: 20px;
    }

    .controls {
      display: flex;
      gap: 15px;
      align-items: center;

      .el-select {
        width: 150px;
      }
    }
  }

  .prediction-cards {
    margin-bottom: 20px;

    .prediction-card {
      height: 120px;
      
      .card-header {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        color: #666;
        margin-bottom: 10px;

        i {
          font-size: 18px;
        }
      }

      .card-value {
        text-align: center;

        .price {
          font-size: 24px;
          font-weight: bold;
          color: #2c3e50;
        }

        .price-range {
          font-size: 12px;
          color: #999;
          margin-top: 5px;
        }

        .probability {
          font-size: 28px;
          font-weight: bold;
          color: #67C23A;
        }

        .probability-bar {
          width: 100%;
          height: 6px;
          background: #f0f0f0;
          border-radius: 3px;
          margin-top: 10px;
          overflow: hidden;

          .probability-fill {
            height: 100%;
            background: linear-gradient(90deg, #67C23A, #85CE61);
            transition: width 0.3s ease;
          }
        }

        .confidence {
          font-size: 12px;
          color: #666;
          margin-top: 5px;
        }

        .volatility-value {
          font-size: 14px;
          color: #999;
          margin-top: 5px;
        }
      }

      // 趋势样式
      .trend-bullish {
        color: #67C23A;
        font-weight: bold;
        font-size: 18px;
      }

      .trend-bearish {
        color: #F56C6C;
        font-weight: bold;
        font-size: 18px;
      }

      .trend-sideways {
        color: #E6A23C;
        font-weight: bold;
        font-size: 18px;
      }

      // 波动性样式
      .volatility-low {
        color: #67C23A;
        font-weight: bold;
        font-size: 16px;
      }

      .volatility-medium {
        color: #E6A23C;
        font-weight: bold;
        font-size: 16px;
      }

      .volatility-high {
        color: #F56C6C;
        font-weight: bold;
        font-size: 16px;
      }
    }
  }

  .prediction-details {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

    .trend-analysis {
      padding: 20px;

      .trend-chart {
        margin-bottom: 30px;

        h4 {
          margin-bottom: 15px;
          color: #2c3e50;
        }

        .chart-container {
          height: 400px;
          width: 100%;
        }
      }

      .trend-factors {
        h4 {
          margin-bottom: 15px;
          color: #2c3e50;
        }

        .factors-list {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 15px;

          .factor-item {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #409EFF;

            .factor-name {
              font-weight: 600;
              color: #2c3e50;
              margin-bottom: 8px;
            }

            .factor-impact {
              display: flex;
              justify-content: space-between;
              align-items: center;

              .factor-weight {
                font-size: 12px;
                color: #666;
              }
            }
          }
        }
      }
    }

    .technical-indicators {
      padding: 20px;

      .indicator-list {
        .indicator-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px 0;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .indicator-name {
            font-weight: 600;
            color: #2c3e50;
          }

          .indicator-value {
            color: #666;
            font-family: monospace;
          }
        }
      }
    }

    .risk-assessment {
      padding: 20px;

      .risk-card {
        text-align: center;

        .risk-level {
          padding: 20px;

          div:first-child {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
          }

          .risk-score {
            color: #666;
            font-size: 14px;
          }
        }
      }

      .risk-breakdown {
        .risk-factor {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 15px 0;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .risk-name {
            font-weight: 600;
            color: #2c3e50;
            flex: 1;
          }

          .risk-level {
            flex: 0 0 80px;
            text-align: center;
          }

          .risk-description {
            flex: 2;
            color: #666;
            font-size: 14px;
            text-align: right;
          }
        }
      }
    }

    .scenario-analysis {
      padding: 20px;

      h4 {
        margin-bottom: 20px;
        color: #2c3e50;
      }
    }
  }

  .loading-container {
    text-align: center;
    padding: 40px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

    .loading-message {
      margin-top: 15px;
      color: #666;
      font-size: 14px;
    }
  }

  // 信号样式
  .signal-buy {
    color: #67C23A;
    font-weight: bold;
  }

  .signal-sell {
    color: #F56C6C;
    font-weight: bold;
  }

  .signal-hold {
    color: #E6A23C;
    font-weight: bold;
  }

  // RSI样式
  .rsi-overbought {
    color: #F56C6C;
    font-weight: bold;
  }

  .rsi-oversold {
    color: #67C23A;
    font-weight: bold;
  }

  .rsi-normal {
    color: #409EFF;
    font-weight: bold;
  }

  // 风险等级样式
  .risk-low {
    color: #67C23A;
    font-weight: bold;
  }

  .risk-medium {
    color: #E6A23C;
    font-weight: bold;
  }

  .risk-high {
    color: #F56C6C;
    font-weight: bold;
  }

  .risk-very-high {
    color: #C0392B;
    font-weight: bold;
  }

  // 场景样式
  .scenario-bull {
    color: #67C23A;
    font-weight: bold;
  }

  .scenario-bear {
    color: #F56C6C;
    font-weight: bold;
  }

  .scenario-neutral {
    color: #E6A23C;
    font-weight: bold;
  }

  // 收益样式
  .return-positive {
    color: #67C23A;
    font-weight: bold;
  }

  .return-negative {
    color: #F56C6C;
    font-weight: bold;
  }

  .return-neutral {
    color: #666;
  }

  // 影响样式
  .impact-positive {
    color: #67C23A;
    font-weight: bold;
  }

  .impact-negative {
    color: #F56C6C;
    font-weight: bold;
  }

  .impact-neutral {
    color: #E6A23C;
    font-weight: bold;
  }
}
</style> 