<template>
  <div class="technical-analyzer">
    <div class="analyzer-header">
      <h3>AI技术分析</h3>
      <p class="description">基于人工智能的全面技术分析系统</p>
    </div>

    <!-- 股票选择和配置 -->
    <div class="analyzer-controls">
      <div class="stock-selection">
        <label>选择股票:</label>
        <select v-model="selectedStock" @change="onStockChange">
          <option value="">请选择股票</option>
          <option v-for="stock in popularStocks" :key="stock.symbol" :value="stock.symbol">
            {{ stock.symbol }} - {{ stock.name }}
          </option>
        </select>
        <input 
          v-model="customSymbol" 
          placeholder="或输入股票代码" 
          @keyup.enter="analyzeCustomStock"
          class="custom-input"
        />
      </div>

      <div class="analysis-config">
        <label>分析周期:</label>
        <select v-model="timeframe">
          <option value="1D">日线</option>
          <option value="1W">周线</option>
          <option value="1M">月线</option>
        </select>

        <label>指标配置:</label>
        <div class="indicators-config">
          <label><input type="checkbox" v-model="indicators.sma" /> SMA</label>
          <label><input type="checkbox" v-model="indicators.macd" /> MACD</label>
          <label><input type="checkbox" v-model="indicators.rsi" /> RSI</label>
          <label><input type="checkbox" v-model="indicators.bollinger" /> 布林带</label>
          <label><input type="checkbox" v-model="indicators.kdj" /> KDJ</label>
        </div>
      </div>

      <button @click="runAnalysis" :disabled="isAnalyzing" class="analyze-btn">
        {{ isAnalyzing ? '分析中...' : '开始分析' }}
      </button>
    </div>

    <!-- 加载状态 -->
    <div v-if="isAnalyzing" class="loading-state">
      <div class="loading-spinner"></div>
      <div class="loading-progress">
        <div class="progress-bar" :style="{ width: `${progress}%` }"></div>
      </div>
      <p class="loading-message">{{ loadingMessage }}</p>
    </div>

    <!-- 分析结果 -->
    <div v-if="analysisResult && !isAnalyzing" class="analysis-results">
      <!-- 综合评分卡片 -->
      <div class="score-card">
        <div class="score-main">
          <div class="score-value" :class="getScoreClass(analysisResult.overallScore)">
            {{ analysisResult.overallScore.toFixed(1) }}
          </div>
          <div class="score-label">综合评分</div>
        </div>
        <div class="score-details">
          <div class="detail-item">
            <span class="label">推荐:</span>
            <span class="value" :class="getRecommendationClass(analysisResult.recommendation)">
              {{ getRecommendationText(analysisResult.recommendation) }}
            </span>
          </div>
          <div class="detail-item">
            <span class="label">置信度:</span>
            <span class="value">{{ (analysisResult.confidence * 100).toFixed(1) }}%</span>
          </div>
          <div class="detail-item">
            <span class="label">风险等级:</span>
            <span class="value" :class="getRiskClass(analysisResult.riskLevel)">
              {{ getRiskText(analysisResult.riskLevel) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 详细分析标签页 -->
      <div class="analysis-tabs">
        <div class="tab-headers">
          <button 
            v-for="tab in tabs" 
            :key="tab.key"
            @click="activeTab = tab.key"
            :class="{ active: activeTab === tab.key }"
            class="tab-header"
          >
            {{ tab.label }}
          </button>
        </div>

        <div class="tab-content">
          <!-- 技术指标 -->
          <div v-if="activeTab === 'indicators'" class="indicators-panel">
            <h4>技术指标分析</h4>
            <div class="indicators-grid">
              <div 
                v-for="indicator in analysisResult.indicators" 
                :key="indicator.name"
                class="indicator-card"
              >
                <div class="indicator-header">
                  <h5>{{ indicator.description }}</h5>
                  <span class="indicator-signal" :class="getSignalClass(indicator.signal)">
                    {{ getSignalText(indicator.signal) }}
                  </span>
                </div>
                <div class="indicator-details">
                  <div class="detail-row">
                    <span>当前值:</span>
                    <span>{{ indicator.value.toFixed(4) }}</span>
                  </div>
                  <div class="detail-row">
                    <span>信号强度:</span>
                    <div class="strength-bar">
                      <div class="strength-fill" :style="{ width: `${indicator.strength}%` }"></div>
                    </div>
                    <span>{{ indicator.strength.toFixed(1) }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 趋势分析 -->
          <div v-if="activeTab === 'trends'" class="trends-panel">
            <h4>趋势分析</h4>
            <div class="trends-grid">
              <div class="trend-card">
                <h5>短期趋势 (5日)</h5>
                <div class="trend-info">
                  <div class="trend-direction" :class="getTrendClass(analysisResult.trendAnalysis.shortTerm.direction)">
                    {{ getTrendText(analysisResult.trendAnalysis.shortTerm.direction) }}
                  </div>
                  <div class="trend-strength">
                    强度: {{ analysisResult.trendAnalysis.shortTerm.strength.toFixed(2) }}%
                  </div>
                  <div class="trend-angle">
                    角度: {{ analysisResult.trendAnalysis.shortTerm.angle.toFixed(1) }}°
                  </div>
                </div>
              </div>

              <div class="trend-card">
                <h5>中期趋势 (20日)</h5>
                <div class="trend-info">
                  <div class="trend-direction" :class="getTrendClass(analysisResult.trendAnalysis.mediumTerm.direction)">
                    {{ getTrendText(analysisResult.trendAnalysis.mediumTerm.direction) }}
                  </div>
                  <div class="trend-strength">
                    强度: {{ analysisResult.trendAnalysis.mediumTerm.strength.toFixed(2) }}%
                  </div>
                  <div class="trend-angle">
                    角度: {{ analysisResult.trendAnalysis.mediumTerm.angle.toFixed(1) }}°
                  </div>
                </div>
              </div>

              <div class="trend-card">
                <h5>长期趋势 (60日)</h5>
                <div class="trend-info">
                  <div class="trend-direction" :class="getTrendClass(analysisResult.trendAnalysis.longTerm.direction)">
                    {{ getTrendText(analysisResult.trendAnalysis.longTerm.direction) }}
                  </div>
                  <div class="trend-strength">
                    强度: {{ analysisResult.trendAnalysis.longTerm.strength.toFixed(2) }}%
                  </div>
                  <div class="trend-angle">
                    角度: {{ analysisResult.trendAnalysis.longTerm.angle.toFixed(1) }}°
                  </div>
                </div>
              </div>

              <div class="trend-card overall">
                <h5>综合趋势</h5>
                <div class="trend-info">
                  <div class="trend-direction" :class="getTrendClass(analysisResult.trendAnalysis.overall.direction)">
                    {{ getTrendText(analysisResult.trendAnalysis.overall.direction) }}
                  </div>
                  <div class="trend-strength">
                    强度: {{ analysisResult.trendAnalysis.overall.strength.toFixed(2) }}%
                  </div>
                  <div class="trend-reliability">
                    可靠性: {{ (analysisResult.trendAnalysis.overall.reliability * 100).toFixed(1) }}%
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 成交量分析 -->
          <div v-if="activeTab === 'volume'" class="volume-panel">
            <h4>成交量分析</h4>
            <div class="volume-stats">
              <div class="stat-card">
                <h5>成交量趋势</h5>
                <div class="stat-value" :class="getVolumeClass(analysisResult.volumeAnalysis.trend)">
                  {{ getVolumeText(analysisResult.volumeAnalysis.trend) }}
                </div>
              </div>
              <div class="stat-card">
                <h5>价量关系</h5>
                <div class="stat-value" :class="getRelationClass(analysisResult.volumeAnalysis.priceVolumeRelation)">
                  {{ getRelationText(analysisResult.volumeAnalysis.priceVolumeRelation) }}
                </div>
              </div>
              <div class="stat-card">
                <h5>平均成交量</h5>
                <div class="stat-value">
                  {{ formatVolume(analysisResult.volumeAnalysis.averageVolume) }}
                </div>
              </div>
              <div class="stat-card">
                <h5>相对成交量</h5>
                <div class="stat-value">
                  {{ analysisResult.volumeAnalysis.relativeVolume.toFixed(2) }}x
                </div>
              </div>
            </div>
          </div>

          <!-- 动量分析 -->
          <div v-if="activeTab === 'momentum'" class="momentum-panel">
            <h4>动量分析</h4>
            <div class="momentum-stats">
              <div class="momentum-chart">
                <div class="chart-item">
                  <span class="chart-label">价格动量</span>
                  <div class="chart-bar">
                    <div 
                      class="chart-fill" 
                      :class="getMomentumClass(analysisResult.momentumAnalysis.priceMomentum)"
                      :style="{ width: `${Math.abs(analysisResult.momentumAnalysis.priceMomentum)}%` }"
                    ></div>
                  </div>
                  <span class="chart-value">{{ analysisResult.momentumAnalysis.priceMomentum.toFixed(2) }}%</span>
                </div>
                <div class="chart-item">
                  <span class="chart-label">成交量动量</span>
                  <div class="chart-bar">
                    <div 
                      class="chart-fill" 
                      :class="getMomentumClass(analysisResult.momentumAnalysis.volumeMomentum)"
                      :style="{ width: `${Math.abs(analysisResult.momentumAnalysis.volumeMomentum)}%` }"
                    ></div>
                  </div>
                  <span class="chart-value">{{ analysisResult.momentumAnalysis.volumeMomentum.toFixed(2) }}%</span>
                </div>
              </div>
              <div class="momentum-summary">
                <div class="summary-item">
                  <span class="label">动量方向:</span>
                  <span class="value" :class="getDirectionClass(analysisResult.momentumAnalysis.direction)">
                    {{ getDirectionText(analysisResult.momentumAnalysis.direction) }}
                  </span>
                </div>
                <div class="summary-item">
                  <span class="label">动量强度:</span>
                  <span class="value">{{ analysisResult.momentumAnalysis.strength.toFixed(2) }}%</span>
                </div>
                <div class="summary-item">
                  <span class="label">背离检测:</span>
                  <span class="value" :class="{ warning: analysisResult.momentumAnalysis.divergence }">
                    {{ analysisResult.momentumAnalysis.divergence ? '存在背离' : '无背离' }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 波动率分析 -->
          <div v-if="activeTab === 'volatility'" class="volatility-panel">
            <h4>波动率分析</h4>
            <div class="volatility-stats">
              <div class="volatility-gauge">
                <div class="gauge-container">
                  <div class="gauge-value">{{ (analysisResult.volatilityAnalysis.current * 100).toFixed(1) }}%</div>
                  <div class="gauge-label">当前波动率</div>
                </div>
              </div>
              <div class="volatility-details">
                <div class="detail-item">
                  <span class="label">波动率趋势:</span>
                  <span class="value" :class="getVolatilityTrendClass(analysisResult.volatilityAnalysis.trend)">
                    {{ getVolatilityTrendText(analysisResult.volatilityAnalysis.trend) }}
                  </span>
                </div>
                <div class="detail-item">
                  <span class="label">波动率状态:</span>
                  <span class="value" :class="getVolatilityStateClass(analysisResult.volatilityAnalysis.state)">
                    {{ getVolatilityStateText(analysisResult.volatilityAnalysis.state) }}
                  </span>
                </div>
                <div class="detail-item">
                  <span class="label">历史百分位:</span>
                  <span class="value">{{ analysisResult.volatilityAnalysis.percentile.toFixed(1) }}%</span>
                </div>
                <div class="detail-item">
                  <span class="label">预测波动率:</span>
                  <span class="value">{{ (analysisResult.volatilityAnalysis.forecast * 100).toFixed(1) }}%</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 支撑阻力 -->
          <div v-if="activeTab === 'support'" class="support-panel">
            <h4>支撑阻力位</h4>
            <div class="support-list">
              <div 
                v-for="level in analysisResult.supportResistance" 
                :key="level.level"
                class="support-item"
                :class="level.type.toLowerCase()"
              >
                <div class="level-info">
                  <div class="level-price">{{ level.level.toFixed(2) }}</div>
                  <div class="level-type">{{ level.type === 'SUPPORT' ? '支撑' : '阻力' }}</div>
                </div>
                <div class="level-details">
                  <div class="detail-row">
                    <span>强度:</span>
                    <div class="strength-bar">
                      <div class="strength-fill" :style="{ width: `${level.strength * 100}%` }"></div>
                    </div>
                    <span>{{ (level.strength * 100).toFixed(1) }}%</span>
                  </div>
                  <div class="detail-row">
                    <span>触及次数:</span>
                    <span>{{ level.touches }}次</span>
                  </div>
                  <div class="detail-row">
                    <span>最后测试:</span>
                    <span>{{ formatDate(level.lastTested) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-if="error" class="error-state">
      <div class="error-icon">⚠️</div>
      <div class="error-message">{{ error }}</div>
      <button @click="clearError" class="retry-btn">重试</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { TechnicalAnalysisAI, type TechnicalAnalysisConfig, type TechnicalDataPoint } from '../../../services/ai/TechnicalAnalysisAI'
import type { TechnicalAnalysisResult } from '../../../types/ai'

// 响应式数据
const selectedStock = ref('')
const customSymbol = ref('')
const timeframe = ref('1D')
const isAnalyzing = ref(false)
const progress = ref(0)
const loadingMessage = ref('')
const analysisResult = ref<TechnicalAnalysisResult | null>(null)
const error = ref('')
const activeTab = ref('indicators')

// 指标配置
const indicators = reactive({
  sma: true,
  macd: true,
  rsi: true,
  bollinger: true,
  kdj: true
})

// 热门股票列表
const popularStocks = ref([
  { symbol: 'AAPL', name: '苹果公司' },
  { symbol: 'MSFT', name: '微软公司' },
  { symbol: 'GOOGL', name: '谷歌' },
  { symbol: 'TSLA', name: '特斯拉' },
  { symbol: 'AMZN', name: '亚马逊' },
  { symbol: 'NVDA', name: '英伟达' },
  { symbol: 'META', name 'Meta' },
  { symbol: 'NFLX', name: '奈飞' }
])

// 标签页配置
const tabs = [
  { key: 'indicators', label: '技术指标' },
  { key: 'trends', label: '趋势分析' },
  { key: 'volume', label: '成交量' },
  { key: 'momentum', label: '动量' },
  { key: 'volatility', label: '波动率' },
  { key: 'support', label: '支撑阻力' }
]

// 技术分析AI实例
const technicalAI = new TechnicalAnalysisAI({
  indicators: ['SMA', 'MACD', 'RSI', 'BOLLINGER', 'KDJ'],
  patterns: ['CANDLESTICK', 'CHART', 'WAVE'],
  timeframes: ['1D', '1W', '1M'],
  sensitivity: 0.7,
  lookbackPeriods: 100
})

// 方法
const onStockChange = () => {
  customSymbol.value = ''
  if (selectedStock.value) {
    runAnalysis()
  }
}

const analyzeCustomStock = () => {
  if (customSymbol.value.trim()) {
    selectedStock.value = ''
    runAnalysis()
  }
}

const runAnalysis = async () => {
  const symbol = selectedStock.value || customSymbol.value.trim().toUpperCase()
  if (!symbol) {
    error.value = '请选择或输入股票代码'
    return
  }

  isAnalyzing.value = true
  error.value = ''
  progress.value = 0
  analysisResult.value = null

  try {
    // 模拟加载进度
    loadingMessage.value = '获取股票数据...'
    progress.value = 20
    await new Promise(resolve => setTimeout(resolve, 500))

    // 生成模拟数据
    const mockData = generateMockData(symbol)
    
    loadingMessage.value = '执行技术分析...'
    progress.value = 50
    await new Promise(resolve => setTimeout(resolve, 800))

    loadingMessage.value = '计算技术指标...'
    progress.value = 75
    await new Promise(resolve => setTimeout(resolve, 600))

    // 执行分析
    const result = await technicalAI.analyzeTechnical(symbol, mockData)
    
    loadingMessage.value = '分析完成'
    progress.value = 100
    await new Promise(resolve => setTimeout(resolve, 300))

    analysisResult.value = result
  } catch (err) {
    error.value = err instanceof Error ? err.message : '分析失败，请重试'
  } finally {
    isAnalyzing.value = false
    progress.value = 0
  }
}

const generateMockData = (symbol: string): TechnicalDataPoint[] => {
  const data: TechnicalDataPoint[] = []
  const basePrice = 100 + Math.random() * 200
  let currentPrice = basePrice
  
  for (let i = 0; i < 100; i++) {
    const change = (Math.random() - 0.5) * 0.04 // ±2% 变化
    currentPrice *= (1 + change)
    
    const high = currentPrice * (1 + Math.random() * 0.02)
    const low = currentPrice * (1 - Math.random() * 0.02)
    const open = i === 0 ? basePrice : data[i - 1].close
    const volume = Math.floor(1000000 + Math.random() * 5000000)
    
    data.push({
      timestamp: Date.now() - (100 - i) * 24 * 60 * 60 * 1000,
      open,
      high,
      low,
      close: currentPrice,
      volume
    })
  }
  
  return data
}

const clearError = () => {
  error.value = ''
}

// 样式辅助方法
const getScoreClass = (score: number) => {
  if (score >= 80) return 'excellent'
  if (score >= 60) return 'good'
  if (score >= 40) return 'neutral'
  return 'poor'
}

const getRecommendationClass = (rec: string) => {
  if (rec.includes('BUY')) return 'buy'
  if (rec.includes('SELL')) return 'sell'
  return 'hold'
}

const getRecommendationText = (rec: string) => {
  const map: { [key: string]: string } = {
    'STRONG_BUY': '强烈买入',
    'BUY': '买入',
    'HOLD': '持有',
    'SELL': '卖出',
    'STRONG_SELL': '强烈卖出'
  }
  return map[rec] || rec
}

const getRiskClass = (risk: string) => {
  const map: { [key: string]: string } = {
    'LOW': 'low-risk',
    'MEDIUM': 'medium-risk',
    'HIGH': 'high-risk',
    'EXTREME': 'extreme-risk'
  }
  return map[risk] || ''
}

const getRiskText = (risk: string) => {
  const map: { [key: string]: string } = {
    'LOW': '低风险',
    'MEDIUM': '中等风险',
    'HIGH': '高风险',
    'EXTREME': '极高风险'
  }
  return map[risk] || risk
}

const getSignalClass = (signal: string) => {
  const map: { [key: string]: string } = {
    'BUY': 'signal-buy',
    'SELL': 'signal-sell',
    'HOLD': 'signal-hold'
  }
  return map[signal] || ''
}

const getSignalText = (signal: string) => {
  const map: { [key: string]: string } = {
    'BUY': '买入',
    'SELL': '卖出',
    'HOLD': '持有'
  }
  return map[signal] || signal
}

const getTrendClass = (direction: string) => {
  const map: { [key: string]: string } = {
    'UP': 'trend-up',
    'DOWN': 'trend-down',
    'SIDEWAYS': 'trend-sideways'
  }
  return map[direction] || ''
}

const getTrendText = (direction: string) => {
  const map: { [key: string]: string } = {
    'UP': '上涨',
    'DOWN': '下跌',
    'SIDEWAYS': '横盘'
  }
  return map[direction] || direction
}

const getVolumeClass = (trend: string) => {
  const map: { [key: string]: string } = {
    'INCREASING': 'volume-increasing',
    'DECREASING': 'volume-decreasing',
    'STABLE': 'volume-stable'
  }
  return map[trend] || ''
}

const getVolumeText = (trend: string) => {
  const map: { [key: string]: string } = {
    'INCREASING': '放量',
    'DECREASING': '缩量',
    'STABLE': '平量'
  }
  return map[trend] || trend
}

const getRelationClass = (relation: string) => {
  const map: { [key: string]: string } = {
    'POSITIVE': 'relation-positive',
    'NEGATIVE': 'relation-negative',
    'NEUTRAL': 'relation-neutral'
  }
  return map[relation] || ''
}

const getRelationText = (relation: string) => {
  const map: { [key: string]: string } = {
    'POSITIVE': '量价齐升',
    'NEGATIVE': '量价背离',
    'NEUTRAL': '量价中性'
  }
  return map[relation] || relation
}

const getMomentumClass = (momentum: number) => {
  return momentum > 0 ? 'momentum-positive' : 'momentum-negative'
}

const getDirectionClass = (direction: string) => {
  const map: { [key: string]: string } = {
    'UP': 'direction-up',
    'DOWN': 'direction-down',
    'NEUTRAL': 'direction-neutral'
  }
  return map[direction] || ''
}

const getDirectionText = (direction: string) => {
  const map: { [key: string]: string } = {
    'UP': '向上',
    'DOWN': '向下',
    'NEUTRAL': '中性'
  }
  return map[direction] || direction
}

const getVolatilityTrendClass = (trend: string) => {
  const map: { [key: string]: string } = {
    'INCREASING': 'vol-increasing',
    'DECREASING': 'vol-decreasing',
    'STABLE': 'vol-stable'
  }
  return map[trend] || ''
}

const getVolatilityTrendText = (trend: string) => {
  const map: { [key: string]: string } = {
    'INCREASING': '上升',
    'DECREASING': '下降',
    'STABLE': '稳定'
  }
  return map[trend] || trend
}

const getVolatilityStateClass = (state: string) => {
  const map: { [key: string]: string } = {
    'LOW': 'vol-low',
    'NORMAL': 'vol-normal',
    'HIGH': 'vol-high',
    'EXTREME': 'vol-extreme'
  }
  return map[state] || ''
}

const getVolatilityStateText = (state: string) => {
  const map: { [key: string]: string } = {
    'LOW': '低波动',
    'NORMAL': '正常',
    'HIGH': '高波动',
    'EXTREME': '极端波动'
  }
  return map[state] || state
}

const formatVolume = (volume: number) => {
  if (volume >= 1e9) return `${(volume / 1e9).toFixed(1)}B`
  if (volume >= 1e6) return `${(volume / 1e6).toFixed(1)}M`
  if (volume >= 1e3) return `${(volume / 1e3).toFixed(1)}K`
  return volume.toString()
}

const formatDate = (timestamp: number) => {
  return new Date(timestamp).toLocaleDateString()
}

// 组件挂载时的初始化
onMounted(() => {
  // 可以在这里加载默认股票的分析
})
</script>

<style scoped lang="scss">
.technical-analyzer {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .analyzer-header {
    text-align: center;
    margin-bottom: 30px;

    h3 {
      color: #2c3e50;
      margin-bottom: 10px;
    }

    .description {
      color: #7f8c8d;
      font-size: 14px;
    }
  }

  .analyzer-controls {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;

    .stock-selection {
      display: flex;
      gap: 10px;
      align-items: center;
      margin-bottom: 15px;

      label {
        font-weight: 500;
        min-width: 80px;
      }

      select, .custom-input {
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
      }

      .custom-input {
        width: 150px;
      }
    }

    .analysis-config {
      display: flex;
      gap: 20px;
      align-items: center;
      margin-bottom: 15px;

      label {
        font-weight: 500;
      }

      .indicators-config {
        display: flex;
        gap: 15px;

        label {
          display: flex;
          align-items: center;
          gap: 5px;
          font-weight: normal;
          cursor: pointer;
        }
      }
    }

    .analyze-btn {
      background: #3498db;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background 0.3s;

      &:hover:not(:disabled) {
        background: #2980b9;
      }

      &:disabled {
        background: #bdc3c7;
        cursor: not-allowed;
      }
    }
  }

  .loading-state {
    text-align: center;
    padding: 40px;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px;
    }

    .loading-progress {
      width: 300px;
      height: 8px;
      background: #f3f3f3;
      border-radius: 4px;
      margin: 0 auto 15px;
      overflow: hidden;

      .progress-bar {
        height: 100%;
        background: #3498db;
        transition: width 0.3s ease;
      }
    }

    .loading-message {
      color: #7f8c8d;
      font-size: 14px;
    }
  }

  .analysis-results {
    .score-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px;
      border-radius: 12px;
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      gap: 30px;

      .score-main {
        text-align: center;

        .score-value {
          font-size: 48px;
          font-weight: bold;
          line-height: 1;

          &.excellent { color: #2ecc71; }
          &.good { color: #f39c12; }
          &.neutral { color: #e74c3c; }
          &.poor { color: #c0392b; }
        }

        .score-label {
          font-size: 14px;
          opacity: 0.9;
        }
      }

      .score-details {
        flex: 1;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;

        .detail-item {
          .label {
            display: block;
            font-size: 12px;
            opacity: 0.8;
            margin-bottom: 5px;
          }

          .value {
            font-size: 16px;
            font-weight: 500;

            &.buy { color: #2ecc71; }
            &.sell { color: #e74c3c; }
            &.hold { color: #f39c12; }
            &.low-risk { color: #2ecc71; }
            &.medium-risk { color: #f39c12; }
            &.high-risk { color: #e67e22; }
            &.extreme-risk { color: #e74c3c; }
          }
        }
      }
    }

    .analysis-tabs {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);

      .tab-headers {
        display: flex;
        border-bottom: 1px solid #eee;

        .tab-header {
          flex: 1;
          padding: 15px;
          background: none;
          border: none;
          cursor: pointer;
          font-size: 14px;
          color: #7f8c8d;
          transition: all 0.3s;

          &.active {
            color: #3498db;
            border-bottom: 2px solid #3498db;
            background: #f8f9fa;
          }

          &:hover:not(.active) {
            background: #f8f9fa;
          }
        }
      }

      .tab-content {
        padding: 20px;

        h4 {
          margin-bottom: 20px;
          color: #2c3e50;
        }

        .indicators-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 15px;

          .indicator-card {
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 15px;

            .indicator-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 10px;

              h5 {
                margin: 0;
                color: #2c3e50;
              }

              .indicator-signal {
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;

                &.signal-buy {
                  background: #d5f4e6;
                  color: #27ae60;
                }

                &.signal-sell {
                  background: #fadbd8;
                  color: #e74c3c;
                }

                &.signal-hold {
                  background: #fef9e7;
                  color: #f39c12;
                }
              }
            }

            .indicator-details {
              .detail-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;

                .strength-bar {
                  flex: 1;
                  height: 6px;
                  background: #f3f3f3;
                  border-radius: 3px;
                  margin: 0 10px;
                  overflow: hidden;

                  .strength-fill {
                    height: 100%;
                    background: #3498db;
                    transition: width 0.3s ease;
                  }
                }
              }
            }
          }
        }

        .trends-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 15px;

          .trend-card {
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 15px;

            &.overall {
              border-color: #3498db;
              background: #f8f9fa;
            }

            h5 {
              margin: 0 0 10px 0;
              color: #2c3e50;
            }

            .trend-direction {
              font-size: 18px;
              font-weight: 600;
              margin-bottom: 8px;

              &.trend-up { color: #27ae60; }
              &.trend-down { color: #e74c3c; }
              &.trend-sideways { color: #f39c12; }
            }

            .trend-strength, .trend-angle, .trend-reliability {
              font-size: 14px;
              color: #7f8c8d;
              margin-bottom: 4px;
            }
          }
        }

        .volume-stats {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 15px;

          .stat-card {
            text-align: center;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 8px;

            h5 {
              margin: 0 0 10px 0;
              color: #2c3e50;
              font-size: 14px;
            }

            .stat-value {
              font-size: 18px;
              font-weight: 600;

              &.volume-increasing { color: #27ae60; }
              &.volume-decreasing { color: #e74c3c; }
              &.volume-stable { color: #f39c12; }
              &.relation-positive { color: #27ae60; }
              &.relation-negative { color: #e74c3c; }
              &.relation-neutral { color: #f39c12; }
            }
          }
        }

        .momentum-stats {
          .momentum-chart {
            margin-bottom: 20px;

            .chart-item {
              display: flex;
              align-items: center;
              margin-bottom: 15px;

              .chart-label {
                width: 120px;
                font-size: 14px;
                color: #2c3e50;
              }

              .chart-bar {
                flex: 1;
                height: 20px;
                background: #f3f3f3;
                border-radius: 10px;
                margin: 0 15px;
                overflow: hidden;

                .chart-fill {
                  height: 100%;
                  transition: width 0.3s ease;

                  &.momentum-positive {
                    background: linear-gradient(90deg, #27ae60, #2ecc71);
                  }

                  &.momentum-negative {
                    background: linear-gradient(90deg, #e74c3c, #c0392b);
                  }
                }
              }

              .chart-value {
                width: 80px;
                text-align: right;
                font-weight: 500;
              }
            }
          }

          .momentum-summary {
            border-top: 1px solid #eee;
            padding-top: 15px;

            .summary-item {
              display: flex;
              justify-content: space-between;
              margin-bottom: 8px;

              .label {
                color: #7f8c8d;
              }

              .value {
                font-weight: 500;

                &.direction-up { color: #27ae60; }
                &.direction-down { color: #e74c3c; }
                &.direction-neutral { color: #f39c12; }
                &.warning { color: #e67e22; }
              }
            }
          }
        }

        .volatility-stats {
          display: flex;
          gap: 30px;
          align-items: center;

          .volatility-gauge {
            .gauge-container {
              width: 120px;
              height: 120px;
              border: 8px solid #3498db;
              border-radius: 50%;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              position: relative;

              .gauge-value {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
              }

              .gauge-label {
                font-size: 12px;
                color: #7f8c8d;
              }
            }
          }

          .volatility-details {
            flex: 1;

            .detail-item {
              display: flex;
              justify-content: space-between;
              margin-bottom: 10px;

              .label {
                color: #7f8c8d;
              }

              .value {
                font-weight: 500;

                &.vol-increasing { color: #e74c3c; }
                &.vol-decreasing { color: #27ae60; }
                &.vol-stable { color: #f39c12; }
                &.vol-low { color: #27ae60; }
                &.vol-normal { color: #3498db; }
                &.vol-high { color: #f39c12; }
                &.vol-extreme { color: #e74c3c; }
              }
            }
          }
        }

        .support-list {
          .support-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 8px;
            margin-bottom: 10px;

            &.support {
              border-left: 4px solid #27ae60;
            }

            &.resistance {
              border-left: 4px solid #e74c3c;
            }

            .level-info {
              .level-price {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
              }

              .level-type {
                font-size: 12px;
                color: #7f8c8d;
              }
            }

            .level-details {
              flex: 1;
              margin-left: 20px;

              .detail-row {
                display: flex;
                align-items: center;
                margin-bottom: 5px;
                font-size: 14px;

                span:first-child {
                  width: 80px;
                  color: #7f8c8d;
                }

                .strength-bar {
                  width: 100px;
                  height: 6px;
                  background: #f3f3f3;
                  border-radius: 3px;
                  margin: 0 10px;
                  overflow: hidden;

                  .strength-fill {
                    height: 100%;
                    background: #3498db;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .error-state {
    text-align: center;
    padding: 40px;
    color: #e74c3c;

    .error-icon {
      font-size: 48px;
      margin-bottom: 15px;
    }

    .error-message {
      font-size: 16px;
      margin-bottom: 20px;
    }

    .retry-btn {
      background: #e74c3c;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      transition: background 0.3s;

      &:hover {
        background: #c0392b;
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 