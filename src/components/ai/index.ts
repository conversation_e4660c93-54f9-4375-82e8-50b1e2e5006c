// AI分析组件
export { default as StockAnalyzer } from './analysis/StockAnalyzer.vue'
export { default as TechnicalAnalyzer } from './analysis/TechnicalAnalyzer.vue'
export { default as TrendPredictor } from './analysis/TrendPredictor.vue'
export { default as RiskAssessment } from './analysis/RiskAssessment.vue'

// AI推荐组件
// export { default as StockRecommender } from './recommendation/StockRecommender.vue'
// export { default as PortfolioOptimizer } from './recommendation/PortfolioOptimizer.vue'

// AI自然语言处理组件
// export { default as QueryParser } from './nlp/QueryParser.vue'
// export { default as ChatInterface } from './nlp/ChatInterface.vue'

// AI可视化组件
// export { default as ModelExplainer } from './visualization/ModelExplainer.vue'
// export { default as PredictionChart } from './visualization/PredictionChart.vue'

// AI服务组件
// export { default as AIMonitor } from './services/AIMonitor.vue'

// 导出AI相关类型
export * from '../../types/ai'

// 导出AI服务
export * from '../../services/ai/AIService'
export * from '../../services/ai/StockAnalyzer'
export * from '../../services/ai/TechnicalAnalysisAI'
export * from '../../services/ai/TrendPredictionAI'
export * from '../../services/ai/RiskAssessmentAI'
export * from '../../services/ai/FeatureEngineering'
export * from '../../services/ai/DataPipeline'
export * from '../../services/ai/ModelTrainingManager'

// 导出AI组合式API
export * from '../../composables/ai/useAI'
export * from '../../composables/ai/useStockAI'
export * from '../../composables/ai/useDataPipeline'
export * from '../../composables/ai/useTrendPrediction'
export * from '../../composables/ai/useRiskAssessment'