<template>
  <div class="form-demo">
    <div class="demo-header">
      <h1>表单组件演示</h1>
      <p>展示动态表单生成器的各种功能和布局模式</p>
    </div>

    <el-tabs v-model="activeTab" type="border-card" class="demo-tabs">
      <!-- 基础表单 -->
      <el-tab-pane label="基础表单" name="basic">
        <div class="demo-section">
          <h3>用户信息表单</h3>
          <p>展示基础的表单字段类型和验证功能</p>
          
          <FormBuilder
            :config="userInfoConfig"
            v-model="userInfoData"
            @submit="handleUserInfoSubmit"
            @change="handleFormChange"
          />
          
          <div class="demo-data">
            <h4>表单数据：</h4>
            <pre>{{ JSON.stringify(userInfoData, null, 2) }}</pre>
          </div>
        </div>
      </el-tab-pane>

      <!-- 分步表单 -->
      <el-tab-pane label="分步表单" name="steps">
        <div class="demo-section">
          <h3>策略配置表单</h3>
          <p>展示分步表单的功能，包括步骤验证和导航</p>
          
          <FormBuilder
            :config="strategyConfig"
            v-model="strategyData"
            @submit="handleStrategySubmit"
            @change="handleFormChange"
          />
          
          <div class="demo-data">
            <h4>表单数据：</h4>
            <pre>{{ JSON.stringify(strategyData, null, 2) }}</pre>
          </div>
        </div>
      </el-tab-pane>

      <!-- 分组表单 -->
      <el-tab-pane label="分组表单" name="groups">
        <div class="demo-section">
          <h3>股票筛选表单</h3>
          <p>展示分组表单的功能，包括可折叠的分组</p>
          
          <FormBuilder
            :config="stockFilterConfig"
            v-model="stockFilterData"
            @submit="handleStockFilterSubmit"
            @change="handleFormChange"
          />
          
          <div class="demo-data">
            <h4>表单数据：</h4>
            <pre>{{ JSON.stringify(stockFilterData, null, 2) }}</pre>
          </div>
        </div>
      </el-tab-pane>

      <!-- 动态表单 -->
      <el-tab-pane label="动态表单" name="dynamic">
        <div class="demo-section">
          <h3>动态字段表单</h3>
          <p>展示条件渲染和字段联动的功能</p>
          
          <FormBuilder
            :config="dynamicFormConfig"
            v-model="dynamicFormData"
            @submit="handleDynamicFormSubmit"
            @change="handleFormChange"
          />
          
          <div class="demo-data">
            <h4>表单数据：</h4>
            <pre>{{ JSON.stringify(dynamicFormData, null, 2) }}</pre>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  FormBuilder, 
  FORM_TEMPLATES, 
  createFormConfig,
  createFormField,
  type FormConfig
} from '@/components/form'

// 响应式状态
const activeTab = ref('basic')

// 表单数据
const userInfoData = ref({})
const strategyData = ref({})
const stockFilterData = ref({})
const dynamicFormData = ref({})

// 表单配置
const userInfoConfig = FORM_TEMPLATES.userInfo || createFormConfig({
  fields: [
    createFormField({
      key: 'name',
      type: 'input',
      label: '姓名',
      required: true,
      placeholder: '请输入姓名'
    }),
    createFormField({
      key: 'email',
      type: 'input',
      label: '邮箱',
      required: true,
      placeholder: '请输入邮箱地址',
      rules: [
        { type: 'email', message: '请输入有效的邮箱地址' }
      ]
    }),
    createFormField({
      key: 'phone',
      type: 'input',
      label: '手机号',
      placeholder: '请输入手机号码'
    }),
    createFormField({
      key: 'age',
      type: 'number',
      label: '年龄',
      config: { min: 18, max: 100 }
    }),
    createFormField({
      key: 'gender',
      type: 'radio',
      label: '性别',
      options: [
        { label: '男', value: 'male' },
        { label: '女', value: 'female' }
      ]
    })
  ],
  layout: {
    type: 'horizontal',
    labelWidth: 120,
    columns: 2,
    gutter: 16
  }
})

const strategyConfig = FORM_TEMPLATES.strategyConfig || createFormConfig({
  fields: [
    createFormField({
      key: 'strategyName',
      type: 'input',
      label: '策略名称',
      required: true,
      step: 1,
      placeholder: '请输入策略名称'
    }),
    createFormField({
      key: 'description',
      type: 'textarea',
      label: '策略描述',
      step: 1,
      placeholder: '请描述策略的基本思路'
    }),
    createFormField({
      key: 'riskLevel',
      type: 'select',
      label: '风险等级',
      required: true,
      step: 2,
      options: [
        { label: '低风险', value: 'low' },
        { label: '中风险', value: 'medium' },
        { label: '高风险', value: 'high' }
      ]
    }),
    createFormField({
      key: 'maxDrawdown',
      type: 'slider',
      label: '最大回撤',
      step: 2,
      config: { min: 0, max: 50, step: 1 }
    })
  ],
  layout: {
    type: 'horizontal',
    labelWidth: 120,
    steps: [
      { 
        key: 'basic', 
        title: '基础信息', 
        description: '配置策略基本信息',
        fields: ['strategyName', 'description'],
        validation: true
      },
      { 
        key: 'risk', 
        title: '风险配置', 
        description: '设置风险控制参数',
        fields: ['riskLevel', 'maxDrawdown'],
        validation: true
      }
    ]
  }
})

const stockFilterConfig = FORM_TEMPLATES.stockFilter || createFormConfig({
  fields: [
    createFormField({
      key: 'exchange',
      type: 'select',
      label: '交易所',
      group: 'basic',
      options: [
        { label: '上海证券交易所', value: 'SSE' },
        { label: '深圳证券交易所', value: 'SZSE' },
        { label: '北京证券交易所', value: 'BSE' }
      ]
    }),
    createFormField({
      key: 'industry',
      type: 'select',
      label: '行业',
      group: 'basic',
      multiple: true,
      options: [
        { label: '科技', value: 'tech' },
        { label: '金融', value: 'finance' },
        { label: '制造业', value: 'manufacturing' },
        { label: '医疗', value: 'healthcare' }
      ]
    }),
    createFormField({
      key: 'priceRange',
      type: 'slider',
      label: '价格区间',
      group: 'filter',
      config: { range: true, min: 0, max: 100, step: 0.1 }
    }),
    createFormField({
      key: 'marketCap',
      type: 'slider',
      label: '市值区间',
      group: 'filter',
      config: { range: true, min: 0, max: 10000, step: 100 }
    })
  ],
  layout: {
    type: 'horizontal',
    labelWidth: 120,
    groups: [
      { 
        key: 'basic', 
        title: '基础筛选', 
        description: '选择交易所和行业',
        fields: ['exchange', 'industry'],
        collapsible: true
      },
      { 
        key: 'filter', 
        title: '数值筛选', 
        description: '设置价格和市值范围',
        fields: ['priceRange', 'marketCap'],
        collapsible: true
      }
    ]
  }
})

const dynamicFormConfig = createFormConfig({
  fields: [
    createFormField({
      key: 'userType',
      type: 'radio',
      label: '用户类型',
      required: true,
      options: [
        { label: '个人用户', value: 'individual' },
        { label: '企业用户', value: 'enterprise' }
      ]
    }),
    
    // 个人用户字段
    createFormField({
      key: 'personalName',
      type: 'input',
      label: '姓名',
      required: true,
      visible: (formData) => formData.userType === 'individual',
      dependencies: ['userType']
    }),
    createFormField({
      key: 'idCard',
      type: 'input',
      label: '身份证号',
      visible: (formData) => formData.userType === 'individual',
      dependencies: ['userType']
    }),
    
    // 企业用户字段
    createFormField({
      key: 'enterpriseName',
      type: 'input',
      label: '企业名称',
      required: true,
      visible: (formData) => formData.userType === 'enterprise',
      dependencies: ['userType']
    }),
    createFormField({
      key: 'businessLicense',
      type: 'input',
      label: '营业执照号',
      visible: (formData) => formData.userType === 'enterprise',
      dependencies: ['userType']
    }),
    
    // 共同字段
    createFormField({
      key: 'email',
      type: 'input',
      label: '邮箱',
      required: true,
      rules: [
        { type: 'email', message: '请输入有效的邮箱地址' }
      ]
    }),
    createFormField({
      key: 'phone',
      type: 'input',
      label: '手机号',
      required: true
    })
  ],
  layout: {
    type: 'horizontal',
    labelWidth: 120,
    columns: 1,
    gutter: 16
  }
})

// 事件处理
const handleFormChange = (field: string, value: any, formData: Record<string, any>) => {
  console.log('表单字段变化:', { field, value, formData })
}

const handleUserInfoSubmit = (formData: Record<string, any>) => {
  ElMessage.success('用户信息提交成功')
  console.log('用户信息:', formData)
}

const handleStrategySubmit = (formData: Record<string, any>) => {
  ElMessage.success('策略配置提交成功')
  console.log('策略配置:', formData)
}

const handleStockFilterSubmit = (formData: Record<string, any>) => {
  ElMessage.success('股票筛选条件提交成功')
  console.log('筛选条件:', formData)
}

const handleDynamicFormSubmit = (formData: Record<string, any>) => {
  ElMessage.success('动态表单提交成功')
  console.log('动态表单数据:', formData)
}
</script>

<style scoped>
.form-demo {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 32px;
}

.demo-header h1 {
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
}

.demo-header p {
  margin: 0;
  color: var(--el-text-color-regular);
}

.demo-tabs {
  margin-bottom: 24px;
}

.demo-section {
  padding: 24px;
}

.demo-section h3 {
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
}

.demo-section p {
  margin: 0 0 24px 0;
  color: var(--el-text-color-regular);
}

.demo-data {
  margin-top: 32px;
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
}

.demo-data h4 {
  margin: 0 0 12px 0;
  color: var(--el-text-color-primary);
}

.demo-data pre {
  margin: 0;
  padding: 12px;
  background: var(--el-fill-color-light);
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-demo {
    padding: 16px;
  }
}
</style> 