<template>
  <div class="ai-analysis-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>AI智能分析引擎</h1>
        <p class="page-description">
          基于深度学习的股票智能分析系统，提供技术分析、趋势预测、风险评估等全方位AI服务
        </p>
      </div>
      
      <div class="header-stats">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ totalAnalysis }}</div>
              <div class="stat-label">总分析次数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ activeModels }}</div>
              <div class="stat-label">活跃模型</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ averageAccuracy }}%</div>
              <div class="stat-label">平均准确率</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ processingTime }}ms</div>
              <div class="stat-label">平均响应时间</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 功能导航 -->
    <div class="function-nav">
      <el-tabs v-model="activeFunction" type="border-card" @tab-change="onFunctionChange">
        <el-tab-pane label="股票分析" name="stock">
          <template #label>
            <i class="el-icon-data-analysis"></i>
            <span>股票分析</span>
          </template>
        </el-tab-pane>
        
        <el-tab-pane label="技术分析" name="technical">
          <template #label>
            <i class="el-icon-trend-charts"></i>
            <span>技术分析</span>
          </template>
        </el-tab-pane>
        
        <el-tab-pane label="趋势预测" name="prediction">
          <template #label>
            <i class="el-icon-magic-stick"></i>
            <span>趋势预测</span>
          </template>
        </el-tab-pane>
        
        <el-tab-pane label="风险评估" name="risk">
          <template #label>
            <i class="el-icon-warning-outline"></i>
            <span>风险评估</span>
          </template>
        </el-tab-pane>
        
        <el-tab-pane label="综合分析" name="comprehensive">
          <template #label>
            <i class="el-icon-pie-chart"></i>
            <span>综合分析</span>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 功能内容区域 -->
    <div class="function-content">
      <!-- 股票分析 -->
      <div v-if="activeFunction === 'stock'" class="analysis-section">
        <StockAnalyzer />
      </div>

      <!-- 技术分析 -->
      <div v-if="activeFunction === 'technical'" class="analysis-section">
        <TechnicalAnalyzer />
      </div>

      <!-- 趋势预测 -->
      <div v-if="activeFunction === 'prediction'" class="analysis-section">
        <TrendPredictor />
      </div>

      <!-- 风险评估 -->
      <div v-if="activeFunction === 'risk'" class="analysis-section">
        <RiskAssessment />
      </div>

      <!-- 综合分析 -->
      <div v-if="activeFunction === 'comprehensive'" class="analysis-section">
        <div class="comprehensive-analysis">
          <div class="analysis-controls">
            <h3>综合分析控制台</h3>
            <div class="controls-row">
              <el-select
                v-model="selectedSymbol"
                placeholder="选择股票"
                @change="onSymbolChange"
                style="width: 200px"
              >
                <el-option
                  v-for="symbol in popularSymbols"
                  :key="symbol"
                  :label="symbol"
                  :value="symbol"
                />
              </el-select>
              
              <el-button
                type="primary"
                @click="runComprehensiveAnalysis"
                :loading="comprehensiveLoading"
                :disabled="!selectedSymbol"
              >
                开始综合分析
              </el-button>
              
              <el-button
                type="success"
                @click="exportAnalysisReport"
                :disabled="!comprehensiveResult"
              >
                导出报告
              </el-button>
            </div>
          </div>

          <!-- 综合分析结果 -->
          <div v-if="comprehensiveResult" class="comprehensive-result">
            <el-row :gutter="20">
              <!-- 分析概览 -->
              <el-col :span="8">
                <el-card class="result-card">
                  <div slot="header">分析概览</div>
                  <div class="overview-content">
                    <div class="overview-item">
                      <span class="label">综合评分:</span>
                      <span :class="getScoreClass(comprehensiveResult.overallScore)">
                        {{ comprehensiveResult.overallScore.toFixed(1) }}/10
                      </span>
                    </div>
                    <div class="overview-item">
                      <span class="label">投资建议:</span>
                      <span :class="getRecommendationClass(comprehensiveResult.recommendation)">
                        {{ comprehensiveResult.recommendation }}
                      </span>
                    </div>
                    <div class="overview-item">
                      <span class="label">置信度:</span>
                      <span class="confidence">
                        {{ (comprehensiveResult.confidence * 100).toFixed(1) }}%
                      </span>
                    </div>
                    <div class="overview-item">
                      <span class="label">风险等级:</span>
                      <span :class="getRiskClass(comprehensiveResult.riskLevel)">
                        {{ getRiskLabel(comprehensiveResult.riskLevel) }}
                      </span>
                    </div>
                  </div>
                </el-card>
              </el-col>
              
              <!-- 分析维度 -->
              <el-col :span="16">
                <el-card class="result-card">
                  <div slot="header">分析维度评分</div>
                  <div class="dimensions-chart">
                    <div ref="dimensionsChartRef" class="chart-container"></div>
                  </div>
                </el-card>
              </el-col>
            </el-row>

            <!-- 详细分析结果 -->
            <div class="detailed-results">
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-card class="analysis-card">
                    <div slot="header">
                      <i class="el-icon-data-analysis"></i>
                      股票分析
                    </div>
                    <div class="analysis-summary">
                      <div class="score">{{ comprehensiveResult.stockAnalysis.score.toFixed(1) }}</div>
                      <div class="trend">{{ comprehensiveResult.stockAnalysis.trend }}</div>
                      <div class="key-points">
                        <div
                          v-for="point in comprehensiveResult.stockAnalysis.keyPoints"
                          :key="point"
                          class="point-item"
                        >
                          {{ point }}
                        </div>
                      </div>
                    </div>
                  </el-card>
                </el-col>
                
                <el-col :span="6">
                  <el-card class="analysis-card">
                    <div slot="header">
                      <i class="el-icon-trend-charts"></i>
                      技术分析
                    </div>
                    <div class="analysis-summary">
                      <div class="score">{{ comprehensiveResult.technicalAnalysis.score.toFixed(1) }}</div>
                      <div class="signal">{{ comprehensiveResult.technicalAnalysis.signal }}</div>
                      <div class="indicators">
                        <div
                          v-for="indicator in comprehensiveResult.technicalAnalysis.indicators"
                          :key="indicator.name"
                          class="indicator-item"
                        >
                          <span>{{ indicator.name }}:</span>
                          <span :class="getSignalClass(indicator.signal)">
                            {{ indicator.signal }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </el-card>
                </el-col>
                
                <el-col :span="6">
                  <el-card class="analysis-card">
                    <div slot="header">
                      <i class="el-icon-magic-stick"></i>
                      趋势预测
                    </div>
                    <div class="analysis-summary">
                      <div class="score">{{ comprehensiveResult.trendPrediction.score.toFixed(1) }}</div>
                      <div class="direction">{{ comprehensiveResult.trendPrediction.direction }}</div>
                      <div class="targets">
                        <div class="target-item">
                          <span>短期目标:</span>
                          <span>¥{{ comprehensiveResult.trendPrediction.shortTarget.toFixed(2) }}</span>
                        </div>
                        <div class="target-item">
                          <span>中期目标:</span>
                          <span>¥{{ comprehensiveResult.trendPrediction.mediumTarget.toFixed(2) }}</span>
                        </div>
                      </div>
                    </div>
                  </el-card>
                </el-col>
                
                <el-col :span="6">
                  <el-card class="analysis-card">
                    <div slot="header">
                      <i class="el-icon-warning-outline"></i>
                      风险评估
                    </div>
                    <div class="analysis-summary">
                      <div class="score">{{ comprehensiveResult.riskAssessment.score.toFixed(1) }}</div>
                      <div class="level">{{ getRiskLabel(comprehensiveResult.riskAssessment.level) }}</div>
                      <div class="risks">
                        <div
                          v-for="risk in comprehensiveResult.riskAssessment.mainRisks"
                          :key="risk"
                          class="risk-item"
                        >
                          {{ risk }}
                        </div>
                      </div>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </div>

            <!-- 分析时间线 -->
            <div class="analysis-timeline">
              <el-card>
                <div slot="header">分析执行时间线</div>
                <el-timeline>
                  <el-timeline-item
                    v-for="step in comprehensiveResult.timeline"
                    :key="step.name"
                    :timestamp="step.timestamp"
                    :type="step.status === 'completed' ? 'success' : 'primary'"
                  >
                    <div class="timeline-content">
                      <h4>{{ step.name }}</h4>
                      <p>{{ step.description }}</p>
                      <div class="step-stats">
                        <span>耗时: {{ step.duration }}ms</span>
                        <span v-if="step.accuracy">准确率: {{ step.accuracy }}%</span>
                      </div>
                    </div>
                  </el-timeline-item>
                </el-timeline>
              </el-card>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="comprehensiveLoading" class="comprehensive-loading">
            <div class="loading-steps">
              <div
                v-for="(step, index) in analysisSteps"
                :key="step.name"
                class="loading-step"
                :class="{ active: index === currentStep, completed: index < currentStep }"
              >
                <div class="step-icon">
                  <i v-if="index < currentStep" class="el-icon-check"></i>
                  <i v-else-if="index === currentStep" class="el-icon-loading"></i>
                  <span v-else>{{ index + 1 }}</span>
                </div>
                <div class="step-info">
                  <div class="step-name">{{ step.name }}</div>
                  <div class="step-description">{{ step.description }}</div>
                </div>
              </div>
            </div>
            
            <div class="overall-progress">
              <el-progress
                :percentage="overallProgress"
                :format="formatProgress"
                status="success"
              />
              <p class="progress-message">{{ progressMessage }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { 
  StockAnalyzer, 
  TechnicalAnalyzer, 
  TrendPredictor, 
  RiskAssessment 
} from '@/components/ai'
import { 
  useStockAI, 
  useTrendPrediction, 
  useRiskAssessment 
} from '@/composables/ai'
import type { RiskLevel } from '@/types/ai'

// 组合式API
const { analyzeStock } = useStockAI()
const { generatePrediction } = useTrendPrediction()
const { performAssessment } = useRiskAssessment()

// 响应式数据
const activeFunction = ref('stock')
const selectedSymbol = ref('')
const comprehensiveLoading = ref(false)
const comprehensiveResult = ref(null)
const currentStep = ref(0)
const overallProgress = ref(0)
const progressMessage = ref('')

// 图表引用
const dimensionsChartRef = ref<HTMLElement>()

// 统计数据
const totalAnalysis = ref(1247)
const activeModels = ref(8)
const averageAccuracy = ref(87.3)
const processingTime = ref(342)

// 热门股票
const popularSymbols = [
  '000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '600519.SH',
  '000858.SZ', '002415.SZ', '300059.SZ', '600276.SH', '000725.SZ'
]

// 分析步骤
const analysisSteps = [
  {
    name: '数据收集',
    description: '获取股票历史数据和实时信息'
  },
  {
    name: '股票分析',
    description: '执行基本面和技术面综合分析'
  },
  {
    name: '技术分析',
    description: '计算技术指标和信号'
  },
  {
    name: '趋势预测',
    description: '运行趋势预测模型'
  },
  {
    name: '风险评估',
    description: '评估各类风险因素'
  },
  {
    name: '结果整合',
    description: '整合分析结果并生成报告'
  }
]

// 方法
const onFunctionChange = (name: string) => {
  console.log('切换到功能:', name)
}

const onSymbolChange = () => {
  comprehensiveResult.value = null
}

const runComprehensiveAnalysis = async () => {
  if (!selectedSymbol.value) {
    ElMessage.warning('请先选择股票')
    return
  }

  comprehensiveLoading.value = true
  currentStep.value = 0
  overallProgress.value = 0
  progressMessage.value = '正在启动综合分析...'

  try {
    // 模拟分析步骤
    for (let i = 0; i < analysisSteps.length; i++) {
      currentStep.value = i
      progressMessage.value = analysisSteps[i].description
      
      // 模拟处理时间
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000))
      
      overallProgress.value = ((i + 1) / analysisSteps.length) * 100
    }

    // 执行实际分析（并行）
    const [stockResult, predictionResult, riskResult] = await Promise.all([
      analyzeStock(selectedSymbol.value),
      generatePrediction(selectedSymbol.value, 'medium'),
      performAssessment(selectedSymbol.value, 'comprehensive')
    ])

    // 生成综合结果
    const result = {
      symbol: selectedSymbol.value,
      timestamp: Date.now(),
      overallScore: (Math.random() * 3 + 7), // 7-10分
      recommendation: getRandomRecommendation(),
      confidence: 0.75 + Math.random() * 0.2,
      riskLevel: getRandomRiskLevel(),
      
      // 各维度分析
      stockAnalysis: {
        score: Math.random() * 3 + 7,
        trend: '上升趋势',
        keyPoints: [
          '基本面表现良好',
          '盈利能力稳定增长',
          '估值处于合理区间'
        ]
      },
      
      technicalAnalysis: {
        score: Math.random() * 3 + 7,
        signal: '买入',
        indicators: [
          { name: 'MA', signal: '买入' },
          { name: 'RSI', signal: '中性' },
          { name: 'MACD', signal: '买入' }
        ]
      },
      
      trendPrediction: {
        score: Math.random() * 3 + 7,
        direction: '看涨',
        shortTarget: 45.6,
        mediumTarget: 52.3
      },
      
      riskAssessment: {
        score: Math.random() * 3 + 7,
        level: 'medium' as RiskLevel,
        mainRisks: [
          '市场波动风险',
          '行业周期风险'
        ]
      },
      
      // 分析时间线
      timeline: analysisSteps.map((step, index) => ({
        name: step.name,
        description: step.description,
        timestamp: new Date(Date.now() - (analysisSteps.length - index) * 1000).toLocaleTimeString(),
        duration: Math.floor(Math.random() * 500 + 200),
        accuracy: Math.floor(Math.random() * 10 + 85),
        status: 'completed'
      }))
    }

    comprehensiveResult.value = result
    
    // 渲染图表
    await nextTick()
    renderDimensionsChart()
    
    ElMessage.success('综合分析完成')
    
  } catch (error) {
    ElMessage.error('综合分析失败: ' + error.message)
  } finally {
    comprehensiveLoading.value = false
  }
}

const exportAnalysisReport = () => {
  if (!comprehensiveResult.value) return
  
  // 生成报告内容
  const report = {
    title: `${comprehensiveResult.value.symbol} 股票综合分析报告`,
    date: new Date().toLocaleDateString(),
    ...comprehensiveResult.value
  }
  
  // 导出为JSON文件
  const dataStr = JSON.stringify(report, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)
  
  const link = document.createElement('a')
  link.href = url
  link.download = `${comprehensiveResult.value.symbol}_analysis_report.json`
  link.click()
  
  URL.revokeObjectURL(url)
  ElMessage.success('分析报告已导出')
}

const renderDimensionsChart = () => {
  if (!dimensionsChartRef.value || !comprehensiveResult.value) return

  const chart = echarts.init(dimensionsChartRef.value)
  
  const data = [
    { name: '股票分析', value: comprehensiveResult.value.stockAnalysis.score },
    { name: '技术分析', value: comprehensiveResult.value.technicalAnalysis.score },
    { name: '趋势预测', value: comprehensiveResult.value.trendPrediction.score },
    { name: '风险评估', value: comprehensiveResult.value.riskAssessment.score }
  ]

  const option = {
    title: {
      text: '分析维度评分',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    radar: {
      indicator: data.map(item => ({
        name: item.name,
        max: 10
      })),
      radius: '60%'
    },
    series: [{
      type: 'radar',
      data: [{
        value: data.map(item => item.value),
        name: '评分',
        itemStyle: {
          color: '#409EFF'
        },
        areaStyle: {
          opacity: 0.3
        }
      }]
    }]
  }

  chart.setOption(option)
}

// 样式类方法
const getScoreClass = (score: number) => {
  if (score >= 8) return 'score-excellent'
  if (score >= 6) return 'score-good'
  return 'score-poor'
}

const getRecommendationClass = (recommendation: string) => {
  const classes = {
    '强烈买入': 'recommendation-strong-buy',
    '买入': 'recommendation-buy',
    '持有': 'recommendation-hold',
    '卖出': 'recommendation-sell',
    '强烈卖出': 'recommendation-strong-sell'
  }
  return classes[recommendation] || 'recommendation-neutral'
}

const getRiskClass = (level: RiskLevel) => {
  const classes = {
    'low': 'risk-low',
    'medium': 'risk-medium',
    'high': 'risk-high',
    'very_high': 'risk-very-high'
  }
  return classes[level]
}

const getRiskLabel = (level: RiskLevel) => {
  const labels = {
    'low': '低风险',
    'medium': '中风险',
    'high': '高风险',
    'very_high': '极高风险'
  }
  return labels[level]
}

const getSignalClass = (signal: string) => {
  const classes = {
    '买入': 'signal-buy',
    '卖出': 'signal-sell',
    '持有': 'signal-hold',
    '中性': 'signal-neutral'
  }
  return classes[signal] || 'signal-neutral'
}

const formatProgress = (percentage: number) => {
  return `${percentage.toFixed(0)}%`
}

// 工具函数
function getRandomRecommendation() {
  const recommendations = ['强烈买入', '买入', '持有', '卖出', '强烈卖出']
  return recommendations[Math.floor(Math.random() * recommendations.length)]
}

function getRandomRiskLevel(): RiskLevel {
  const levels = ['low', 'medium', 'high', 'very_high'] as const
  return levels[Math.floor(Math.random() * levels.length)]
}

onMounted(() => {
  // 默认选择第一个股票
  if (popularSymbols.length > 0) {
    selectedSymbol.value = popularSymbols[0]
  }
})
</script>

<style scoped lang="scss">
.ai-analysis-page {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;

  .page-header {
    margin-bottom: 30px;

    .header-content {
      text-align: center;
      margin-bottom: 30px;

      h1 {
        font-size: 32px;
        color: #2c3e50;
        margin-bottom: 10px;
      }

      .page-description {
        font-size: 16px;
        color: #666;
        max-width: 800px;
        margin: 0 auto;
        line-height: 1.6;
      }
    }

    .header-stats {
      .stat-card {
        text-align: center;
        padding: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);

        .stat-value {
          font-size: 28px;
          font-weight: bold;
          color: #409EFF;
          margin-bottom: 5px;
        }

        .stat-label {
          font-size: 14px;
          color: #666;
        }
      }
    }
  }

  .function-nav {
    margin-bottom: 20px;

    .el-tabs {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
  }

  .function-content {
    .analysis-section {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      overflow: hidden;
    }

    .comprehensive-analysis {
      padding: 20px;

      .analysis-controls {
        margin-bottom: 30px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;

        h3 {
          margin-bottom: 15px;
          color: #2c3e50;
        }

        .controls-row {
          display: flex;
          gap: 15px;
          align-items: center;
        }
      }

      .comprehensive-result {
        .result-card {
          margin-bottom: 20px;

          .overview-content {
            .overview-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 10px 0;
              border-bottom: 1px solid #f0f0f0;

              &:last-child {
                border-bottom: none;
              }

              .label {
                font-weight: 600;
                color: #2c3e50;
              }

              .confidence {
                color: #409EFF;
                font-weight: bold;
              }
            }
          }

          .dimensions-chart {
            .chart-container {
              height: 300px;
              width: 100%;
            }
          }
        }

        .detailed-results {
          margin: 30px 0;

          .analysis-card {
            height: 280px;

            .analysis-summary {
              text-align: center;

              .score {
                font-size: 36px;
                font-weight: bold;
                color: #409EFF;
                margin-bottom: 10px;
              }

              .trend, .signal, .direction, .level {
                font-size: 16px;
                font-weight: 600;
                color: #2c3e50;
                margin-bottom: 15px;
              }

              .key-points, .indicators, .targets, .risks {
                text-align: left;

                .point-item, .indicator-item, .target-item, .risk-item {
                  padding: 5px 0;
                  font-size: 14px;
                  color: #666;
                  border-bottom: 1px solid #f5f5f5;

                  &:last-child {
                    border-bottom: none;
                  }
                }

                .indicator-item {
                  display: flex;
                  justify-content: space-between;
                }

                .target-item {
                  display: flex;
                  justify-content: space-between;
                }
              }
            }
          }
        }

        .analysis-timeline {
          margin-top: 30px;

          .timeline-content {
            h4 {
              margin-bottom: 8px;
              color: #2c3e50;
            }

            p {
              margin-bottom: 10px;
              color: #666;
              font-size: 14px;
            }

            .step-stats {
              display: flex;
              gap: 20px;
              font-size: 12px;
              color: #999;
            }
          }
        }
      }

      .comprehensive-loading {
        text-align: center;
        padding: 40px;

        .loading-steps {
          display: flex;
          justify-content: center;
          margin-bottom: 40px;
          flex-wrap: wrap;
          gap: 20px;

          .loading-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            border-radius: 8px;
            background: #f8f9fa;
            min-width: 150px;
            transition: all 0.3s ease;

            &.active {
              background: #e3f2fd;
              border: 2px solid #409EFF;
            }

            &.completed {
              background: #f0f9f0;
              border: 2px solid #67C23A;
            }

            .step-icon {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-bottom: 10px;
              background: white;
              font-size: 18px;
              font-weight: bold;

              .el-icon-loading {
                color: #409EFF;
                animation: rotate 1s linear infinite;
              }

              .el-icon-check {
                color: #67C23A;
              }
            }

            .step-info {
              text-align: center;

              .step-name {
                font-weight: 600;
                color: #2c3e50;
                margin-bottom: 5px;
              }

              .step-description {
                font-size: 12px;
                color: #666;
                line-height: 1.4;
              }
            }
          }
        }

        .overall-progress {
          max-width: 600px;
          margin: 0 auto;

          .progress-message {
            margin-top: 15px;
            color: #666;
            font-size: 14px;
          }
        }
      }
    }
  }

  // 评分样式
  .score-excellent {
    color: #67C23A;
    font-weight: bold;
  }

  .score-good {
    color: #E6A23C;
    font-weight: bold;
  }

  .score-poor {
    color: #F56C6C;
    font-weight: bold;
  }

  // 推荐样式
  .recommendation-strong-buy {
    color: #67C23A;
    font-weight: bold;
  }

  .recommendation-buy {
    color: #85CE61;
    font-weight: bold;
  }

  .recommendation-hold {
    color: #E6A23C;
    font-weight: bold;
  }

  .recommendation-sell {
    color: #F78989;
    font-weight: bold;
  }

  .recommendation-strong-sell {
    color: #F56C6C;
    font-weight: bold;
  }

  // 风险样式
  .risk-low {
    color: #67C23A;
    font-weight: bold;
  }

  .risk-medium {
    color: #E6A23C;
    font-weight: bold;
  }

  .risk-high {
    color: #F56C6C;
    font-weight: bold;
  }

  .risk-very-high {
    color: #C0392B;
    font-weight: bold;
  }

  // 信号样式
  .signal-buy {
    color: #67C23A;
    font-weight: bold;
  }

  .signal-sell {
    color: #F56C6C;
    font-weight: bold;
  }

  .signal-hold, .signal-neutral {
    color: #E6A23C;
    font-weight: bold;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style> 