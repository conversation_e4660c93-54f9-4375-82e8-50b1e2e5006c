<template>
  <div class="ai-demo">
    <!-- 页面标题 -->
    <div class="demo-header">
      <h1>AI智能分析演示</h1>
      <p>体验AI驱动的股票分析、智能推荐和预测功能</p>
    </div>

    <!-- 功能导航 -->
    <el-card class="demo-nav">
      <el-tabs v-model="activeDemo" type="card" @tab-click="onTabClick">
        <el-tab-pane label="智能股票分析" name="stock-analyzer">
          <StockAnalyzer />
        </el-tab-pane>

        <el-tab-pane label="AI推荐系统" name="recommendation">
          <div class="recommendation-demo">
            <h3>AI推荐系统</h3>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-card>
                  <template #header>
                    <h4>股票推荐</h4>
                  </template>
                  <div class="recommendation-controls">
                    <el-button type="primary" @click="getRecommendations" :loading="recLoading">
                      获取推荐
                    </el-button>
                  </div>
                  <div v-if="recommendations.length > 0" class="recommendation-list">
                    <div
                      v-for="rec in recommendations"
                      :key="rec.id"
                      class="recommendation-item"
                    >
                      <div class="rec-header">
                        <span class="rec-title">{{ rec.title }}</span>
                        <el-tag :type="getPriorityType(rec.priority)">
                          {{ rec.priority }}
                        </el-tag>
                      </div>
                      <div class="rec-description">{{ rec.description }}</div>
                      <div class="rec-metrics">
                        <span>置信度: {{ (rec.confidence * 100).toFixed(1) }}%</span>
                        <span v-if="rec.metrics?.expected_return">
                          预期收益: {{ (rec.metrics.expected_return * 100).toFixed(1) }}%
                        </span>
                      </div>
                      <div class="rec-actions">
                        <el-rate
                          v-model="rec.rating"
                          @change="(value) => rateRecommendation(rec.id, value)"
                          show-text
                          text-color="#ff9900"
                        />
                      </div>
                    </div>
                  </div>
                </el-card>
              </el-col>

              <el-col :span="12">
                <el-card>
                  <template #header>
                    <h4>市场情绪分析</h4>
                  </template>
                  <div class="sentiment-controls">
                    <el-button type="primary" @click="getMarketSentiment" :loading="sentimentLoading">
                      分析市场情绪
                    </el-button>
                  </div>
                  <div v-if="marketSentiment" class="sentiment-display">
                    <div class="sentiment-overall">
                      <div class="sentiment-score" :class="getSentimentClass(marketSentiment.overall_sentiment)">
                        {{ (marketSentiment.overall_sentiment * 100).toFixed(1) }}
                      </div>
                      <div class="sentiment-label">整体情绪指数</div>
                    </div>
                    <div class="sentiment-trend">
                      <el-tag :type="getTrendType(marketSentiment.sentiment_trend)">
                        {{ getTrendText(marketSentiment.sentiment_trend) }}
                      </el-tag>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>

        <el-tab-pane label="AI性能监控" name="monitoring">
          <div class="monitoring-demo">
            <h3>AI模型性能监控</h3>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-card>
                  <template #header>
                    <h4>总体指标</h4>
                  </template>
                  <div class="metric-item">
                    <label>总推理次数</label>
                    <span class="metric-value">{{ totalInferences }}</span>
                  </div>
                  <div class="metric-item">
                    <label>平均延迟</label>
                    <span class="metric-value">{{ averageLatency.toFixed(0) }}ms</span>
                  </div>
                  <div class="metric-item">
                    <label>错误率</label>
                    <span class="metric-value" :class="getErrorRateClass(overallErrorRate)">
                      {{ (overallErrorRate * 100).toFixed(2) }}%
                    </span>
                  </div>
                </el-card>
              </el-col>

              <el-col :span="8">
                <el-card>
                  <template #header>
                    <h4>可用模型</h4>
                  </template>
                  <div v-if="models.length > 0" class="models-list">
                    <div
                      v-for="model in models"
                      :key="model.id"
                      class="model-item"
                    >
                      <div class="model-name">{{ model.name }}</div>
                      <div class="model-type">{{ model.type }}</div>
                      <el-tag size="small">{{ model.version }}</el-tag>
                    </div>
                  </div>
                  <div v-else class="no-models">
                    <el-button @click="loadModels" :loading="loading">
                      加载模型
                    </el-button>
                  </div>
                </el-card>
              </el-col>

              <el-col :span="8">
                <el-card>
                  <template #header>
                    <h4>系统告警</h4>
                  </template>
                  <div v-if="activeAlerts.length > 0" class="alerts-list">
                    <div
                      v-for="alert in activeAlerts.slice(0, 5)"
                      :key="alert.id"
                      class="alert-item"
                      :class="alert.severity"
                    >
                      <div class="alert-message">{{ alert.message }}</div>
                      <div class="alert-time">
                        {{ formatTime(alert.timestamp) }}
                      </div>
                    </div>
                  </div>
                  <div v-else class="no-alerts">
                    <el-icon><SuccessFilled /></el-icon>
                    <span>系统运行正常</span>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>

        <el-tab-pane label="批量分析" name="batch">
          <div class="batch-demo">
            <h3>批量股票分析</h3>
            <el-card>
              <div class="batch-controls">
                <div class="symbol-input">
                  <el-input
                    v-model="batchSymbols"
                    type="textarea"
                    :rows="3"
                    placeholder="输入股票代码，用逗号分隔，例如：AAPL,MSFT,GOOGL,AMZN"
                  />
                </div>
                <div class="batch-actions">
                  <el-button type="primary" @click="startBatchAnalysis" :loading="batchLoading">
                    开始批量分析
                  </el-button>
                  <el-button @click="clearBatchResults">清空结果</el-button>
                </div>
              </div>

              <div v-if="batchResults.length > 0" class="batch-results">
                <h4>分析结果</h4>
                <el-table :data="batchResults" style="width: 100%">
                  <el-table-column prop="symbol" label="股票代码" width="100" />
                  <el-table-column prop="score" label="评分" width="80">
                    <template #default="scope">
                      <span :class="getScoreClass(scope.row.score)">
                        {{ scope.row.score.toFixed(1) }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="grade" label="等级" width="80">
                    <template #default="scope">
                      <el-tag :type="getGradeType(scope.row.grade)">
                        {{ scope.row.grade }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="recommendation" label="建议" width="100">
                    <template #default="scope">
                      <el-tag :type="getRecommendationType(scope.row.recommendation)">
                        {{ getRecommendationText(scope.row.recommendation) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="confidence" label="置信度" width="100">
                    <template #default="scope">
                      {{ (scope.row.confidence * 100).toFixed(1) }}%
                    </template>
                  </el-table-column>
                  <el-table-column label="技术趋势" width="100">
                    <template #default="scope">
                      {{ getTrendText(scope.row.technicalAnalysis.trend) }}
                    </template>
                  </el-table-column>
                  <el-table-column label="风险等级" width="100">
                    <template #default="scope">
                      {{ getRiskText(scope.row.riskAssessment.overall_risk) }}
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="120">
                    <template #default="scope">
                      <el-button size="small" @click="viewDetails(scope.row)">
                        查看详情
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-card>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailsVisible" title="分析详情" width="80%">
      <div v-if="selectedAnalysis" class="analysis-details">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="股票代码">{{ selectedAnalysis.symbol }}</el-descriptions-item>
          <el-descriptions-item label="综合评分">{{ selectedAnalysis.score.toFixed(1) }}</el-descriptions-item>
          <el-descriptions-item label="等级">{{ selectedAnalysis.grade }}</el-descriptions-item>
          <el-descriptions-item label="投资建议">{{ getRecommendationText(selectedAnalysis.recommendation) }}</el-descriptions-item>
          <el-descriptions-item label="置信度">{{ (selectedAnalysis.confidence * 100).toFixed(1) }}%</el-descriptions-item>
          <el-descriptions-item label="分析时间">{{ formatTime(selectedAnalysis.timestamp) }}</el-descriptions-item>
        </el-descriptions>

        <div class="factors-section">
          <h4>关键因子</h4>
          <el-row :gutter="10">
            <el-col
              v-for="factor in selectedAnalysis.factors"
              :key="factor.name"
              :span="12"
            >
              <el-card class="factor-card">
                <div class="factor-info">
                  <span class="factor-name">{{ factor.name }}</span>
                  <span class="factor-value" :class="getImpactClass(factor.impact)">
                    {{ factor.value.toFixed(1) }}
                  </span>
                </div>
                <div class="factor-explanation">{{ factor.explanation }}</div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { SuccessFilled } from '@element-plus/icons-vue'
import { StockAnalyzer } from '@/components/ai'
import { useAI, useStockAI, useAIRecommendation, useAIMonitoring } from '@/composables/ai/useAI'
import { StockAIAnalysis, AIRecommendation, MarketSentiment } from '@/types/ai'

// 组合式API
const { loading, models, loadModels } = useAI()
const { batchAnalyzeStocks, getMarketSentiment: getMarketSentimentData } = useStockAI()
const { recommendations, getStockRecommendations, rateRecommendation } = useAIRecommendation()
const { totalInferences, averageLatency, overallErrorRate, activeAlerts } = useAIMonitoring()

// 响应式数据
const activeDemo = ref('stock-analyzer')
const recLoading = ref(false)
const sentimentLoading = ref(false)
const batchLoading = ref(false)
const marketSentiment = ref<MarketSentiment | null>(null)
const batchSymbols = ref('AAPL,MSFT,GOOGL,AMZN,TSLA')
const batchResults = ref<StockAIAnalysis[]>([])
const detailsVisible = ref(false)
const selectedAnalysis = ref<StockAIAnalysis | null>(null)

// 方法
const onTabClick = (tab: any) => {
  console.log('切换到标签页:', tab.props.name)
}

const getRecommendations = async () => {
  recLoading.value = true
  try {
    await getStockRecommendations()
  } finally {
    recLoading.value = false
  }
}

const getMarketSentiment = async () => {
  sentimentLoading.value = true
  try {
    const sentiment = await getMarketSentimentData()
    marketSentiment.value = sentiment
  } finally {
    sentimentLoading.value = false
  }
}

const startBatchAnalysis = async () => {
  if (!batchSymbols.value.trim()) return
  
  batchLoading.value = true
  try {
    const symbols = batchSymbols.value.split(',').map(s => s.trim()).filter(s => s)
    const results = await batchAnalyzeStocks(symbols, { batchSize: 5 })
    batchResults.value = results
  } finally {
    batchLoading.value = false
  }
}

const clearBatchResults = () => {
  batchResults.value = []
}

const viewDetails = (analysis: StockAIAnalysis) => {
  selectedAnalysis.value = analysis
  detailsVisible.value = true
}

// 样式辅助函数
const getPriorityType = (priority: string) => {
  const types: Record<string, string> = {
    'high': 'danger',
    'medium': 'warning',
    'low': 'info'
  }
  return types[priority] || 'info'
}

const getSentimentClass = (sentiment: number) => {
  if (sentiment > 0.3) return 'positive'
  if (sentiment < -0.3) return 'negative'
  return 'neutral'
}

const getTrendType = (trend: string) => {
  const types: Record<string, string> = {
    'improving': 'success',
    'declining': 'danger',
    'stable': 'info'
  }
  return types[trend] || 'info'
}

const getTrendText = (trend: string) => {
  const texts: Record<string, string> = {
    'improving': '改善',
    'declining': '下降',
    'stable': '稳定',
    'bullish': '看涨',
    'bearish': '看跌',
    'sideways': '震荡'
  }
  return texts[trend] || trend
}

const getErrorRateClass = (rate: number) => {
  if (rate > 0.1) return 'high-error'
  if (rate > 0.05) return 'medium-error'
  return 'low-error'
}

const getScoreClass = (score: number) => {
  if (score >= 80) return 'excellent'
  if (score >= 70) return 'good'
  if (score >= 60) return 'average'
  if (score >= 40) return 'poor'
  return 'very-poor'
}

const getGradeType = (grade: string) => {
  const types: Record<string, string> = {
    'A+': 'success',
    'A': 'success',
    'B+': 'primary',
    'B': 'primary',
    'C+': 'warning',
    'C': 'warning',
    'D': 'danger'
  }
  return types[grade] || 'info'
}

const getRecommendationType = (rec: string) => {
  if (rec.includes('buy')) return 'success'
  if (rec.includes('sell')) return 'danger'
  return 'warning'
}

const getRecommendationText = (rec: string) => {
  const map: Record<string, string> = {
    'strong_buy': '强烈买入',
    'buy': '买入',
    'hold': '持有',
    'sell': '卖出',
    'strong_sell': '强烈卖出'
  }
  return map[rec] || rec
}

const getRiskText = (risk: string) => {
  const map: Record<string, string> = {
    'low': '低风险',
    'medium': '中风险',
    'high': '高风险'
  }
  return map[risk] || risk
}

const getImpactClass = (impact: string) => {
  return `impact-${impact}`
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleString()
}

// 生命周期
onMounted(() => {
  loadModels()
})
</script>

<style scoped lang="scss">
.ai-demo {
  padding: 20px;

  .demo-header {
    text-align: center;
    margin-bottom: 30px;

    h1 {
      color: #303133;
      margin-bottom: 10px;
    }

    p {
      color: #606266;
      font-size: 1.1em;
    }
  }

  .demo-nav {
    .recommendation-demo {
      .recommendation-controls {
        margin-bottom: 20px;
      }

      .recommendation-list {
        .recommendation-item {
          margin-bottom: 15px;
          padding: 15px;
          border: 1px solid #EBEEF5;
          border-radius: 4px;

          .rec-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .rec-title {
              font-weight: bold;
              color: #303133;
            }
          }

          .rec-description {
            color: #606266;
            margin-bottom: 10px;
          }

          .rec-metrics {
            display: flex;
            gap: 20px;
            margin-bottom: 10px;
            font-size: 0.9em;
            color: #909399;
          }

          .rec-actions {
            display: flex;
            justify-content: flex-end;
          }
        }
      }

      .sentiment-controls {
        margin-bottom: 20px;
      }

      .sentiment-display {
        text-align: center;

        .sentiment-overall {
          margin-bottom: 15px;

          .sentiment-score {
            font-size: 3em;
            font-weight: bold;
            margin-bottom: 5px;

            &.positive { color: #67C23A; }
            &.negative { color: #F56C6C; }
            &.neutral { color: #E6A23C; }
          }

          .sentiment-label {
            color: #606266;
          }
        }
      }
    }

    .monitoring-demo {
      .metric-item {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid #EBEEF5;

        label {
          color: #606266;
        }

        .metric-value {
          font-weight: bold;
          color: #303133;

          &.high-error { color: #F56C6C; }
          &.medium-error { color: #E6A23C; }
          &.low-error { color: #67C23A; }
        }
      }

      .models-list {
        .model-item {
          margin-bottom: 10px;
          padding: 10px;
          background: #F5F7FA;
          border-radius: 4px;

          .model-name {
            font-weight: bold;
            color: #303133;
          }

          .model-type {
            font-size: 0.9em;
            color: #606266;
            margin: 5px 0;
          }
        }
      }

      .no-models {
        text-align: center;
        padding: 20px;
        color: #909399;
      }

      .alerts-list {
        .alert-item {
          margin-bottom: 10px;
          padding: 10px;
          border-radius: 4px;

          &.high {
            background: #FEF0F0;
            border-left: 4px solid #F56C6C;
          }

          &.medium {
            background: #FDF6EC;
            border-left: 4px solid #E6A23C;
          }

          &.low {
            background: #F0F9FF;
            border-left: 4px solid #409EFF;
          }

          .alert-message {
            font-size: 0.9em;
            color: #303133;
            margin-bottom: 5px;
          }

          .alert-time {
            font-size: 0.8em;
            color: #909399;
          }
        }
      }

      .no-alerts {
        text-align: center;
        padding: 20px;
        color: #67C23A;

        .el-icon {
          margin-right: 5px;
        }
      }
    }

    .batch-demo {
      .batch-controls {
        margin-bottom: 20px;

        .symbol-input {
          margin-bottom: 15px;
        }

        .batch-actions {
          display: flex;
          gap: 10px;
        }
      }

      .batch-results {
        h4 {
          margin-bottom: 15px;
          color: #303133;
        }

        .excellent { color: #67C23A; }
        .good { color: #409EFF; }
        .average { color: #E6A23C; }
        .poor { color: #F56C6C; }
        .very-poor { color: #F56C6C; }
      }
    }
  }

  .analysis-details {
    .factors-section {
      margin-top: 20px;

      h4 {
        margin-bottom: 15px;
        color: #303133;
      }

      .factor-card {
        margin-bottom: 10px;

        .factor-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .factor-name {
            font-weight: bold;
            color: #303133;
          }

          .factor-value {
            font-weight: bold;

            &.impact-positive { color: #67C23A; }
            &.impact-negative { color: #F56C6C; }
            &.impact-neutral { color: #909399; }
          }
        }

        .factor-explanation {
          font-size: 0.9em;
          color: #606266;
        }
      }
    }
  }
}
</style> 