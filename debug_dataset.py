#!/usr/bin/env python3

import qlib
from qlib.contrib.data.handler import Alpha158
from qlib.data.dataset import DatasetH
import pandas as pd

def debug_dataset():
    """Debug the dataset structure"""
    print("=== Debug Dataset Structure ===")
    
    # Initialize Qlib
    qlib.init(mount_path="~/.qlib/qlib_data/cn_data", region="cn")
    
    # Create Alpha158 handler
    handler = Alpha158(
        instruments='csi300',
        start_time='2020-01-01',
        end_time='2020-12-31',
        fit_start_time='2020-01-01',
        fit_end_time='2020-06-30',
        freq='day'
    )
    
    # Create dataset
    dataset = DatasetH(
        handler=handler,
        segments={
            'train': ('2020-01-01', '2020-06-30'),
            'valid': ('2020-07-01', '2020-09-30'),
            'test': ('2020-10-01', '2020-12-31')
        }
    )
    
    # Get train data and examine structure
    print("Calling dataset.prepare('train')...")
    train_data = dataset.prepare('train')
    
    print(f"Type of train_data: {type(train_data)}")
    
    if hasattr(train_data, 'keys'):
        print(f"Keys in train_data: {list(train_data.keys())}")
    elif isinstance(train_data, tuple):
        print(f"train_data is a tuple with {len(train_data)} elements")
        for i, elem in enumerate(train_data):
            print(f"  Element {i}: type={type(elem)}, shape={getattr(elem, 'shape', 'no shape')}")
    elif isinstance(train_data, pd.DataFrame):
        print(f"train_data is a DataFrame with shape: {train_data.shape}")
        print(f"Columns: {train_data.columns.tolist()[:10]}...")  # Show first 10 columns
    else:
        print(f"train_data structure: {train_data}")
    
    # Try different access patterns
    print("\n=== Trying different access patterns ===")
    
    try:
        # Pattern 1: Direct dictionary access
        features = train_data['feature']
        print("✓ train_data['feature'] works")
        print(f"Features shape: {features.shape}")
    except Exception as e:
        print(f"✗ train_data['feature'] failed: {e}")
    
    try:
        # Pattern 2: Tuple unpacking
        if isinstance(train_data, tuple) and len(train_data) == 2:
            features, labels = train_data
            print("✓ Tuple unpacking (features, labels) works")
            print(f"Features shape: {features.shape}")
            print(f"Labels shape: {labels.shape}")
    except Exception as e:
        print(f"✗ Tuple unpacking failed: {e}")
    
    try:
        # Pattern 3: Check if it's a pandas DataFrame with MultiIndex
        if isinstance(train_data, pd.DataFrame):
            print("✓ train_data is a DataFrame")
            print(f"Index levels: {train_data.index.nlevels}")
            print(f"Columns: {train_data.columns.tolist()[:5]}...")
            
            # Try to separate features and labels
            label_cols = [col for col in train_data.columns if 'LABEL' in str(col)]
            feature_cols = [col for col in train_data.columns if 'LABEL' not in str(col)]
            
            print(f"Found {len(feature_cols)} feature columns")
            print(f"Found {len(label_cols)} label columns")
            
            if feature_cols:
                features = train_data[feature_cols]
                print(f"Features shape: {features.shape}")
            if label_cols:
                labels = train_data[label_cols]
                print(f"Labels shape: {labels.shape}")
                
    except Exception as e:
        print(f"✗ DataFrame analysis failed: {e}")

if __name__ == "__main__":
    debug_dataset()