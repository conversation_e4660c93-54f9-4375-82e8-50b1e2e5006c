#!/usr/bin/env python3
"""
MySQL连接测试脚本
用于验证Qlib项目MySQL改造的环境准备
"""

def test_mysql_basic_connection():
    """测试基础MySQL连接"""
    print("🔍 测试基础MySQL连接...")
    try:
        import pymysql
        
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='root',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"✅ MySQL连接成功，版本: {version[0]}")
            
            # 检查现有数据库
            cursor.execute("SHOW DATABASES")
            databases = [row[0] for row in cursor.fetchall()]
            print(f"📁 现有数据库: {databases}")
            
        connection.close()
        return True
        
    except ImportError:
        print("❌ PyMySQL未安装，请运行: pip install PyMySQL")
        return False
    except Exception as e:
        print(f"❌ MySQL连接失败: {e}")
        return False

def test_sqlalchemy_connection():
    """测试SQLAlchemy连接"""
    print("\n🔍 测试SQLAlchemy连接...")
    try:
        import sqlalchemy
        from sqlalchemy import create_engine, text
        
        print(f"📦 SQLAlchemy版本: {sqlalchemy.__version__}")
        
        # 测试连接到MySQL服务器
        engine = create_engine("mysql+pymysql://root:root@localhost:3306/")
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1 as test"))
            print("✅ SQLAlchemy基础连接成功")
            
        return True
        
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        return False
    except Exception as e:
        print(f"❌ SQLAlchemy连接失败: {e}")
        return False

def create_qlib_databases():
    """创建Qlib所需的数据库"""
    print("\n🏗️ 创建Qlib数据库...")
    try:
        import pymysql
        
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='root',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 创建MLflow数据库
            cursor.execute("CREATE DATABASE IF NOT EXISTS qlib_mlflow CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print("✅ 创建qlib_mlflow数据库")
            
            # 创建任务管理数据库
            cursor.execute("CREATE DATABASE IF NOT EXISTS qlib_tasks CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print("✅ 创建qlib_tasks数据库")
            
            # 验证数据库创建
            cursor.execute("SHOW DATABASES LIKE 'qlib_%'")
            qlib_dbs = [row[0] for row in cursor.fetchall()]
            print(f"📁 Qlib数据库: {qlib_dbs}")
            
        connection.commit()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库创建失败: {e}")
        return False

def test_mlflow_mysql_backend():
    """测试MLflow MySQL后端"""
    print("\n🧪 测试MLflow MySQL后端...")
    try:
        import mlflow
        import sqlalchemy
        from sqlalchemy import create_engine, text
        
        print(f"📦 MLflow版本: {mlflow.__version__}")
        
        # 测试连接到qlib_mlflow数据库
        mysql_uri = "mysql+pymysql://root:root@localhost:3306/qlib_mlflow"
        engine = create_engine(mysql_uri)
        
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1 as test"))
            print("✅ qlib_mlflow数据库连接成功")
            
        # 测试MLflow跟踪URI
        mlflow.set_tracking_uri(mysql_uri)
        print("✅ MLflow MySQL跟踪URI设置成功")
        
        return True
        
    except Exception as e:
        print(f"❌ MLflow MySQL后端测试失败: {e}")
        return False

def test_qlib_with_mysql():
    """测试Qlib与MySQL集成"""
    print("\n🎯 测试Qlib与MySQL集成...")
    try:
        import qlib
        from qlib.constant import REG_CN
        
        # 使用MySQL作为MLflow后端初始化Qlib
        qlib.init(
            provider_uri="~/.qlib/qlib_data/cn_data",
            region=REG_CN,
            exp_manager={
                "class": "MLflowExpManager",
                "module_path": "qlib.workflow.expm",
                "kwargs": {
                    "uri": "mysql+pymysql://root:root@localhost:3306/qlib_mlflow",
                    "default_exp_name": "MySQL_Test_Experiment",
                },
            }
        )
        
        print("✅ Qlib MySQL集成初始化成功")
        
        # 测试实验管理
        from qlib.workflow import R
        
        # 开始一个测试实验
        with R.start(experiment_name="MySQL_Connection_Test"):
            R.log_params(test_type="mysql_integration", status="success")
            R.log_metrics(connection_test=1.0)
            print("✅ 实验记录到MySQL成功")
            
        return True
        
    except Exception as e:
        print(f"❌ Qlib MySQL集成测试失败: {e}")
        return False

def main():
    """主测试流程"""
    print("🚀 Qlib MySQL改造环境测试")
    print("=" * 50)
    
    # 测试步骤
    tests = [
        ("基础MySQL连接", test_mysql_basic_connection),
        ("SQLAlchemy连接", test_sqlalchemy_connection), 
        ("创建Qlib数据库", create_qlib_databases),
        ("MLflow MySQL后端", test_mlflow_mysql_backend),
        ("Qlib MySQL集成", test_qlib_with_mysql),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    print("="*50)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    print(f"\n📈 总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！MySQL改造环境准备完成")
        print("\n📝 下一步:")
        print("1. 修改qlib/config.py中的默认exp_manager配置")
        print("2. 运行实际的模型训练验证MySQL存储")
        print("3. 开发数据迁移工具")
    else:
        print("⚠️ 部分测试失败，请检查环境配置")
        print("\n🔧 故障排除:")
        print("1. 确保MySQL服务运行: brew services start mysql")
        print("2. 安装Python依赖: pip install PyMySQL mysqlclient")
        print("3. 检查MySQL用户权限")

if __name__ == "__main__":
    main() 