#!/usr/bin/env python3
"""
Qlib MySQL集成完整示例
展示如何在实际工作流中使用MySQL进行实验管理和任务管理
"""
import qlib
from qlib.constant import REG_CN
from qlib.workflow import R
from qlib.workflow.task.mysql_manager import MySQLTaskManager
from qlib.contrib.model.linear import LinearModel
from qlib.data.dataset import DatasetH
from qlib.contrib.data.handler import Alpha158
import time

def setup_qlib_with_mysql():
    """使用MySQL初始化Qlib"""
    qlib.init(
        provider_uri="~/.qlib/qlib_data/cn_data",
        region=REG_CN,
        exp_manager={
            "class": "MLflowExpManager",
            "module_path": "qlib.workflow.expm",
            "kwargs": {
                "uri": "mysql+pymysql://root:root@localhost:3306/qlib_mlflow",
                "default_exp_name": "MySQL_Integrated_Workflow",
            },
        }
    )
    print("✅ Qlib MySQL集成初始化完成")

def create_workflow_tasks():
    """创建工作流任务"""
    print("\n📋 创建工作流任务...")
    
    # 初始化任务管理器
    task_manager = MySQLTaskManager("mysql+pymysql://root:root@localhost:3306/qlib_tasks")
    
    # 创建数据准备任务
    data_task_id = task_manager.create_task(
        name="数据准备和特征工程",
        task_type="data_preparation",
        config={
            "handler": "Alpha158",
            "start_time": "2008-01-01",
            "end_time": "2020-08-01",
            "instruments": "csi500"
        }
    )
    print(f"✅ 数据准备任务创建: {data_task_id}")
    
    # 创建模型训练任务
    model_task_id = task_manager.create_task(
        name="线性模型训练",
        task_type="model_training",
        config={
            "model": "LinearModel",
            "estimator": "ols",
            "train_period": "2008-2014",
            "valid_period": "2015-2016"
        },
        depends_on=[data_task_id]
    )
    print(f"✅ 模型训练任务创建: {model_task_id}")
    
    # 创建回测任务
    backtest_task_id = task_manager.create_task(
        name="策略回测分析",
        task_type="backtest",
        config={
            "strategy": "TopkDropoutStrategy",
            "topk": 50,
            "n_drop": 5,
            "test_period": "2017-2020"
        },
        depends_on=[model_task_id]
    )
    print(f"✅ 回测分析任务创建: {backtest_task_id}")
    
    return task_manager, data_task_id, model_task_id, backtest_task_id

def execute_data_preparation_task(task_manager, task_id):
    """执行数据准备任务"""
    print(f"\n🔄 执行数据准备任务: {task_id}")
    
    # 更新任务状态为运行中
    task_manager.update_task_status(task_id, "RUNNING")
    
    try:
        # 模拟数据准备过程
        print("   📊 加载Alpha158特征...")
        time.sleep(1)  # 模拟处理时间
        
        # 记录任务结果
        task_manager.add_task_result(
            task_id,
            "data_info",
            {
                "feature_count": 158,
                "stock_count": 3875,
                "date_range": "2008-01-01 to 2020-08-01",
                "missing_ratio": 0.05
            }
        )
        
        # 任务完成
        task_manager.update_task_status(task_id, "COMPLETED")
        print("   ✅ 数据准备任务完成")
        return True
        
    except Exception as e:
        task_manager.update_task_status(task_id, "FAILED", str(e))
        print(f"   ❌ 数据准备任务失败: {e}")
        return False

def execute_model_training_task(task_manager, task_id):
    """执行模型训练任务"""
    print(f"\n🤖 执行模型训练任务: {task_id}")
    
    # 更新任务状态为运行中
    task_manager.update_task_status(task_id, "RUNNING")
    
    try:
        # 使用MLflow记录实验
        with R.start(experiment_name="MySQL_Integrated_Linear_Model"):
            print("   🧠 训练线性模型...")
            
            # 记录参数到MLflow
            R.log_params(
                model="LinearModel",
                estimator="ols",
                train_period="2008-2014",
                valid_period="2015-2016"
            )
            
            # 模拟训练过程
            time.sleep(2)  # 模拟训练时间
            
            # 模拟训练结果
            ic = 0.025
            icir = 0.18
            annual_return = 0.035
            max_drawdown = 0.12
            
            # 记录指标到MLflow
            R.log_metrics(
                ic=ic,
                icir=icir,
                annual_return=annual_return,
                max_drawdown=max_drawdown
            )
            
            # 记录任务结果到任务管理器
            task_manager.add_task_result(
                task_id,
                "training_metrics",
                {
                    "ic": ic,
                    "icir": icir,
                    "annual_return": annual_return,
                    "max_drawdown": max_drawdown,
                    "training_time": 2.0
                }
            )
            
            task_manager.add_task_result(
                task_id,
                "model_params",
                {
                    "model_type": "LinearModel",
                    "feature_count": 158,
                    "regularization": None
                }
            )
        
        # 任务完成
        task_manager.update_task_status(task_id, "COMPLETED")
        print("   ✅ 模型训练任务完成")
        return True
        
    except Exception as e:
        task_manager.update_task_status(task_id, "FAILED", str(e))
        print(f"   ❌ 模型训练任务失败: {e}")
        return False

def execute_backtest_task(task_manager, task_id):
    """执行回测任务"""
    print(f"\n📈 执行回测分析任务: {task_id}")
    
    # 更新任务状态为运行中
    task_manager.update_task_status(task_id, "RUNNING")
    
    try:
        # 使用MLflow记录回测实验
        with R.start(experiment_name="MySQL_Integrated_Backtest"):
            print("   📊 执行策略回测...")
            
            # 记录回测参数
            R.log_params(
                strategy="TopkDropoutStrategy",
                topk=50,
                n_drop=5,
                test_period="2017-2020",
                initial_capital=100000000
            )
            
            # 模拟回测过程
            time.sleep(1.5)  # 模拟回测时间
            
            # 模拟回测结果
            excess_return = 0.023
            sharpe_ratio = 0.33
            max_drawdown = -0.101
            annual_volatility = 0.045
            
            # 记录回测指标到MLflow
            R.log_metrics(
                excess_return=excess_return,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                annual_volatility=annual_volatility
            )
            
            # 记录任务结果到任务管理器
            task_manager.add_task_result(
                task_id,
                "backtest_results",
                {
                    "excess_return": excess_return,
                    "sharpe_ratio": sharpe_ratio,
                    "max_drawdown": max_drawdown,
                    "annual_volatility": annual_volatility,
                    "backtest_period": "2017-2020"
                }
            )
        
        # 任务完成
        task_manager.update_task_status(task_id, "COMPLETED")
        print("   ✅ 回测分析任务完成")
        return True
        
    except Exception as e:
        task_manager.update_task_status(task_id, "FAILED", str(e))
        print(f"   ❌ 回测分析任务失败: {e}")
        return False

def display_workflow_summary(task_manager):
    """显示工作流总结"""
    print("\n📊 工作流执行总结")
    print("=" * 50)
    
    # 获取任务统计
    stats = task_manager.get_task_statistics()
    print(f"总任务数: {stats.get('total_tasks', 0)}")
    print(f"总结果数: {stats.get('total_results', 0)}")
    print(f"状态分布: {stats.get('status_distribution', {})}")
    print(f"类型分布: {stats.get('type_distribution', {})}")
    
    # 显示最近的任务
    recent_tasks = task_manager.list_tasks(limit=5)
    print(f"\n最近任务:")
    for task in recent_tasks:
        print(f"  - {task['name']}: {task['status']} ({task['task_type']})")

def main():
    """主函数"""
    print("🚀 Qlib MySQL集成完整工作流示例")
    print("=" * 60)
    
    # 1. 初始化Qlib with MySQL
    setup_qlib_with_mysql()
    
    # 2. 创建工作流任务
    task_manager, data_task_id, model_task_id, backtest_task_id = create_workflow_tasks()
    
    # 3. 按依赖顺序执行任务
    print("\n🔄 开始执行工作流...")
    
    # 执行数据准备任务
    if not execute_data_preparation_task(task_manager, data_task_id):
        print("❌ 工作流失败：数据准备任务失败")
        return
    
    # 执行模型训练任务
    if not execute_model_training_task(task_manager, model_task_id):
        print("❌ 工作流失败：模型训练任务失败")
        return
    
    # 执行回测任务
    if not execute_backtest_task(task_manager, backtest_task_id):
        print("❌ 工作流失败：回测分析任务失败")
        return
    
    # 4. 显示工作流总结
    display_workflow_summary(task_manager)
    
    print("\n🎉 完整工作流执行成功！")
    print("💾 所有实验数据已保存到MySQL数据库")
    print("📋 所有任务状态已记录到MySQL任务管理系统")

if __name__ == "__main__":
    main() 