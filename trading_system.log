2025-08-07 01:01:34,480 - __main__ - INFO - 开始运行综合演示系统
2025-08-07 01:01:34,480 - __main__ - INFO - Starting demo_strategy_framework
2025-08-07 01:01:34,480 - __main__ - INFO - === 策略框架演示 ===
2025-08-07 01:01:34,480 - __main__ - INFO - 策略创建完成
2025-08-07 01:01:34,486 - MomentumStrategy - INFO - Generated 30 momentum signals
2025-08-07 01:01:34,486 - __main__ - INFO - momentum策略生成了30个信号
2025-08-07 01:01:34,486 - __main__ - INFO - Starting update_performance
2025-08-07 01:01:34,486 - __main__ - INFO - Completed update_performance in 0.00 seconds
2025-08-07 01:01:34,491 - MeanReversionStrategy - INFO - Generated 6 mean reversion signals
2025-08-07 01:01:34,491 - __main__ - INFO - mean_reversion策略生成了6个信号
2025-08-07 01:01:34,491 - __main__ - INFO - Starting update_performance
2025-08-07 01:01:34,492 - __main__ - INFO - Completed update_performance in 0.00 seconds
2025-08-07 01:01:34,492 - __main__ - INFO - Completed demo_strategy_framework in 0.01 seconds
2025-08-07 01:01:34,492 - __main__ - INFO - Starting demo_alpha158_model
2025-08-07 01:01:34,492 - __main__ - INFO - === Alpha158多因子模型演示 ===
2025-08-07 01:01:34,492 - __main__ - INFO - Starting prepare_data
2025-08-07 01:02:08,253 - Alpha158MultiFactorModel - INFO - Alpha158数据准备完成
2025-08-07 01:02:08,253 - __main__ - INFO - Completed prepare_data in 33.76 seconds
2025-08-07 01:02:08,253 - __main__ - INFO - Starting train_model
2025-08-07 01:02:08,272 - Alpha158MultiFactorModel - ERROR - 模型训练失败: Empty data from dataset, please check your dataset config.
2025-08-07 01:02:08,272 - __main__ - ERROR - Failed train_model after 0.02 seconds: Empty data from dataset, please check your dataset config.
2025-08-07 01:02:08,272 - __main__ - ERROR - Alpha158模型演示失败: Empty data from dataset, please check your dataset config.
2025-08-07 01:02:08,272 - __main__ - INFO - Completed demo_alpha158_model in 33.78 seconds
2025-08-07 01:02:08,272 - __main__ - INFO - Starting demo_lstm_model
2025-08-07 01:02:08,272 - __main__ - INFO - === LSTM模型演示 ===
2025-08-07 01:02:10,290 - __main__ - INFO - Starting train
2025-08-07 01:02:10,291 - LSTMTrainingSystem - INFO - 开始训练LSTM模型，共5个epoch
2025-08-07 01:02:10,291 - __main__ - INFO - Starting train_epoch
2025-08-07 01:02:11,489 - __main__ - INFO - Completed train_epoch in 1.20 seconds
2025-08-07 01:02:11,514 - LSTMTrainingSystem - INFO - Epoch 1/5: Train Loss: 0.948105, Val Loss: 1.118388
2025-08-07 01:02:11,518 - __main__ - INFO - Starting train_epoch
2025-08-07 01:02:11,823 - __main__ - INFO - Completed train_epoch in 0.31 seconds
2025-08-07 01:02:11,841 - LSTMTrainingSystem - INFO - Epoch 2/5: Train Loss: 0.929299, Val Loss: 1.115376
2025-08-07 01:02:11,842 - __main__ - INFO - Starting train_epoch
2025-08-07 01:02:12,110 - __main__ - INFO - Completed train_epoch in 0.27 seconds
2025-08-07 01:02:12,128 - LSTMTrainingSystem - INFO - Epoch 3/5: Train Loss: 0.928349, Val Loss: 1.114182
2025-08-07 01:02:12,129 - __main__ - INFO - Starting train_epoch
2025-08-07 01:02:12,409 - __main__ - INFO - Completed train_epoch in 0.28 seconds
2025-08-07 01:02:12,427 - LSTMTrainingSystem - INFO - Epoch 4/5: Train Loss: 0.948476, Val Loss: 1.134081
2025-08-07 01:02:12,427 - __main__ - INFO - Starting train_epoch
2025-08-07 01:02:12,680 - __main__ - INFO - Completed train_epoch in 0.25 seconds
2025-08-07 01:02:12,697 - LSTMTrainingSystem - INFO - Epoch 5/5: Train Loss: 0.920529, Val Loss: 1.138952
2025-08-07 01:02:12,701 - LSTMTrainingSystem - INFO - LSTM训练完成
2025-08-07 01:02:12,701 - __main__ - INFO - Completed train in 2.41 seconds
2025-08-07 01:02:12,701 - __main__ - INFO - LSTM训练完成，最佳验证损失: 1.114182
2025-08-07 01:02:12,701 - __main__ - INFO - Completed demo_lstm_model in 4.43 seconds
2025-08-07 01:02:12,701 - __main__ - INFO - === 错误处理演示 ===
2025-08-07 01:02:12,702 - __main__ - ERROR - Error in generate_signals: 输入数据为空
2025-08-07 01:02:12,702 - __main__ - ERROR - Traceback: Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/qlib/demo/chap11/best_practices_advanced_demo.py", line 71, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/qlib/demo/chap11/best_practices_advanced_demo.py", line 163, in generate_signals
    raise DataError("输入数据为空")
DataError: 输入数据为空

2025-08-07 01:02:12,702 - __main__ - INFO - 成功捕获数据错误: 输入数据为空
2025-08-07 01:02:12,702 - __main__ - INFO - 成功捕获策略错误: 未知策略类型: invalid_strategy
2025-08-07 01:02:12,702 - __main__ - INFO - 成功捕获风险错误: 演示风险管理错误
2025-08-07 01:02:12,702 - __main__ - INFO - === 生成综合报告 ===
2025-08-07 01:02:12,702 - __main__ - INFO - 综合报告生成完成
2025-08-07 01:02:12,702 - __main__ - INFO - === 演示完成 ===
2025-08-07 01:02:12,702 - __main__ - INFO - 测试了2个策略
2025-08-07 01:02:12,703 - __main__ - INFO - 训练了1个模型
2025-08-09 23:09:45,361 - __main__ - INFO - 开始运行综合演示系统
2025-08-09 23:09:45,361 - __main__ - INFO - Starting demo_strategy_framework
2025-08-09 23:09:45,361 - __main__ - INFO - === 策略框架演示 ===
2025-08-09 23:09:45,361 - __main__ - INFO - 策略创建完成
2025-08-09 23:09:45,368 - MomentumStrategy - INFO - Generated 30 momentum signals
2025-08-09 23:09:45,368 - __main__ - INFO - momentum策略生成了30个信号
2025-08-09 23:09:45,368 - __main__ - INFO - Starting update_performance
2025-08-09 23:09:45,369 - __main__ - INFO - Completed update_performance in 0.00 seconds
2025-08-09 23:09:45,374 - MeanReversionStrategy - INFO - Generated 6 mean reversion signals
2025-08-09 23:09:45,374 - __main__ - INFO - mean_reversion策略生成了6个信号
2025-08-09 23:09:45,374 - __main__ - INFO - Starting update_performance
2025-08-09 23:09:45,374 - __main__ - INFO - Completed update_performance in 0.00 seconds
2025-08-09 23:09:45,374 - __main__ - INFO - Completed demo_strategy_framework in 0.01 seconds
2025-08-09 23:09:45,374 - __main__ - INFO - Starting demo_alpha158_model
2025-08-09 23:09:45,374 - __main__ - INFO - === Alpha158多因子模型演示 ===
2025-08-09 23:09:45,374 - __main__ - INFO - Starting prepare_data
2025-08-09 23:10:17,333 - Alpha158MultiFactorModel - INFO - Alpha158数据准备完成
2025-08-09 23:10:17,334 - __main__ - INFO - Completed prepare_data in 31.96 seconds
2025-08-09 23:10:17,334 - __main__ - INFO - Starting train_model
2025-08-09 23:10:17,352 - Alpha158MultiFactorModel - ERROR - 模型训练失败: Empty data from dataset, please check your dataset config.
2025-08-09 23:10:17,352 - __main__ - ERROR - Failed train_model after 0.02 seconds: Empty data from dataset, please check your dataset config.
2025-08-09 23:10:17,352 - __main__ - ERROR - Alpha158模型演示失败: Empty data from dataset, please check your dataset config.
2025-08-09 23:10:17,353 - __main__ - INFO - Completed demo_alpha158_model in 31.98 seconds
2025-08-09 23:10:17,353 - __main__ - INFO - Starting demo_lstm_model
2025-08-09 23:10:17,353 - __main__ - INFO - === LSTM模型演示 ===
2025-08-09 23:10:18,768 - __main__ - INFO - Starting train
2025-08-09 23:10:18,768 - LSTMTrainingSystem - INFO - 开始训练LSTM模型，共5个epoch
2025-08-09 23:10:18,768 - __main__ - INFO - Starting train_epoch
2025-08-09 23:10:19,231 - __main__ - INFO - Completed train_epoch in 0.46 seconds
2025-08-09 23:10:19,262 - LSTMTrainingSystem - INFO - Epoch 1/5: Train Loss: 0.924090, Val Loss: 1.115192
2025-08-09 23:10:19,266 - __main__ - INFO - Starting train_epoch
2025-08-09 23:10:19,571 - __main__ - INFO - Completed train_epoch in 0.31 seconds
2025-08-09 23:10:19,593 - LSTMTrainingSystem - INFO - Epoch 2/5: Train Loss: 0.930992, Val Loss: 1.114575
2025-08-09 23:10:19,595 - __main__ - INFO - Starting train_epoch
2025-08-09 23:10:19,877 - __main__ - INFO - Completed train_epoch in 0.28 seconds
2025-08-09 23:10:19,900 - LSTMTrainingSystem - INFO - Epoch 3/5: Train Loss: 0.946324, Val Loss: 1.111611
2025-08-09 23:10:19,901 - __main__ - INFO - Starting train_epoch
2025-08-09 23:10:20,232 - __main__ - INFO - Completed train_epoch in 0.33 seconds
2025-08-09 23:10:20,253 - LSTMTrainingSystem - INFO - Epoch 4/5: Train Loss: 0.924140, Val Loss: 1.111355
2025-08-09 23:10:20,255 - __main__ - INFO - Starting train_epoch
2025-08-09 23:10:20,576 - __main__ - INFO - Completed train_epoch in 0.32 seconds
2025-08-09 23:10:20,601 - LSTMTrainingSystem - INFO - Epoch 5/5: Train Loss: 0.910970, Val Loss: 1.137718
2025-08-09 23:10:20,605 - LSTMTrainingSystem - INFO - LSTM训练完成
2025-08-09 23:10:20,605 - __main__ - INFO - Completed train in 1.84 seconds
2025-08-09 23:10:20,605 - __main__ - INFO - LSTM训练完成，最佳验证损失: 1.111355
2025-08-09 23:10:20,605 - __main__ - INFO - Completed demo_lstm_model in 3.25 seconds
2025-08-09 23:10:20,605 - __main__ - INFO - === 错误处理演示 ===
2025-08-09 23:10:20,606 - __main__ - ERROR - Error in generate_signals: 输入数据为空
2025-08-09 23:10:20,607 - __main__ - ERROR - Traceback: Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/qlib/demo/chap11/best_practices_advanced_demo.py", line 71, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/qlib/demo/chap11/best_practices_advanced_demo.py", line 163, in generate_signals
    raise DataError("输入数据为空")
DataError: 输入数据为空

2025-08-09 23:10:20,607 - __main__ - INFO - 成功捕获数据错误: 输入数据为空
2025-08-09 23:10:20,607 - __main__ - INFO - 成功捕获策略错误: 未知策略类型: invalid_strategy
2025-08-09 23:10:20,607 - __main__ - INFO - 成功捕获风险错误: 演示风险管理错误
2025-08-09 23:10:20,607 - __main__ - INFO - === 生成综合报告 ===
2025-08-09 23:10:20,607 - __main__ - INFO - 综合报告生成完成
2025-08-09 23:10:20,607 - __main__ - INFO - === 演示完成 ===
2025-08-09 23:10:20,607 - __main__ - INFO - 测试了2个策略
2025-08-09 23:10:20,607 - __main__ - INFO - 训练了1个模型
2025-08-09 23:12:07,809 - __main__ - INFO - 开始运行综合演示系统
2025-08-09 23:12:07,810 - __main__ - INFO - Starting demo_strategy_framework
2025-08-09 23:12:07,810 - __main__ - INFO - === 策略框架演示 ===
2025-08-09 23:12:07,810 - __main__ - INFO - 策略创建完成
2025-08-09 23:12:07,815 - MomentumStrategy - INFO - Generated 30 momentum signals
2025-08-09 23:12:07,815 - __main__ - INFO - momentum策略生成了30个信号
2025-08-09 23:12:07,815 - __main__ - INFO - Starting update_performance
2025-08-09 23:12:07,815 - __main__ - INFO - Completed update_performance in 0.00 seconds
2025-08-09 23:12:07,820 - MeanReversionStrategy - INFO - Generated 6 mean reversion signals
2025-08-09 23:12:07,820 - __main__ - INFO - mean_reversion策略生成了6个信号
2025-08-09 23:12:07,821 - __main__ - INFO - Starting update_performance
2025-08-09 23:12:07,821 - __main__ - INFO - Completed update_performance in 0.00 seconds
2025-08-09 23:12:07,821 - __main__ - INFO - Completed demo_strategy_framework in 0.01 seconds
2025-08-09 23:12:07,821 - __main__ - INFO - Starting demo_alpha158_model
2025-08-09 23:12:07,821 - __main__ - INFO - === Alpha158多因子模型演示 ===
2025-08-09 23:12:07,821 - __main__ - INFO - Starting prepare_data
2025-08-09 23:12:39,683 - Alpha158MultiFactorModel - INFO - Alpha158数据准备完成
2025-08-09 23:12:39,683 - __main__ - INFO - Completed prepare_data in 31.86 seconds
2025-08-09 23:12:39,683 - __main__ - INFO - Starting train_model
2025-08-09 23:12:39,701 - Alpha158MultiFactorModel - ERROR - 模型训练失败: Empty data from dataset, please check your dataset config.
2025-08-09 23:12:39,702 - __main__ - ERROR - Failed train_model after 0.02 seconds: Empty data from dataset, please check your dataset config.
2025-08-09 23:12:39,702 - __main__ - ERROR - Alpha158模型演示失败: Empty data from dataset, please check your dataset config.
2025-08-09 23:12:39,702 - __main__ - INFO - Completed demo_alpha158_model in 31.88 seconds
2025-08-09 23:12:39,702 - __main__ - INFO - Starting demo_lstm_model
2025-08-09 23:12:39,702 - __main__ - INFO - === LSTM模型演示 ===
2025-08-09 23:12:41,476 - __main__ - INFO - Starting train
2025-08-09 23:12:41,476 - LSTMTrainingSystem - INFO - 开始训练LSTM模型，共5个epoch
2025-08-09 23:12:41,476 - __main__ - INFO - Starting train_epoch
2025-08-09 23:12:42,562 - __main__ - INFO - Completed train_epoch in 1.09 seconds
2025-08-09 23:12:42,589 - LSTMTrainingSystem - INFO - Epoch 1/5: Train Loss: 0.942494, Val Loss: 1.111725
2025-08-09 23:12:42,591 - __main__ - INFO - Starting train_epoch
2025-08-09 23:12:42,909 - __main__ - INFO - Completed train_epoch in 0.32 seconds
2025-08-09 23:12:42,933 - LSTMTrainingSystem - INFO - Epoch 2/5: Train Loss: 0.929323, Val Loss: 1.110899
2025-08-09 23:12:42,934 - __main__ - INFO - Starting train_epoch
2025-08-09 23:12:43,250 - __main__ - INFO - Completed train_epoch in 0.32 seconds
2025-08-09 23:12:43,272 - LSTMTrainingSystem - INFO - Epoch 3/5: Train Loss: 0.934143, Val Loss: 1.114409
2025-08-09 23:12:43,272 - __main__ - INFO - Starting train_epoch
2025-08-09 23:12:43,592 - __main__ - INFO - Completed train_epoch in 0.32 seconds
2025-08-09 23:12:43,616 - LSTMTrainingSystem - INFO - Epoch 4/5: Train Loss: 0.914819, Val Loss: 1.116471
2025-08-09 23:12:43,616 - __main__ - INFO - Starting train_epoch
2025-08-09 23:12:43,881 - __main__ - INFO - Completed train_epoch in 0.27 seconds
2025-08-09 23:12:43,899 - LSTMTrainingSystem - INFO - Epoch 5/5: Train Loss: 0.913319, Val Loss: 1.112748
2025-08-09 23:12:43,900 - LSTMTrainingSystem - INFO - Early stopping at epoch 5
2025-08-09 23:12:43,901 - LSTMTrainingSystem - INFO - LSTM训练完成
2025-08-09 23:12:43,901 - __main__ - INFO - Completed train in 2.42 seconds
2025-08-09 23:12:43,901 - __main__ - INFO - LSTM训练完成，最佳验证损失: 1.110899
2025-08-09 23:12:43,901 - __main__ - INFO - Completed demo_lstm_model in 4.20 seconds
2025-08-09 23:12:43,901 - __main__ - INFO - === 错误处理演示 ===
2025-08-09 23:12:43,902 - __main__ - ERROR - Error in generate_signals: 输入数据为空
2025-08-09 23:12:43,902 - __main__ - ERROR - Traceback: Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/qlib/demo/chap11/best_practices_advanced_demo.py", line 71, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/qlib/demo/chap11/best_practices_advanced_demo.py", line 163, in generate_signals
    raise DataError("输入数据为空")
DataError: 输入数据为空

2025-08-09 23:12:43,902 - __main__ - INFO - 成功捕获数据错误: 输入数据为空
2025-08-09 23:12:43,902 - __main__ - INFO - 成功捕获策略错误: 未知策略类型: invalid_strategy
2025-08-09 23:12:43,902 - __main__ - INFO - 成功捕获风险错误: 演示风险管理错误
2025-08-09 23:12:43,902 - __main__ - INFO - === 生成综合报告 ===
2025-08-09 23:12:43,903 - __main__ - INFO - 综合报告生成完成
2025-08-09 23:12:43,903 - __main__ - INFO - === 演示完成 ===
2025-08-09 23:12:43,903 - __main__ - INFO - 测试了2个策略
2025-08-09 23:12:43,903 - __main__ - INFO - 训练了1个模型
2025-08-09 23:14:06,248 - __main__ - INFO - 开始运行综合演示系统
2025-08-09 23:14:06,248 - __main__ - INFO - Starting demo_strategy_framework
2025-08-09 23:14:06,249 - __main__ - INFO - === 策略框架演示 ===
2025-08-09 23:14:06,249 - __main__ - INFO - 策略创建完成
2025-08-09 23:14:06,255 - MomentumStrategy - INFO - Generated 30 momentum signals
2025-08-09 23:14:06,255 - __main__ - INFO - momentum策略生成了30个信号
2025-08-09 23:14:06,255 - __main__ - INFO - Starting update_performance
2025-08-09 23:14:06,255 - __main__ - INFO - Completed update_performance in 0.00 seconds
2025-08-09 23:14:06,260 - MeanReversionStrategy - INFO - Generated 6 mean reversion signals
2025-08-09 23:14:06,260 - __main__ - INFO - mean_reversion策略生成了6个信号
2025-08-09 23:14:06,261 - __main__ - INFO - Starting update_performance
2025-08-09 23:14:06,261 - __main__ - INFO - Completed update_performance in 0.00 seconds
2025-08-09 23:14:06,261 - __main__ - INFO - Completed demo_strategy_framework in 0.01 seconds
2025-08-09 23:14:06,261 - __main__ - INFO - Starting demo_alpha158_model
2025-08-09 23:14:06,261 - __main__ - INFO - === Alpha158多因子模型演示 ===
2025-08-09 23:14:06,261 - __main__ - INFO - Starting prepare_data
2025-08-09 23:14:37,528 - Alpha158MultiFactorModel - INFO - Alpha158数据准备完成
2025-08-09 23:14:37,529 - __main__ - INFO - Completed prepare_data in 31.27 seconds
2025-08-09 23:14:37,529 - __main__ - INFO - Starting train_model
2025-08-09 23:14:37,546 - Alpha158MultiFactorModel - ERROR - 模型训练失败: Empty data from dataset, please check your dataset config.
2025-08-09 23:14:37,546 - __main__ - ERROR - Failed train_model after 0.02 seconds: Empty data from dataset, please check your dataset config.
2025-08-09 23:14:37,546 - __main__ - ERROR - Alpha158模型演示失败: Empty data from dataset, please check your dataset config.
2025-08-09 23:14:37,546 - __main__ - INFO - Completed demo_alpha158_model in 31.29 seconds
2025-08-09 23:14:37,546 - __main__ - INFO - Starting demo_lstm_model
2025-08-09 23:14:37,547 - __main__ - INFO - === LSTM模型演示 ===
2025-08-09 23:14:38,823 - __main__ - INFO - Starting train
2025-08-09 23:14:38,823 - LSTMTrainingSystem - INFO - 开始训练LSTM模型，共5个epoch
2025-08-09 23:14:38,823 - __main__ - INFO - Starting train_epoch
2025-08-09 23:14:39,189 - __main__ - INFO - Completed train_epoch in 0.37 seconds
2025-08-09 23:14:39,216 - LSTMTrainingSystem - INFO - Epoch 1/5: Train Loss: 0.930720, Val Loss: 1.121176
2025-08-09 23:14:39,218 - __main__ - INFO - Starting train_epoch
2025-08-09 23:14:39,518 - __main__ - INFO - Completed train_epoch in 0.30 seconds
2025-08-09 23:14:39,538 - LSTMTrainingSystem - INFO - Epoch 2/5: Train Loss: 0.930546, Val Loss: 1.116274
2025-08-09 23:14:39,539 - __main__ - INFO - Starting train_epoch
2025-08-09 23:14:39,852 - __main__ - INFO - Completed train_epoch in 0.31 seconds
2025-08-09 23:14:39,875 - LSTMTrainingSystem - INFO - Epoch 3/5: Train Loss: 0.933061, Val Loss: 1.124025
2025-08-09 23:14:39,875 - __main__ - INFO - Starting train_epoch
2025-08-09 23:14:40,145 - __main__ - INFO - Completed train_epoch in 0.27 seconds
2025-08-09 23:14:40,166 - LSTMTrainingSystem - INFO - Epoch 4/5: Train Loss: 0.918404, Val Loss: 1.105663
2025-08-09 23:14:40,168 - __main__ - INFO - Starting train_epoch
2025-08-09 23:14:40,467 - __main__ - INFO - Completed train_epoch in 0.30 seconds
2025-08-09 23:14:40,505 - LSTMTrainingSystem - INFO - Epoch 5/5: Train Loss: 0.906030, Val Loss: 1.109988
2025-08-09 23:14:40,507 - LSTMTrainingSystem - INFO - LSTM训练完成
2025-08-09 23:14:40,507 - __main__ - INFO - Completed train in 1.68 seconds
2025-08-09 23:14:40,507 - __main__ - INFO - LSTM训练完成，最佳验证损失: 1.105663
2025-08-09 23:14:40,507 - __main__ - INFO - Completed demo_lstm_model in 2.96 seconds
2025-08-09 23:14:40,507 - __main__ - INFO - === 错误处理演示 ===
2025-08-09 23:14:40,508 - __main__ - ERROR - Error in generate_signals: 输入数据为空
2025-08-09 23:14:40,508 - __main__ - ERROR - Traceback: Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/qlib/demo/chap11/best_practices_advanced_demo.py", line 71, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/PycharmProjects/qlib/demo/chap11/best_practices_advanced_demo.py", line 163, in generate_signals
    raise DataError("输入数据为空")
DataError: 输入数据为空

2025-08-09 23:14:40,508 - __main__ - INFO - 成功捕获数据错误: 输入数据为空
2025-08-09 23:14:40,508 - __main__ - INFO - 成功捕获策略错误: 未知策略类型: invalid_strategy
2025-08-09 23:14:40,508 - __main__ - INFO - 成功捕获风险错误: 演示风险管理错误
2025-08-09 23:14:40,508 - __main__ - INFO - === 生成综合报告 ===
2025-08-09 23:14:40,509 - __main__ - INFO - 综合报告生成完成
2025-08-09 23:14:40,509 - __main__ - INFO - === 演示完成 ===
2025-08-09 23:14:40,509 - __main__ - INFO - 测试了2个策略
2025-08-09 23:14:40,509 - __main__ - INFO - 训练了1个模型
